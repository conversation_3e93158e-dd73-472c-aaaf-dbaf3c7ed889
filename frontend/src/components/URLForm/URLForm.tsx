import React, { useState } from "react";
import { Search } from "lucide-react";
import { AnalysisRequest } from "../../types";
import styles from "./URLForm.module.scss";
import classNames from "classnames/bind";

const cx = classNames.bind(styles);

interface URLFormProps {
  onSubmit: (request: AnalysisRequest) => void;
  isLoading: boolean;
  onTestSubmit?: () => void; // 테스트 버튼 클릭 핸들러 prop 추가
  onTestBadSubmit?: () => void; // 문제 많은 케이스 버튼 prop 추가
}

const URLForm: React.FC<URLFormProps> = ({
  onSubmit,
  isLoading,
  onTestSubmit,
  onTestBadSubmit,
}) => {
  const [url, setUrl] = useState("");

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (!url.trim()) {
      alert("URL을 입력해주세요.");
      return;
    }

    // URL 형식 자동 보정 및 검증
    let formattedUrl = url.trim();
    if (!/^https?:\/\//.test(formattedUrl)) {
      // localhost와 127.0.0.1은 http로, 나머지는 https로
      if (
        formattedUrl.startsWith("localhost") ||
        formattedUrl.startsWith("127.0.0.1")
      ) {
        formattedUrl = "http://" + formattedUrl;
      } else {
        formattedUrl = "https://" + formattedUrl;
      }
    }

    // 더 유연한 URL 패턴 (도메인, localhost, IP 주소 허용)
    const urlPattern =
      /^https?:\/\/([\w.-]+(\.[a-z]{2,})|localhost|127\.0\.0\.1)(:[0-9]+)?(\/.*)?$/i;
    if (!urlPattern.test(formattedUrl)) {
      alert(
        "올바른 URL 형식을 입력해주세요.\n예시: google.com, localhost:4000, https://naver.com"
      );
      return;
    }

    onSubmit({ url: formattedUrl, ai_provider: "gemini" });
  };

  return (
    <form className={cx("form")} onSubmit={handleSubmit}>
      <div className={cx("input-group")}>
        <div className={cx("url-input-wrapper")}>
          <Search className={cx("search-icon")} />
          <input
            type="text"
            value={url}
            onChange={(e) => setUrl(e.target.value)}
            placeholder="분석할 웹사이트 URL을 입력하세요 (예: https://example.com)"
            className={cx("url-input")}
            disabled={isLoading}
          />
        </div>
      </div>

      <button
        type="submit"
        className={cx("submit-button")}
        disabled={isLoading || !url.trim()}
      >
        {isLoading ? (
          <>
            <div className={cx("spinner")}></div>
            분석 중...
          </>
        ) : (
          <>
            <Search size={20} />
            SEO 분석 시작
          </>
        )}
      </button>

      {/* 테스트 버튼 추가 - 개발 환경에서만 표시 */}
      {process.env.NODE_ENV === "development" && (
        <>
          <button
            type="button"
            className={cx("submit-button")}
            style={{ marginTop: 12, background: "#4fd1c5" }}
            onClick={onTestSubmit}
            disabled={isLoading}
          >
            🧪 테스트(풀케이스) 결과 보기
          </button>

          <button
            type="button"
            className={cx("submit-button")}
            style={{ marginTop: 12, background: "#f56565" }}
            onClick={onTestBadSubmit}
            disabled={isLoading}
          >
            🚨 문제 많은 케이스 결과 보기
          </button>
        </>
      )}
    </form>
  );
};

export default URLForm;
