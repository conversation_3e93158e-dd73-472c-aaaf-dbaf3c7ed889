# Python 가상환경
venv/
env/
ENV/
.venv/
.env/

# Python 캐시
__pycache__/
*.py[cod]
*$py.class
*.so

# 환경 변수 파일
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
.env.production

# IDE 설정 파일
.vscode/
.idea/
*.swp
*.swo
*~

# 운영체제 파일
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# 로그 파일
*.log
logs/

# 데이터베이스
*.db
*.sqlite3

# 테스트 커버리지
htmlcov/
.coverage
.coverage.*
coverage.xml
*.cover
.hypothesis/
.pytest_cache/

# 빌드 디렉토리
build/
dist/
*.egg-info/

# 임시 파일
*.tmp
*.temp
temp/

# 백업 파일
*.bak
*.backup

# Chrome 드라이버 (자동 다운로드됨)
chromedriver*

# 사용자 업로드 파일
uploads/
static/uploads/

# 개발 중 생성되는 임시 파일들
test_*.py
debug_*.py
scratch.* 

/frontend/node_modules
/frontend/.env.production
.vercel

# Playwright
test-results/
playwright-report/
playwright/.cache/
*.trace

/node_modules