export interface CheckInfo {
  id: number;
  title: string;
  importance: number;
  criteria: string;
  status: "pass" | "fail" | "warning" | "pending" | "optional_skip";
  message: string;
  score: number;
}

export interface SectionResult {
  section_id: string;
  section_name: string;
  checks: CheckInfo[];
  total_checks: number;
  passed_checks: number;
  section_score: number;
  ai_suggestions?: {
    id: number;
    title?: string;
    suggestion: string;
    expected_impact?: string;
  }[];
}

export interface CategoryResult {
  category_id: string;
  category_name: string;
  category_score: number;
  sections: SectionResult[];
}

export interface Recommendation {
  priority: string;
  title: string;
  description: string;
  category: string;
  expected_impact: string;
}

export interface AIInsights {
  overall_summary: string;
  seo_level: string;
  strengths: string[];
  priority_recommendations: Recommendation[];
  technical_insights: string[];
  content_insights: string[];
  expected_score_improvement: string;
  implementation_timeline: string;
}

export interface SEOAnalysisResult {
  url: string;
  overall_score: number;
  categories: CategoryResult[];
  ai_analysis: AIInsights;
  recommendations: Recommendation[];
}

export interface AnalysisRequest {
  url: string;
  ai_provider?: "gemini";
}

// (삭제됨) E-E-A-T 타입 정의는 더 이상 사용하지 않습니다.
