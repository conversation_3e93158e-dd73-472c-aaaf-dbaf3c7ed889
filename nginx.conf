upstream backend {
    server seo-backend:8000;  
}

server {
    listen 80;
    server_name **************;

    location / {
        proxy_pass http://backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # CORS 설정
        add_header Access-Control-Allow-Origin * always;
        add_header Access-Control-Allow-Methods "GET, POST, OPTIONS" always;
        add_header Access-Control-Allow-Headers "DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization" always;
        add_header Access-Control-Max-Age 86400 always;
        
        # OPTIONS 요청 처리 - FastAPI로 전달
        # Nginx에서 직접 처리하지 않고 백엔드로 전달
    }

    # 정적 파일 설정 (필요시)
    location /static/ {
        alias /home/<USER>/seo/static/;
    }
} 