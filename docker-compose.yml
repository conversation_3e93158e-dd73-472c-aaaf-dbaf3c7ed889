version: "3.8"

services:
  seo-backend:
    build: .
    ports:
      - "8000:8000"
    environment:
      - PAGESPEED_API_KEY=${PAGESPEED_API_KEY}
      - GOOGLE_API_KEY=${GOOGLE_API_KEY}
      - GOOGLE_API_KEY_1=${GOOGLE_API_KEY_1:-}
      - GOOGLE_API_KEY_2=${GOOGLE_API_KEY_2:-}
      - GOOGLE_API_KEY_3=${GOOGLE_API_KEY_3:-}
      - GOOGLE_API_KEYS=${GOOGLE_API_KEYS:-}
      - DATABASE_URL=${DATABASE_URL:-postgresql+asyncpg://xe_seo:xe_seo@db:5432/xe_seo}
      - ENVIRONMENT=development
      - PRODUCTION_DOMAIN=seoapi.pxd.co.kr
    restart: unless-stopped
    volumes:
      - ./logs:/app/logs # 로그 저장용
    depends_on:
      - db
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/conf.d/default.conf
      - /etc/letsencrypt:/etc/letsencrypt:ro # Let's Encrypt 인증서
      - /var/www/certbot:/var/www/certbot:ro # Certbot webroot
    depends_on:
      - seo-backend
    restart: unless-stopped

  db:
    image: postgres:15-alpine
    environment:
      - POSTGRES_DB=xe_seo
      - POSTGRES_USER=xe_seo
      - POSTGRES_PASSWORD=xe_seo
    ports:
      - "5432:5432"
    volumes:
      - db_data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U xe_seo -d xe_seo"]
      interval: 10s
      timeout: 5s
      retries: 5
    restart: unless-stopped

volumes:
  db_data:
