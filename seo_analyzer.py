import requests
from bs4 import BeautifulSoup
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
import time
from typing import Dict, List
import urllib.parse
import json
import os
import re
import xml.etree.ElementTree as ET
import httpx
import asyncio
import datetime
from dotenv import load_dotenv

from ai_analyzer import AIAnalyzer # AIAnalyzer import 추가
from url_content_analyzer import URLContentAnalyzer

# .env 파일 로드
load_dotenv()

PAGESPEED_API_KEY = os.getenv("PAGESPEED_API_KEY")

class SEOAnalyzer:
    def __init__(self, url: str, checklist: Dict):
        self.url = self._add_scheme_if_missing(url)
        self.checklist = checklist
        self.driver = None
        self.soup = None
        self.robots_txt_content = None
        self.robots_txt_status = None
        self.headers = {}
        self.pagespeed_results = {}
        self.ai_analyzer = None

    @classmethod
    async def create(cls, url: str, checklist: Dict):
        """비동기 초기화를 위한 팩토리 메소드"""
        instance = cls(url, checklist)
        instance.ai_analyzer = AIAnalyzer()

        # 무거운 I/O 작업들을 비동기적으로 동시에 실행
        # 1. Selenium 드라이버 초기화 (블록킹 작업이므로 별도 스레드에서 실행)
        init_driver_task = asyncio.to_thread(instance._init_driver_and_soup)
        
        # 2. 네트워크 요청들 (httpx로 비동기 실행)
        async with httpx.AsyncClient(timeout=60, follow_redirects=True, verify=False) as client:
            # 드라이버 초기화를 기다리는 동안 다른 네트워크 요청을 보냄
            robots_task = instance._get_robots_txt(client)
            headers_task = instance._get_headers(client)
            pagespeed_task = instance._get_pagespeed_insights(client)
            
            # 모든 비동기 작업이 완료될 때까지 대기
            await init_driver_task # 드라이버와 soup 객체가 설정될 때까지 대기
            
            results = await asyncio.gather(
                robots_task,
                headers_task,
                pagespeed_task,
                return_exceptions=True
            )

            # 결과 할당
            instance.robots_txt_content, instance.robots_txt_status = results[0] if not isinstance(results[0], Exception) else (None, None)
            instance.headers = results[1] if not isinstance(results[1], Exception) else {}
            instance.pagespeed_results = results[2] if not isinstance(results[2], Exception) else {}

        return instance

    def _init_driver_and_soup(self):
        """Selenium 드라이버와 BeautifulSoup 객체를 초기화하는 동기 함수"""
        try:
            options = Options()
            options.add_argument("--headless")
            options.add_argument("--no-sandbox")
            options.add_argument("--disable-dev-shm-usage")
            options.add_argument("--disable-gpu")  # GPU 비활성화로 속도 향상
            options.add_argument("--disable-extensions")  # 확장 프로그램 비활성화
            options.add_argument("--disable-images")  # 이미지 로딩 비활성화로 속도 향상
            options.add_argument("user-agent=Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/123.0.0.0 Safari/537.36")
            # Docker 내 chromium 바이너리를 명시
            chrome_binary = os.getenv("CHROME_BINARY", "/usr/bin/chromium")
            if os.path.exists(chrome_binary):
                options.binary_location = chrome_binary
            
            # 명시적 chromedriver 경로 사용
            chromedriver_path = os.getenv("CHROMEDRIVER", "/usr/bin/chromedriver")
            if os.path.exists(chromedriver_path):
                service = Service(executable_path=chromedriver_path)
                self.driver = webdriver.Chrome(service=service, options=options)
            else:
                # 경로를 찾지 못하면 Selenium Manager에 위임
                self.driver = webdriver.Chrome(options=options)
            
            print(f"🚀 Selenium 드라이버 시작됨 (PID: {self.driver.service.process.pid if self.driver.service.process else 'Unknown'})")
            
            self.driver.get(self.url)
            time.sleep(2) # 페이지 로딩 대기 시간 단축 (3초 → 2초)
            self.soup = BeautifulSoup(self.driver.page_source, "html.parser")
        except Exception as e:
            print(f"WebDriver 초기화 또는 페이지 파싱 오류: {e}")
            # 초기화 실패 시에도 드라이버가 생성되었다면 정리
            if hasattr(self, 'driver') and self.driver:
                try:
                    self.driver.quit()
                    print("🧹 실패한 드라이버 정리 완료")
                except:
                    pass
            self.driver = None
            self.soup = None

    def _get_soup(self):
        return BeautifulSoup(self.driver.page_source, "html.parser") if self.driver else None

    async def _get_headers(self, client: httpx.AsyncClient):
        try:
            response = await client.head(self.url)
            return response.headers
        except httpx.RequestError as e:
            print(f"헤더 정보 요청 오류: {e}")
            return {}

    async def _get_robots_txt(self, client: httpx.AsyncClient):
        # 다양한 호스트/프로토콜 조합을 시도하여 robots.txt를 최대한 발견
        parsed = urllib.parse.urlparse(self.url)
        host = parsed.netloc
        host_variants = {host}
        if not host.startswith('www.'):
            host_variants.add(f"www.{host}")
        else:
            host_variants.add(host[4:])
        candidates = []
        for h in host_variants:
            candidates.append(f"https://{h}/robots.txt")
            candidates.append(f"http://{h}/robots.txt")
        for robots_url in candidates:
            try:
                response = await client.get(robots_url, follow_redirects=True, timeout=10.0)
                if response.status_code == 200:
                    return (response.text, 200)
            except httpx.RequestError:
                continue
        return None, None

    def _add_scheme_if_missing(self, url: str) -> str:
        return "https://" + url if not urllib.parse.urlparse(url).scheme else url

    async def _get_pagespeed_insights(self, client: httpx.AsyncClient) -> Dict:
        """Google PageSpeed Insights API를 비동기적으로 호출하여 성능 데이터를 가져옵니다."""
        if not PAGESPEED_API_KEY:
            print("PageSpeed API 키가 설정되지 않아 기본 성능 분석을 수행합니다.")
            print("API 키를 설정하려면:")
            print("1. Google Cloud Console에서 PageSpeed Insights API를 활성화하세요")
            print("2. API 키를 생성하고 환경변수 PAGESPEED_API_KEY에 설정하세요")
            print("3. 또는 .env 파일에 PAGESPEED_API_KEY=your_api_key_here를 추가하세요")
            
            # 기본 성능 분석 수행
            return await self._get_basic_performance_metrics()
        
        # URL 인코딩 추가
        encoded_url = urllib.parse.quote(self.url, safe='')
        api_url = f"https://www.googleapis.com/pagespeedonline/v5/runPagespeed?url={encoded_url}&key={PAGESPEED_API_KEY}&category=PERFORMANCE&strategy=MOBILE"
        print(f"PageSpeed API 호출 시도: {self.url}")
        print(f"인코딩된 URL: {encoded_url}")
        
        try:
            # 타임아웃을 20초로 늘리고 재시도 로직 추가
            for attempt in range(2):  # 최대 2번 시도
                try:
                    response = await client.get(api_url, timeout=20.0)  # 20초로 늘림
                    response.raise_for_status()
                    print("PageSpeed API 호출 성공!")
                    return response.json()
                except httpx.TimeoutException as e:
                    if attempt == 0:  # 첫 번째 시도에서 실패
                        print(f"PageSpeed API 첫 번째 시도 시간 초과 (20초), 재시도 중...")
                        continue
                    else:  # 두 번째 시도에서도 실패
                        print(f"PageSpeed Insights API 호출 시간 초과 (20초) - 기본 성능 분석을 수행합니다: {e}")
                        return await self._get_basic_performance_metrics()
        except httpx.HTTPStatusError as e:
            print(f"PageSpeed API HTTP 오류 ({e.response.status_code}): {e}")
            print(f"응답 내용: {e.response.text[:200]}...")  # 응답 내용 일부 출력
            if e.response.status_code == 403:
                print(f"PageSpeed API 키 인증 실패 (403) - 기본 성능 분석을 수행합니다: {e}")
            elif e.response.status_code == 429:
                print(f"PageSpeed API 할당량 초과 (429) - 기본 성능 분석을 수행합니다: {e}")
            else:
                print(f"PageSpeed API HTTP 오류 ({e.response.status_code}) - 기본 성능 분석을 수행합니다: {e}")
            return await self._get_basic_performance_metrics()
        except httpx.RequestError as e:
            print(f"PageSpeed Insights API 네트워크 오류 - 기본 성능 분석을 수행합니다: {e}")
            return await self._get_basic_performance_metrics()

    async def _get_basic_performance_metrics(self) -> Dict:
        """Selenium을 사용하여 기본적인 성능 메트릭을 수집합니다."""
        if not self.driver:
            return {}
        
        try:
            print("기본 성능 메트릭 수집 시작...")
            
            # 페이지 로드 시간 측정
            navigation_timing = self.driver.execute_script("""
                var timing = performance.timing;
                return {
                    'domContentLoaded': timing.domContentLoadedEventEnd - timing.navigationStart,
                    'loadComplete': timing.loadEventEnd - timing.navigationStart,
                    'firstPaint': performance.getEntriesByType('paint')[0]?.startTime || 0,
                    'firstContentfulPaint': performance.getEntriesByType('paint')[1]?.startTime || 0
                };
            """)
            
            # 리소스 크기 측정
            resource_sizes = self.driver.execute_script("""
                var resources = performance.getEntriesByType('resource');
                var totalSize = 0;
                var imageSize = 0;
                var scriptSize = 0;
                var cssSize = 0;
                
                resources.forEach(function(resource) {
                    if (resource.transferSize) {
                        totalSize += resource.transferSize;
                        if (resource.name.includes('.jpg') || resource.name.includes('.png') || resource.name.includes('.gif') || resource.name.includes('.webp')) {
                            imageSize += resource.transferSize;
                        } else if (resource.name.includes('.js')) {
                            scriptSize += resource.transferSize;
                        } else if (resource.name.includes('.css')) {
                            cssSize += resource.transferSize;
                        }
                    }
                });
                
                return {
                    'totalSize': totalSize,
                    'imageSize': imageSize,
                    'scriptSize': scriptSize,
                    'cssSize': cssSize
                };
            """)
            
            # DOM 요소 수 측정
            dom_elements = self.driver.execute_script("""
                return {
                    'totalElements': document.getElementsByTagName('*').length,
                    'images': document.images.length,
                    'scripts': document.scripts.length,
                    'stylesheets': document.styleSheets.length
                };
            """)
            
            # 추가 성능 메트릭
            additional_metrics = self.driver.execute_script("""
                return {
                    'titleLength': document.title.length,
                    'metaDescriptionLength': document.querySelector('meta[name="description"]')?.content?.length || 0,
                    'h1Count': document.querySelectorAll('h1').length,
                    'h2Count': document.querySelectorAll('h2').length,
                    'linkCount': document.querySelectorAll('a').length,
                    'formCount': document.querySelectorAll('form').length
                };
            """)
            
            result = {
                'lighthouseResult': {
                    'audits': {
                        'largest-contentful-paint': {
                            'numericValue': navigation_timing.get('firstContentfulPaint', 0) * 1000
                        },
                        'total-blocking-time': {
                            'numericValue': max(0, navigation_timing.get('domContentLoaded', 0) - navigation_timing.get('firstPaint', 0))
                        },
                        'cumulative-layout-shift': {
                            'numericValue': 0.1  # 기본값
                        },
                        'server-response-time': {
                            'numericValue': navigation_timing.get('domContentLoaded', 0) * 1000
                        }
                    }
                },
                'basicMetrics': {
                    'pageLoadTime': navigation_timing.get('loadComplete', 0),
                    'domContentLoaded': navigation_timing.get('domContentLoaded', 0),
                    'totalSizeKB': resource_sizes.get('totalSize', 0) / 1024,
                    'imageSizeKB': resource_sizes.get('imageSize', 0) / 1024,
                    'scriptSizeKB': resource_sizes.get('scriptSize', 0) / 1024,
                    'cssSizeKB': resource_sizes.get('cssSize', 0) / 1024,
                    'totalElements': dom_elements.get('totalElements', 0),
                    'imageCount': dom_elements.get('images', 0),
                    'scriptCount': dom_elements.get('scripts', 0),
                    'stylesheetCount': dom_elements.get('stylesheets', 0),
                    'titleLength': additional_metrics.get('titleLength', 0),
                    'metaDescriptionLength': additional_metrics.get('metaDescriptionLength', 0),
                    'h1Count': additional_metrics.get('h1Count', 0),
                    'h2Count': additional_metrics.get('h2Count', 0),
                    'linkCount': additional_metrics.get('linkCount', 0),
                    'formCount': additional_metrics.get('formCount', 0)
                }
            }
            
            print(f"기본 성능 메트릭 수집 완료:")
            print(f"- 페이지 로드 시간: {result['basicMetrics']['pageLoadTime']}ms")
            print(f"- 총 크기: {result['basicMetrics']['totalSizeKB']:.1f}KB")
            print(f"- 이미지 수: {result['basicMetrics']['imageCount']}개")
            print(f"- 스크립트 수: {result['basicMetrics']['scriptCount']}개")
            
            return result
            
        except Exception as e:
            print(f"기본 성능 메트릭 수집 중 오류: {e}")
            return {}

    async def analyze(self) -> Dict:
        if not self.driver or not self.soup:
            return {"error": "WebDriver 초기화 또는 페이지 파싱에 실패하여 분석을 진행할 수 없습니다."}

        # 1. 모든 카테고리와 섹션의 분석을 비동기적으로 수행합니다.
        categories = self.checklist.get("seo_checklist", {}).get("categories", [])
        categories_results = [await self._analyze_category(cat) for cat in categories]
        
        # 2. 분석 결과에서 'fail' 또는 'warning' 상태인 모든 체크 항목을 하나의 리스트로 수집합니다.
        all_failed_checks = []
        mixed_content_info = None
        
        # 전역적으로 유니크한 AI용 ID를 부여하여 섹션 간 ID 충돌을 방지
        ai_id_counter = 1
        for category in categories_results:
            for section in category['sections']:
                failed_checks_in_section = [c for c in section['checks'] if c['status'] in ['fail', 'warning']]
                for check in failed_checks_in_section:
                    # 섹션 컨텍스트와 원본 체크 ID를 함께 보존하고, AI 프롬프트에 사용할 유니크 ID를 별도로 부여
                    check_with_context = check.copy()
                    check_with_context['section_id'] = section.get('section_id') or section.get('id')
                    check_with_context['original_check_id'] = check.get('id')
                    # AI용 임시 ID로 교체 (프롬프트 내 중복 회피)
                    check_with_context['id'] = ai_id_counter
                    ai_id_counter += 1
                    all_failed_checks.append(check_with_context)
                
                # Mixed Content 검사 결과 수집
                if section.get('section_id') == 'security_trust':
                    for check in section['checks']:
                        if 'mixed content' in check.get('title', '').lower() or '혼합 콘텐츠' in check.get('title', ''):
                            # 저장된 Mixed Content 정보 사용
                            mixed_content_count = check.get('mixed_content_count', 0)
                            mixed_content_details = check.get('mixed_content_details', '')
                            
                            mixed_content_info = {
                                'status': check.get('status'),
                                'message': check.get('message'),
                                'score': check.get('score'),
                                'mixed_content_count': mixed_content_count,
                                'mixed_content_details': mixed_content_details
                            }
                            break

        # 3. 수집된 항목이 있을 경우, 단 한 번만 AI에게 개선 제안을 요청합니다.
        all_ai_suggestions = []
        if all_failed_checks:
            try:
                # 실패한 항목이 너무 많으면 상위 10개만 처리 (속도 개선)
                limited_failed_checks = all_failed_checks[:10] if len(all_failed_checks) > 10 else all_failed_checks
                
                # Mixed Content 정보를 AI 제안에 포함
                if mixed_content_info and mixed_content_info.get('status') == 'fail':
                    # Mixed Content 상세 정보를 포함한 구체적인 제안 생성
                    mixed_content_count = mixed_content_info.get('mixed_content_count', 0)
                    mixed_content_details = mixed_content_info.get('mixed_content_details', '')
                    
                    # 상세 정보가 있으면 구체적인 제안 생성
                    if mixed_content_details:
                        suggestion_text = f"Mixed Content {mixed_content_count}건을 발견했습니다. 구체적으로 {mixed_content_details} 등의 HTTP 리소스를 HTTPS로 변경하여 보안을 강화하세요."
                    else:
                        suggestion_text = f"Mixed Content {mixed_content_count}건을 발견했습니다. 모든 HTTP 리소스(이미지, 스크립트, CSS, iframe 등)를 HTTPS로 변경하여 보안을 강화하세요."
                    
                    mixed_content_suggestion = {
                        'id': 'mixed_content_special',
                        'suggestion': suggestion_text,
                        'expected_impact': "보안 강화 및 브라우저 경고 제거"
                    }
                    limited_failed_checks.append(mixed_content_suggestion)
                
                all_ai_suggestions = await self.ai_analyzer.get_suggestions_for_failed_checks(limited_failed_checks)
            except Exception as e:
                print(f"전체 AI 제안 요청 중 오류 발생: {e}")
                # AI 분석 실패 시에도 기본 분석 결과는 제공
                all_ai_suggestions = []
                
                # Mixed Content 실패 시 기본 제안 생성
                if mixed_content_info and mixed_content_info.get('status') == 'fail':
                    mixed_content_count = mixed_content_info.get('mixed_content_count', 0)
                    mixed_content_details = mixed_content_info.get('mixed_content_details', '')
                    
                    # 상세 정보가 있으면 구체적인 제안 생성
                    if mixed_content_details:
                        suggestion_text = f"Mixed Content {mixed_content_count}건을 발견했습니다. 구체적으로 {mixed_content_details} 등의 HTTP 리소스를 HTTPS로 변경하여 보안을 강화하세요."
                    else:
                        suggestion_text = f"Mixed Content {mixed_content_count}건을 발견했습니다. 모든 HTTP 리소스(이미지, 스크립트, CSS, iframe 등)를 HTTPS로 변경하여 보안을 강화하세요."
                    
                    all_ai_suggestions.append({
                        'id': 'mixed_content_default',
                        'suggestion': suggestion_text,
                        'expected_impact': "보안 강화 및 브라우저 경고 제거"
                    })
        
        # 4. ID를 키로 하는 딕셔너리를 만들어 제안을 쉽게 찾을 수 있도록 합니다.
        # 섹션 단위로 제안을 정확히 매핑하기 위해 (section_id, original_check_id) 키를 사용
        suggestions_map = {}
        for s in all_ai_suggestions:
            section_id_for_suggestion = s.get('section_id')
            original_check_id = s.get('id')
            if section_id_for_suggestion is not None and original_check_id is not None:
                suggestions_map[(section_id_for_suggestion, original_check_id)] = s

        # 5. 각 섹션을 순회하며 해당하는 AI 제안을 다시 넣어줍니다.
        for category in categories_results:
            for section in category['sections']:
                section_suggestions = []
                for check in section['checks']:
                    key = (section.get('section_id') or section.get('id'), check.get('id'))
                    suggestion = suggestions_map.get(key)
                    if suggestion:
                        # 프론트엔드 매칭을 위해 제안 객체의 id는 원본 체크 ID를 유지
                        normalized = suggestion.copy()
                        normalized['id'] = check.get('id')
                        section_suggestions.append(normalized)
                section['ai_suggestions'] = section_suggestions
        
        self.driver.quit()
        return self._compile_final_result(categories_results)

    async def _analyze_category(self, category: Dict) -> Dict:
        sections = category.get("sections", [])
        sections_results = [await self._analyze_section(sec) for sec in sections]
        # 빈 섹션(체크 0개)은 제외
        sections_results = [s for s in sections_results if s.get("total_checks", 0) > 0]
        return {
            "category_id": category.get("id"),
            "category_name": category.get("name"),
            "sections": sections_results,
            "category_score": self._calculate_average_score([s.get("section_score", 0) for s in sections_results])
        }

    async def _analyze_section(self, section: Dict) -> Dict:
        import time
        section_id = section.get("id")
        all_checks_from_config = []
        if "sub_sections" in section and section["sub_sections"]:
            for sub in section["sub_sections"]:
                all_checks_from_config.extend(sub.get("items", []))
        else:
            all_checks_from_config.extend(section.get("items", []))
        
        check_results = []
        if section_id == 'crawling_indexing':
            check_results = self._perform_crawling_indexing_checks(all_checks_from_config)
        elif section_id == 'page_optimization': # ✅ '페이지 최적화' 섹션 분석 로직 추가
            check_results = self._perform_page_optimization_checks(all_checks_from_config)
        elif section_id == 'security_trust': # ✅ "보안 및 신뢰성" 섹션 분석 로직 추가
            check_results = await self._perform_security_trust_checks(all_checks_from_config)
        elif section_id == 'web_performance_technical':
            check_results = self._perform_web_performance_checks(all_checks_from_config)
        elif section_id == 'url_content_standardization': # ✅ "URL 및 콘텐츠 표준화" 섹션 분석 로직 추가
            check_results = await self._perform_url_content_standardization_checks(all_checks_from_config)
        elif section_id == 'site_structure_ux': # ✅ "사이트 구조 및 사용자 경험" 섹션 분석 로직 추가
            section_start_time = time.time()
            print(f"🔍 사이트 구조 섹션 분석 시작: {section_id} (시작 시간: {time.strftime('%H:%M:%S')})")
            check_results = await self._perform_site_structure_ux_checks(all_checks_from_config)
            section_end_time = time.time()
            section_duration = section_end_time - section_start_time
            print(f"✅ 사이트 구조 섹션 분석 완료: {len(check_results)}개 항목 (소요 시간: {section_duration:.2f}초)")
        else:
            check_results = [{"id": c.get("id"), "title": c.get("title"), "importance": c.get("importance"), "criteria": c.get("criteria"), "status": "pending", "message": "분석 미지원", "score": 0} for c in all_checks_from_config]

        passed_checks_count = len([c for c in check_results if c["status"] == "pass"])
        section_score = self._calculate_weighted_score(check_results)
        
        # AI 제안 요청 로직을 analyze() 메소드로 이동시켰으므로 여기서는 제거합니다.

        return {
            "section_id": section.get("id"), "section_name": section.get("name"), "checks": check_results,
            "total_checks": len(check_results), "passed_checks": passed_checks_count, "section_score": section_score,
            "ai_suggestions": [] # 우선 빈 리스트로 초기화
        }

    def _perform_crawling_indexing_checks(self, checks_config: List[Dict]) -> List[Dict]:
        results = []
        sitemap_urls = re.findall(r"Sitemap:\s*(.*)", self.robots_txt_content, re.IGNORECASE) if self.robots_txt_content else []

        # --- 사전 분석 ---
        crawl_delay, disallow_all, blocked_resources = None, False, []
        if self.robots_txt_content:
            in_ua_all = False
            disallow_rules = []
            for line in self.robots_txt_content.splitlines():
                line = line.strip().lower()
                if line.startswith('user-agent:'): in_ua_all = '*' in line
                if in_ua_all and line.startswith('disallow:'):
                    rule = line.split(':', 1)[1].strip()
                    if rule: disallow_rules.append(rule)
                    if rule == '/': disallow_all = True
                if in_ua_all and line.startswith('crawl-delay:'):
                    val = re.findall(r'\d+', line)
                    if val: crawl_delay = int(val[0])
            
            resources = [t.get('href') for t in self.soup.find_all('link', rel='stylesheet')] + [t.get('src') for t in self.soup.find_all('script', src=True)]
            for res_path in filter(None, resources):
                for rule in disallow_rules:
                    if res_path.startswith(rule):
                        blocked_resources.append(res_path)
                        break
        
        sitemap_content, sitemap_size, url_count, sitemap_ok, canonical_ok, sitemap_meta = None, 0, 0, True, True, {}
        if sitemap_urls:
            try:
                res = requests.get(sitemap_urls[0], timeout=10, allow_redirects=True, headers={"User-Agent": "xe-seo-analyzer/1.0"})
                if res.status_code == 200:
                    raw = res.content
                    ct = res.headers.get('Content-Type', '').lower()
                    if sitemap_urls[0].lower().endswith('.gz') or ('application/x-gzip' in ct or 'application/gzip' in ct):
                        try:
                            import gzip
                            raw = gzip.decompress(raw)
                            print("사이트맵 gzip 압축 해제 완료")
                        except Exception as e:
                            print(f"사이트맵 gzip 해제 실패: {e}")
                    sitemap_content, sitemap_size = raw, len(raw)
                    root = ET.fromstring(sitemap_content)
                    ns = {'sm': 'http://www.sitemaps.org/schemas/sitemap/0.9', 'xhtml': 'http://www.w3.org/1999/xhtml'}
                    urls = root.findall('sm:url', ns)
                    url_count = len(urls)
                    sitemap_meta = {'is_index': 'sitemapindex' in root.tag, 'has_image': b'<image:image>' in sitemap_content, 'has_video': b'<video:video>' in sitemap_content, 'has_hreflang': bool(root.find('.//xhtml:link', ns))}

                    for url_node in urls[:5]: # 샘플 5개 검사
                        page_url = url_node.find('sm:loc', ns).text
                        page_res = requests.get(page_url, timeout=5)
                        if page_res.status_code != 200: sitemap_ok = False; break
                        page_soup = BeautifulSoup(page_res.content, 'html.parser')
                        canonical_tag = page_soup.find('link', rel='canonical')
                        if canonical_tag and self._normalize_url(canonical_tag.get('href','')) != self._normalize_url(page_url):
                            canonical_ok = False; break
            except Exception as e:
                print(f"Sitemap parsing error: {e}")
                sitemap_content = None

        for item in checks_config:
            result = item.copy()
            result.update({
                "status": "fail", 
                "message": "분석 중 기술적 오류가 발생했습니다", 
                "score": 0,
                "title": item.get("title", "Unknown"),
                "importance": item.get("importance", 1),
                "criteria": item.get("criteria", "분석 중 오류 발생")
            })
            title = item.get("title", "").lower()
            
            print(f"크롤링/색인 분석 중: {title}")

            try:
                if "robots.txt 200" in title:
                    if self.robots_txt_status == 200: result.update({"status": "pass", "message": "robots.txt가 200 OK를 반환합니다.", "score": 100})
                    else: result.update({"message": f"robots.txt를 찾을 수 없습니다 (상태 코드: {self.robots_txt_status})."})
                elif "user-agent=* disallow" in title:
                    if not self.robots_txt_content: result.update({"message": "robots.txt 파일이 없습니다."})
                    elif not disallow_all: result.update({"status": "pass", "message": "모든 검색엔진의 접근을 막는 'Disallow: /' 규칙이 없습니다.", "score": 100})
                    else: result.update({"message": "치명적 오류: 'User-agent: *'에 'Disallow: /'가 설정되어 모든 검색엔진을 차단합니다."})
                elif "sitemap 지시자" in title or "robots 연동" in title:
                    if sitemap_urls: result.update({"status": "pass", "message": f"robots.txt에 사이트맵 주소 {len(sitemap_urls)}개를 확인했습니다.", "score": 100})
                    else: result.update({"message": "robots.txt에 Sitemap 지시어가 없습니다."})
                elif "crawl-delay" in title:
                    if crawl_delay is None: result.update({"status": "pass", "message": "Crawl-delay가 설정되지 않았습니다 (일반적으로 권장).", "score": 100})
                    elif crawl_delay <= 5: result.update({"status": "pass", "message": f"Crawl-delay가 {crawl_delay}초로 적절하게 설정되었습니다.", "score": 100})
                    else: result.update({"message": f"Crawl-delay가 {crawl_delay}초로 너무 길어 크롤링이 비효율적일 수 있습니다.", "score": 40})
                elif "http→https" in title:
                    # (This check was moved to a different function but left here as fallback idea)
                    http_url = self.url.replace("https://", "http://", 1)
                    res = requests.head(http_url, allow_redirects=False, timeout=5)
                    if 300 <= res.status_code < 400 and res.headers.get("Location", "").startswith("https://"):
                         result.update({"status": "pass", "message": "HTTP에서 HTTPS로 정상 리디렉션됩니다.", "score": 100})
                    else:
                         result.update({"message": f"HTTP->HTTPS 리디렉션이 올바르지 않습니다 (상태코드: {res.status_code})."})
                elif "robots.txt 크기" in title:
                    size_kb = len(self.robots_txt_content.encode('utf-8')) / 1024 if self.robots_txt_content else 0
                    if size_kb <= 500: result.update({"status": "pass", "message": f"robots.txt 파일 크기가 {size_kb:.2f}KB로 적절합니다.", "score": 100})
                    else: result.update({"message": f"robots.txt 파일 크기가 {size_kb:.2f}KB로 너무 큽니다 (500KB 이하 권장).", "score": 40})
                elif "pre-render 블로킹" in title:
                    if not self.robots_txt_content: result.update({"status": "pass", "message": "robots.txt가 없어 리소스 차단 여부를 확인할 수 없습니다.", "score": 100})
                    elif not blocked_resources: result.update({"status": "pass", "message": "robots.txt에서 주요 렌더링 리소스(CSS, JS)를 차단하지 않습니다.", "score": 100})
                    else: 
                        blocked_list = ', '.join(blocked_resources[:3])
                        if len(blocked_resources) > 3:
                            blocked_list += f" 등 {len(blocked_resources)}개"
                        result.update({"message": f"robots.txt가 렌더링에 필요한 리소스를 차단합니다: {blocked_list}", "score": 20})
                elif "disallow 오탐 테스트" in title:
                    if not self.robots_txt_content: 
                        result.update({"status": "pass", "message": "robots.txt가 없어 Disallow 규칙이 없습니다.", "score": 100})
                    else:
                        # Disallow 규칙이 실제로 중요한 페이지를 차단하는지 테스트
                        disallow_rules = []
                        in_ua_all = False
                        for line in self.robots_txt_content.splitlines():
                            line = line.strip().lower()
                            if line.startswith('user-agent:'): 
                                in_ua_all = '*' in line
                            if in_ua_all and line.startswith('disallow:'):
                                rule = line.split(':', 1)[1].strip()
                                if rule: 
                                    disallow_rules.append(rule)
                        
                        if not disallow_rules:
                            result.update({"status": "pass", "message": "Disallow 규칙이 없어 모든 페이지가 허용됩니다.", "score": 100})
                        else:
                            # 실제 사이트에서 중요한 페이지들을 찾아서 테스트
                            important_urls = []
                            
                            # 1. 메인 페이지 (루트)
                            important_urls.append('/')
                            
                            # 2. 사이트맵에서 실제 존재하는 URL들 (최대 10개)
                            if sitemap_urls:
                                try:
                                    sitemap_res = requests.get(sitemap_urls[0], timeout=10)
                                    if sitemap_res.status_code == 200:
                                        root = ET.fromstring(sitemap_res.content)
                                        ns = {'sm': 'http://www.sitemaps.org/schemas/sitemap/0.9'}
                                        urls = root.findall('sm:url', ns)
                                        
                                        for url_node in urls[:10]:  # 최대 10개
                                            url = url_node.find('sm:loc', ns).text
                                            # 현재 도메인의 URL만 사용
                                            if self.url in url:
                                                path = urllib.parse.urlparse(url).path
                                                if path and path not in important_urls:
                                                    important_urls.append(path)
                                except Exception as e:
                                    print(f"사이트맵 파싱 오류: {e}")
                            
                            # 3. 현재 페이지에서 발견된 내부 링크들 (중요도 기반)
                            if self.soup:
                                # H1 태그가 있는 페이지는 중요
                                h1_tags = self.soup.find_all('h1')
                                for h1 in h1_tags:
                                    parent_link = h1.find_parent('a')
                                    if parent_link and parent_link.get('href'):
                                        href = parent_link['href']
                                        if href.startswith('/') and href not in important_urls:
                                            important_urls.append(href)
                                
                                # 네비게이션 메뉴 링크들
                                nav_links = self.soup.find_all('a', href=True)
                                for link in nav_links[:20]:  # 최대 20개
                                    href = link['href']
                                    if href.startswith('/') and href not in important_urls and len(important_urls) < 15:
                                        # 메뉴 관련 키워드가 포함된 링크 우선
                                        link_text = link.get_text(strip=True).lower()
                                        if any(keyword in link_text for keyword in ['about', 'contact', 'product', 'service', 'blog', 'news', 'help', 'support']):
                                            important_urls.append(href)
                            
                            # 4. 기본적인 중요 페이지들 (사이트맵이나 내부 링크에서 찾지 못한 경우)
                            if len(important_urls) < 5:
                                default_important = ['/about', '/contact', '/products', '/services', '/blog']
                                for url in default_important:
                                    if url not in important_urls:
                                        important_urls.append(url)
                            
                            # 실제 테스트 수행
                            blocked_count = 0
                            blocked_urls = []
                            for url in important_urls:
                                for rule in disallow_rules:
                                    if url.startswith(rule):
                                        blocked_count += 1
                                        blocked_urls.append(url)
                                        break
                            
                            if blocked_count == 0:
                                result.update({"status": "pass", "message": f"Disallow 규칙이 중요 페이지 {len(important_urls)}개를 차단하지 않습니다.", "score": 100})
                            elif blocked_count <= len(important_urls) * 0.2:  # 20% 이하 차단
                                result.update({"status": "warning", "message": f"Disallow 규칙이 중요 페이지 {len(important_urls)}개 중 {blocked_count}개를 차단합니다. 차단된 URL: {', '.join(blocked_urls[:3])}", "score": 70})
                            else:
                                result.update({"status": "fail", "message": f"Disallow 규칙이 중요 페이지 {len(important_urls)}개 중 {blocked_count}개를 차단합니다. 많은 중요 페이지가 차단되고 있습니다.", "score": 30})
                elif "xml 유효성" in title:
                    if not sitemap_content: result.update({"message": "robots.txt에 사이트맵이 없어 검사 불가합니다."})
                    else: result.update({"status": "pass", "message": "사이트맵이 유효한 XML 형식입니다.", "score": 100})
                elif "utf-8 인코딩" in title:
                    if not sitemap_content: result.update({"message": "사이트맵을 찾을 수 없습니다."})
                    elif b'encoding="UTF-8"' in sitemap_content[:100]: result.update({"status": "pass", "message": "사이트맵이 UTF-8로 올바르게 인코딩되었습니다.", "score": 100})
                    else: result.update({"message": "사이트맵 XML 선언에 UTF-8 인코딩이 명시되지 않았습니다."})
                elif "<url> ≤50,000" in title:
                    if not sitemap_content: result.update({"message": "사이트맵을 찾을 수 없습니다."})
                    elif url_count <= 50000: result.update({"status": "pass", "message": f"사이트맵에 포함된 URL이 {url_count}개로 권장사항을 준수합니다.", "score": 100})
                    else: result.update({"message": f"사이트맵의 URL 개수가 {url_count}개로 너무 많습니다 (50,000개 이하 권장).", "score": 40})
                elif "파일 크기 ≤50 mb" in title:
                    size_mb = sitemap_size / (1024 * 1024)
                    if not sitemap_content: result.update({"message": "사이트맵을 찾을 수 없습니다."})
                    elif size_mb <= 50: result.update({"status": "pass", "message": f"사이트맵 파일 크기가 {size_mb:.2f}MB로 권장사항을 준수합니다.", "score": 100})
                    else: result.update({"message": f"사이트맵 파일 크기가 {size_mb:.2f}MB로 너무 큽니다 (50MB 이하 권장).", "score": 40})
                elif any(k in title for k in ["허용 url 2xx", "5xx 오류율", "http 200 상태"]):
                    if not sitemap_content: result.update({"message": "사이트맵을 찾을 수 없습니다."})
                    elif sitemap_ok: result.update({"status": "pass", "message": "사이트맵의 샘플 URL들이 모두 정상적으로 응답합니다(2xx).", "score": 100})
                    else: result.update({"message": "사이트맵의 일부 URL이 200 OK 상태가 아닙니다. 깨진 링크를 확인하세요."})
                elif "canonical url만 포함" in title:
                    if not sitemap_content: result.update({"message": "사이트맵을 찾을 수 없습니다."})
                    elif canonical_ok: result.update({"status": "pass", "message": "사이트맵의 샘플 URL들이 모두 Canonical URL로 확인됩니다.", "score": 100})
                    else: result.update({"message": "사이트맵에 Canonical URL이 아닌 페이지가 포함된 것으로 보입니다."})
                elif "이미지/비디오 맵" in title:
                    if not sitemap_content: result.update({"message": "사이트맵을 찾을 수 없습니다."})
                    elif sitemap_meta.get('has_image') or sitemap_meta.get('has_video'): result.update({"status": "pass", "message": "사이트맵에 이미지 또는 비디오 정보가 포함되어 있습니다.", "score": 100})
                    else: result.update({"status": "warning", "message": "사이트맵에 이미지/비디오 정보가 없어, 관련 콘텐츠의 색인 기회를 놓칠 수 있습니다.", "score": 70})
                elif "hreflang" in title:
                    if not sitemap_content: result.update({"message": "사이트맵을 찾을 수 없습니다."})
                    elif sitemap_meta.get('has_hreflang'): result.update({"status": "pass", "message": "사이트맵에 hreflang 정보가 포함되어 있습니다.", "score": 100})
                    else: result.update({"status": "warning", "message": "다국어 사이트의 경우, hreflang 정보 추가를 고려하세요.", "score": 70})
                elif "인덱스 sitemap 체계" in title:
                    if not sitemap_content:
                        result.update({"message": "사이트맵을 찾을 수 없습니다."})
                    elif sitemap_meta.get('is_index'):
                        result.update({"status": "pass", "message": "사이트맵 인덱스 파일을 사용하여 여러 사이트맵을 효율적으로 관리합니다.", "score": 100})
                    else:
                        # 인덱스 미사용 시 URL 50,000개 이하이면 통과
                        if url_count <= 50000:
                            result.update({
                                "status": "pass",
                                "message": f"사이트맵 URL {url_count}개로 단일 Sitemap으로도 기준을 충족합니다 (인덱스 미사용 허용).",
                                "score": 100
                            })
                        else:
                            # 사양 초과: 인덱스 분할 필요
                            result.update({
                                "status": "fail",
                                "message": f"사이트맵 URL {url_count}개로 50,000개를 초과합니다. 인덱스 Sitemap으로 분할이 필요합니다.",
                                "score": 20
                            })
                elif "lastmod 정확도" in title or "<lastmod> 정확도" in title:
                    if not sitemap_content: 
                        result.update({"message": "사이트맵을 찾을 수 없습니다."})
                    else:
                        try:
                            print("lastmod 정확도 분석 시작...")
                            # 사이트맵에서 lastmod 정보를 추출하고 실제 페이지 수정 시간과 비교
                            root = ET.fromstring(sitemap_content)
                            ns = {'sm': 'http://www.sitemaps.org/schemas/sitemap/0.9'}
                            urls = root.findall('sm:url', ns)
                            
                            print(f"사이트맵에서 {len(urls)}개의 URL 발견")
                            
                            if not urls:
                                result.update({"message": "사이트맵에 URL이 없습니다."})
                            else:
                                # 샘플 URL들의 lastmod 정확도 검사
                                sample_count = min(5, len(urls))
                                accurate_count = 0
                                total_checked = 0
                                lastmod_found = 0
                                
                                for i, url_node in enumerate(urls[:sample_count]):
                                    lastmod_tag = url_node.find('sm:lastmod', ns)
                                    if lastmod_tag is not None:
                                        lastmod_found += 1
                                        lastmod_str = lastmod_tag.text
                                        print(f"URL {i+1}: lastmod = {lastmod_str}")
                                        try:
                                            # lastmod 시간을 파싱
                                            lastmod_time = datetime.datetime.fromisoformat(lastmod_str.replace('Z', '+00:00'))
                                            
                                            # 실제 페이지의 Last-Modified 헤더 확인
                                            page_url = url_node.find('sm:loc', ns).text
                                            print(f"페이지 확인: {page_url}")
                                            page_res = requests.head(page_url, timeout=5)
                                            
                                            if 'Last-Modified' in page_res.headers:
                                                header_time_str = page_res.headers['Last-Modified']
                                                header_time = datetime.datetime.strptime(header_time_str, '%a, %d %b %Y %H:%M:%S %Z')
                                                
                                                # 시간 차이 계산 (72시간 = 3일)
                                                time_diff = abs((lastmod_time - header_time).total_seconds() / 3600)
                                                print(f"시간 차이: {time_diff:.1f}시간")
                                                
                                                if time_diff <= 72:  # 72시간 이내
                                                    accurate_count += 1
                                                total_checked += 1
                                                
                                        except (ValueError, TypeError) as e:
                                            print(f"lastmod 파싱 실패: {e}")
                                            continue
                                    else:
                                        print(f"URL {i+1}: lastmod 태그 없음")
                                
                                print(f"lastmod 태그 발견: {lastmod_found}개, 검사 완료: {total_checked}개")
                                
                                if total_checked == 0:
                                    if lastmod_found == 0:
                                        result.update({"status": "fail", "message": "사이트맵에 lastmod 태그가 없습니다.", "score": 20})
                                    else:
                                        result.update({"status": "fail", "message": "lastmod 정보 파싱에 실패했습니다.", "score": 20})
                                elif accurate_count == total_checked:
                                    result.update({"status": "pass", "message": f"lastmod 정보가 샘플 {total_checked}개 모두 정확합니다 (±72시간).", "score": 100})
                                elif accurate_count >= total_checked * 0.8:
                                    result.update({"status": "warning", "message": f"lastmod 정보가 샘플 {total_checked}개 중 {accurate_count}개만 정확합니다.", "score": 70})
                                else:
                                    result.update({"status": "fail", "message": f"lastmod 정보가 샘플 {total_checked}개 중 {accurate_count}개만 정확합니다. 정확도가 낮습니다.", "score": 30})
                                    
                        except Exception as e:
                            print(f"lastmod 정확도 분석 중 예외 발생: {e}")
                            result.update({"status": "fail", "message": f"lastmod 정확도 분석 중 오류: {e}", "score": 20})
                else:
                    result.update({"status": "pending", "message": "분석 미지원"})
            except Exception as e:
                result.update({"message": f"분석 중 오류 발생: {e}"})
            results.append(result)
        return results

    def _perform_page_optimization_checks(self, checks_config: List[Dict]) -> List[Dict]:
        """'페이지 최적화' 섹션의 메타 태그, H1, 구조화 데이터 등을 분석합니다."""
        results = []
        
        # JSON-LD 구조화 데이터 추출
        json_ld_scripts = self.soup.find_all('script', type='application/ld+json')
        structured_data = []
        for script in json_ld_scripts:
            try:
                data = json.loads(script.string)
                if isinstance(data, list):
                    structured_data.extend(data)
                else:
                    structured_data.append(data)
            except (json.JSONDecodeError, AttributeError):
                continue

        # 필수 항목과 선택적 항목 분류
        required_items = [
            "title 30–60", "meta description", "h1 단일 존재", 
            "viewport", "charset", "meta robots", "og:", "breadcrumb schema",
            "breadcrumblist", "invalid field"
        ]
        
        # 진짜 선택적 항목들 (구조화 데이터 관련)
        optional_items = [
            "article", "product", "faqpage", "videoobject", 
            "imageobject", "howto", "jobposting", "event"
        ]

        for item in checks_config:
            result = item.copy()
            result.update({
                "status": "fail", 
                "message": "분석 중 기술적 오류가 발생했습니다", 
                "score": 0,
                "title": item.get("title", "Unknown"),
                "importance": item.get("importance", 1),
                "criteria": item.get("criteria", "분석 중 오류 발생")
            })
            check_title = item.get("title", "").lower()
            
            # 필수 항목인지 선택적 항목인지 판단
            is_required = any(req_item in check_title for req_item in required_items)
            is_optional = any(opt_item in check_title for opt_item in optional_items)

            try:
                # 1. Title 태그 검사 (30-60자)
                if "title 30–60" in check_title:
                    title_tag = self.soup.find('title')
                    if title_tag:
                        title_text = title_tag.get_text(strip=True)
                        title_len = len(title_text)
                        if 30 <= title_len <= 60:
                            result.update({"status": "pass", "message": f"제목 길이가 최적화되었습니다 ({title_len}자).", "score": 100})
                        else:
                            result.update({"message": f"제목 길이가 권장 범위(30~60자)를 벗어납니다 ({title_len}자).", "score": 40})
                    else:
                        result.update({"message": "페이지에 <title> 태그가 존재하지 않습니다."})
                
                # 2. Meta Description 검사 (70-155자)
                elif "meta description" in check_title:
                    desc_tag = self.soup.find('meta', attrs={'name': 'description'})
                    if desc_tag and desc_tag.get('content'):
                        desc_text = desc_tag['content'].strip()
                        desc_len = len(desc_text)
                        if 70 <= desc_len <= 155:
                            result.update({"status": "pass", "message": f"메타 디스크립션 길이가 적절합니다 ({desc_len}자).", "score": 100})
                        else:
                            result.update({"message": f"메타 디스크립션 길이가 권장 범위(70~155자)를 벗어납니다 ({desc_len}자).", "score": 40})
                    else:
                        result.update({"message": "메타 디스크립션이 없거나 내용이 비어있습니다."})
                        
                # 3. H1 태그 검사 (단일 존재)
                elif "h1 단일 존재" in check_title:
                    h1_tags = self.soup.find_all('h1')
                    h1_count = len(h1_tags)
                    if h1_count == 1:
                        result.update({"status": "pass", "message": "H1 태그가 1개 존재하여 명확한 주제를 전달합니다.", "score": 100})
                    elif h1_count == 0:
                        result.update({"message": "페이지의 핵심 주제를 나타내는 H1 태그가 없습니다."})
                    else:
                        result.update({"message": f"H1 태그가 {h1_count}개 존재합니다. 페이지당 하나만 사용하는 것을 권장합니다.", "score": 20})
                
                # 4. Meta Robots 검사
                elif "meta robots" in check_title:
                    robots_tag = self.soup.find('meta', attrs={'name': 'robots'})
                    if robots_tag and robots_tag.get('content'):
                        robots_content = robots_tag['content'].lower()
                        if 'noindex' in robots_content or 'nofollow' in robots_content:
                            result.update({"message": f"robots 메타 태그가 검색엔진 색인을 제한합니다: {robots_content}", "score": 0})
                        else:
                            result.update({"status": "pass", "message": "robots 메타 태그가 검색엔진 친화적으로 설정되었습니다.", "score": 100})
                    else:
                        result.update({"status": "pass", "message": "robots 메타 태그가 없어 기본값(index,follow)이 적용됩니다.", "score": 100})
                
                # 5. Open Graph 태그 검사
                elif "og:title" in check_title or "og:" in check_title:
                    og_title = self.soup.find('meta', property='og:title')
                    og_description = self.soup.find('meta', property='og:description')
                    og_image = self.soup.find('meta', property='og:image')
                    og_url = self.soup.find('meta', property='og:url')
                    og_type = self.soup.find('meta', property='og:type')
                    
                    og_count = sum([1 for tag in [og_title, og_description, og_image, og_url, og_type] if tag])
                    
                    if og_count >= 4:
                        result.update({"status": "pass", "message": f"Open Graph 태그가 {og_count}개 설정되어 소셜 미디어 공유에 최적화되었습니다.", "score": 100})
                    elif og_count >= 2:
                        result.update({"message": f"Open Graph 태그가 {og_count}개만 설정되었습니다. 더 완전한 설정을 권장합니다.", "score": 60})
                    else:
                        result.update({"message": "Open Graph 태그가 부족하여 소셜 미디어 공유 시 제대로 표시되지 않을 수 있습니다."})
                
                # 6. Viewport meta 태그 검사
                elif "viewport" in check_title:
                    viewport_tag = self.soup.find('meta', attrs={'name': 'viewport'})
                    if viewport_tag and viewport_tag.get('content'):
                        viewport_content = viewport_tag['content'].lower()
                        if 'width=device-width' in viewport_content:
                            result.update({"status": "pass", "message": "viewport 메타 태그가 모바일 친화적으로 설정되었습니다.", "score": 100})
                        else:
                            result.update({"message": "viewport 메타 태그에 'width=device-width'가 없어 모바일 표시에 문제가 있을 수 있습니다.", "score": 40})
                    else:
                        result.update({"message": "viewport 메타 태그가 없어 모바일 사용자 경험이 저하될 수 있습니다."})
                
                # 7. Charset UTF-8 검사
                elif "charset" in check_title:
                    charset_tag = self.soup.find('meta', charset=True)
                    if not charset_tag:
                        # http-equiv 방식도 확인
                        charset_tag = self.soup.find('meta', attrs={'http-equiv': 'Content-Type'})
                    
                    if charset_tag:
                        charset_value = charset_tag.get('charset', '').lower()
                        if not charset_value and charset_tag.get('content'):
                            charset_match = re.search(r'charset=([^;]+)', charset_tag.get('content', '').lower())
                            charset_value = charset_match.group(1) if charset_match else ''
                        
                        if 'utf-8' in charset_value:
                            result.update({"status": "pass", "message": "UTF-8 문자 인코딩이 올바르게 설정되었습니다.", "score": 100})
                        else:
                            result.update({"message": f"문자 인코딩이 UTF-8이 아닙니다: {charset_value}", "score": 50})
                    else:
                        result.update({"message": "문자 인코딩 선언이 없어 텍스트 표시에 문제가 있을 수 있습니다."})
                
                # 8. Breadcrumb Schema 검사
                elif "breadcrumb schema" in check_title:
                    breadcrumb_found = False
                    for data in structured_data:
                        if data.get('@type') == 'BreadcrumbList':
                            breadcrumb_found = True
                            break
                    
                    if breadcrumb_found:
                        result.update({"status": "pass", "message": "BreadcrumbList 구조화 데이터가 구현되어 있습니다.", "score": 100})
                    else:
                        # HTML 내 breadcrumb navigation 확인
                        nav_elements = self.soup.find_all('nav')
                        breadcrumb_nav = any('breadcrumb' in nav.get('class', []) or 
                                           'breadcrumb' in nav.get('aria-label', '').lower() or
                                           'breadcrumb' in str(nav).lower() 
                                           for nav in nav_elements)
                        
                        if breadcrumb_nav:
                            result.update({"message": "HTML breadcrumb은 있지만 구조화 데이터가 없습니다.", "score": 50})
                        else:
                            result.update({"message": "breadcrumb 네비게이션 및 구조화 데이터가 없습니다."})
                
                # 9. Article 구조화 데이터 검사 (선택적 항목)
                elif "article 필수" in check_title or "article" in check_title:
                    article_found = False
                    for data in structured_data:
                        if data.get('@type') in ['Article', 'NewsArticle', 'BlogPosting']:
                            has_headline = bool(data.get('headline'))
                            has_author = bool(data.get('author'))
                            has_date = bool(data.get('datePublished') or data.get('dateModified'))
                            
                            if has_headline and has_author and has_date:
                                result.update({"status": "pass", "message": "Article 구조화 데이터가 필수 속성(headline, author, date)을 모두 포함합니다.", "score": 100})
                                article_found = True
                                break
                            else:
                                missing = []
                                if not has_headline: missing.append('headline')
                                if not has_author: missing.append('author')
                                if not has_date: missing.append('date')
                                result.update({"status": "fail", "message": f"Article 구조화 데이터에 누락된 속성이 있습니다: {', '.join(missing)}", "score": 40})
                                article_found = True
                                break
                    
                    if not article_found:
                        # 선택적 항목: 없어서 건너뜀
                        result.update({"status": "optional_skip", "message": "Article 구조화 데이터가 없습니다. 뉴스/블로그 사이트라면 추가를 고려하세요.", "score": 100})
                
                # 10. Product Schema 검사 (선택적 항목)
                elif "product" in check_title:
                    product_found = False
                    for data in structured_data:
                        if data.get('@type') == 'Product':
                            has_name = bool(data.get('name'))
                            has_price = bool(data.get('offers', {}).get('price') or data.get('offers', {}).get('priceSpecification'))
                            has_availability = bool(data.get('offers', {}).get('availability'))
                            
                            if has_name and has_price and has_availability:
                                result.update({"status": "pass", "message": "Product 구조화 데이터가 필수 속성(name, price, availability)을 모두 포함합니다.", "score": 100})
                                product_found = True
                                break
                            else:
                                missing = []
                                if not has_name: missing.append('name')
                                if not has_price: missing.append('price')
                                if not has_availability: missing.append('availability')
                                result.update({"status": "fail", "message": f"Product 구조화 데이터에 누락된 속성이 있습니다: {', '.join(missing)}", "score": 40})
                                product_found = True
                                break
                    
                    if not product_found:
                        # 선택적 항목: 없어서 건너뜀
                        result.update({"status": "optional_skip", "message": "Product 구조화 데이터가 없습니다. 전자상거래 사이트라면 추가를 고려하세요.", "score": 100})
                
                # 11. FAQPage 구조화 데이터 검사
                elif "faqpage" in check_title:
                    faq_found = False
                    for data in structured_data:
                        if data.get('@type') == 'FAQPage':
                            main_entity = data.get('mainEntity', [])
                            if isinstance(main_entity, list) and len(main_entity) >= 2:
                                result.update({"status": "pass", "message": f"FAQPage 구조화 데이터에 {len(main_entity)}개의 Q&A가 있습니다.", "score": 100})
                                faq_found = True
                                break
                            elif main_entity:
                                result.update({"status": "warning", "message": "FAQPage 구조화 데이터가 있지만 Q&A 항목이 부족합니다.", "score": 50})
                                faq_found = True
                                break
                    
                    if not faq_found:
                        # 선택적 항목: 없어서 건너뜀
                        result.update({"status": "optional_skip", "message": "FAQPage 구조화 데이터가 없습니다. FAQ 섹션이 있다면 추가를 고려하세요.", "score": 100})
                
                # 12. BreadcrumbList 구조화 데이터 검사
                elif "breadcrumblist" in check_title:
                    breadcrumb_found = False
                    for data in structured_data:
                        if data.get('@type') == 'BreadcrumbList':
                            items = data.get('itemListElement', [])
                            if items and all(item.get('position') for item in items):
                                result.update({"status": "pass", "message": f"BreadcrumbList가 {len(items)}개 항목으로 올바르게 구성되었습니다.", "score": 100})
                                breadcrumb_found = True
                                break
                            else:
                                result.update({"status": "fail", "message": "BreadcrumbList 구조화 데이터에 position 속성이 누락되었습니다.", "score": 40})
                                breadcrumb_found = True
                                break
                    
                    if not breadcrumb_found:
                        result.update({"status": "fail", "message": "BreadcrumbList 구조화 데이터가 없습니다."})
                
                # 13. VideoObject 구조화 데이터 검사
                elif "videoobject" in check_title:
                    video_found = False
                    for data in structured_data:
                        if data.get('@type') == 'VideoObject':
                            duration = data.get('duration')
                            if duration and ('PT' in str(duration) or 'P' in str(duration)):
                                result.update({"status": "pass", "message": "VideoObject에 ISO8601 형식의 duration이 설정되었습니다.", "score": 100})
                                video_found = True
                                break
                            else:
                                result.update({"status": "fail", "message": "VideoObject에 duration이 없거나 ISO8601 형식이 아닙니다.", "score": 40})
                                video_found = True
                                break
                    
                    if not video_found:
                        # 선택적 항목: 없어서 건너뜀
                        result.update({"status": "optional_skip", "message": "VideoObject 구조화 데이터가 없습니다. 동영상 콘텐츠가 있다면 추가를 고려하세요.", "score": 100})
                
                # 14. ImageObject 구조화 데이터 검사 (선택적 항목)
                elif "imageobject" in check_title:
                    image_found = False
                    for data in structured_data:
                        if data.get('@type') == 'ImageObject':
                            url = data.get('url', '')
                            if url.startswith('https://'):
                                result.update({"status": "pass", "message": "ImageObject URL이 HTTPS로 설정되었습니다.", "score": 100})
                                image_found = True
                                break
                            else:
                                result.update({"status": "fail", "message": "ImageObject URL이 HTTPS가 아닙니다.", "score": 40})
                                image_found = True
                                break
                    
                    if not image_found:
                        # 선택적 항목: 없어서 건너뜀
                        result.update({"status": "optional_skip", "message": "ImageObject 구조화 데이터가 없습니다. 중요한 이미지가 있다면 추가를 고려하세요.", "score": 100})
                
                # 15. HowTo 구조화 데이터 검사 (선택적 항목)
                elif "howto" in check_title:
                    howto_found = False
                    for data in structured_data:
                        if data.get('@type') == 'HowTo':
                            steps = data.get('step', [])
                            if isinstance(steps, list) and len(steps) >= 3:
                                result.update({"status": "pass", "message": f"HowTo에 {len(steps)}개의 단계가 설정되었습니다.", "score": 100})
                                howto_found = True
                                break
                            else:
                                result.update({"status": "fail", "message": "HowTo에 3개 이상의 단계가 필요합니다.", "score": 40})
                                howto_found = True
                                break
                    
                    if not howto_found:
                        # 선택적 항목: 없어서 건너뜀
                        result.update({"status": "optional_skip", "message": "HowTo 구조화 데이터가 없습니다. 단계별 가이드가 있다면 추가를 고려하세요.", "score": 100})
                
                # 17. JobPosting 구조화 데이터 검사 (선택적 항목)
                elif "jobposting" in check_title:
                    job_found = False
                    for data in structured_data:
                        if data.get('@type') == 'JobPosting':
                            valid_through = data.get('validThrough')
                            if valid_through:
                                result.update({"status": "pass", "message": "JobPosting에 validThrough 날짜가 설정되었습니다.", "score": 100})
                                job_found = True
                                break
                            else:
                                result.update({"status": "fail", "message": "JobPosting에 validThrough 만료일이 없습니다.", "score": 40})
                                job_found = True
                                break
                    
                    if not job_found:
                        # 선택적 항목: 없어서 건너뜀
                        result.update({"status": "optional_skip", "message": "JobPosting 구조화 데이터가 없습니다. 채용 정보가 있다면 추가를 고려하세요.", "score": 100})
                
                # 18. Event 구조화 데이터 검사 (선택적 항목)
                elif "event" in check_title:
                    event_found = False
                    for data in structured_data:
                        if data.get('@type') == 'Event':
                            start_date = data.get('startDate')
                            if start_date and ('T' in str(start_date) or '+' in str(start_date)):
                                result.update({"status": "pass", "message": "Event에 시간대가 포함된 startDate가 설정되었습니다.", "score": 100})
                                event_found = True
                                break
                            else:
                                result.update({"status": "fail", "message": "Event에 시간대가 포함된 startDate가 필요합니다.", "score": 40})
                                event_found = True
                                break
                    
                    if not event_found:
                        # 선택적 항목: 없어서 건너뜀
                        result.update({"status": "optional_skip", "message": "Event 구조화 데이터가 없습니다. 이벤트나 행사가 있다면 추가를 고려하세요.", "score": 100})
                
                # 20. 구조화 데이터 오류 검사 (Invalid field)
                elif "invalid field" in check_title:
                    invalid_fields = []
                    total_schemas = 0
                    
                    for data in structured_data:
                        total_schemas += 1
                        schema_type = data.get('@type', '')
                        schema_errors = []
                        
                        # Article/NewsArticle/BlogPosting 검증
                        if schema_type in ['Article', 'NewsArticle', 'BlogPosting']:
                            if not data.get('headline'):
                                schema_errors.append('headline 필수 필드 누락')
                            if not data.get('author'):
                                schema_errors.append('author 필수 필드 누락')
                            if not (data.get('datePublished') or data.get('dateModified')):
                                schema_errors.append('datePublished 또는 dateModified 필수 필드 누락')
                        
                        # Product 스키마 검증
                        elif schema_type == 'Product':
                            if not data.get('name'):
                                schema_errors.append('name 필수 필드 누락')
                            offers = data.get('offers', {})
                            if not offers.get('price') and not offers.get('priceSpecification'):
                                schema_errors.append('offers.price 필수 필드 누락')
                        
                        # BreadcrumbList 검증
                        elif schema_type == 'BreadcrumbList':
                            items = data.get('itemListElement', [])
                            for i, item in enumerate(items):
                                if not item.get('position'):
                                    schema_errors.append(f'BreadcrumbList item {i+1}의 position 필드 누락')
                                if not item.get('name') and not item.get('item', {}).get('name'):
                                    schema_errors.append(f'BreadcrumbList item {i+1}의 name 필드 누락')
                        
                        # FAQPage 검증
                        elif schema_type == 'FAQPage':
                            main_entity = data.get('mainEntity', [])
                            if not main_entity:
                                schema_errors.append('mainEntity 필수 필드 누락')
                            elif isinstance(main_entity, list):
                                for i, faq in enumerate(main_entity):
                                    if not faq.get('name'):
                                        schema_errors.append(f'FAQ {i+1}의 name(질문) 필드 누락')
                                    if not faq.get('acceptedAnswer', {}).get('text'):
                                        schema_errors.append(f'FAQ {i+1}의 acceptedAnswer.text(답변) 필드 누락')
                        
                        # VideoObject 검증
                        elif schema_type == 'VideoObject':
                            if not data.get('name'):
                                schema_errors.append('name 필수 필드 누락')
                            if not data.get('description'):
                                schema_errors.append('description 필수 필드 누락')
                            if not data.get('thumbnailUrl'):
                                schema_errors.append('thumbnailUrl 필수 필드 누락')
                            # Duration 형식 검증
                            duration = data.get('duration')
                            if duration and not ('PT' in str(duration) or 'P' in str(duration)):
                                schema_errors.append('duration이 ISO8601 형식이 아님')
                        
                        # URL 유효성 검증 (모든 스키마 공통)
                        url_fields = ['url', 'image', 'logo', 'thumbnailUrl', 'contentUrl']
                        for field in url_fields:
                            url_value = data.get(field, '')
                            if url_value:
                                # URL 필드가 다양한 형태로 올 수 있으므로 안전하게 처리
                                urls_to_check = []
                                
                                if isinstance(url_value, str):
                                    # 문자열인 경우
                                    urls_to_check = [url_value]
                                elif isinstance(url_value, list):
                                    # 리스트인 경우 (예: "image": ["url1", "url2"])
                                    for item in url_value:
                                        if isinstance(item, str):
                                            urls_to_check.append(item)
                                        elif isinstance(item, dict) and item.get('url'):
                                            urls_to_check.append(item.get('url'))
                                elif isinstance(url_value, dict) and url_value.get('url'):
                                    # 객체인 경우 (예: "image": {"@type": "ImageObject", "url": "..."})
                                    urls_to_check = [url_value.get('url')]
                                
                                # 추출된 URL들을 검증
                                for url_to_check in urls_to_check:
                                    if url_to_check and isinstance(url_to_check, str):
                                        if not (url_to_check.startswith('http://') or url_to_check.startswith('https://')):
                                            schema_errors.append(f'{field}이 올바른 URL 형식이 아님: {url_to_check}')
                        
                        # 날짜 형식 검증
                        date_fields = ['datePublished', 'dateModified', 'startDate', 'endDate', 'validThrough']
                        for field in date_fields:
                            date_value = data.get(field, '')
                            if date_value and not isinstance(date_value, str):
                                schema_errors.append(f'{field}이 문자열 형식이 아님')
                        
                        # 스키마별 오류 수집
                        if schema_errors:
                            invalid_fields.extend([f'{schema_type}: {error}' for error in schema_errors])
                    
                    # 결과 처리
                    if not structured_data:
                        result.update({"message": "구조화 데이터가 없어서 검증할 수 없습니다.", "score": 50})
                    elif invalid_fields:
                        error_count = len(invalid_fields)
                        error_summary = invalid_fields[:3]  # 처음 3개만 표시
                        more_text = f" 외 {error_count-3}개" if error_count > 3 else ""
                        result.update({
                            "message": f"구조화 데이터에서 {error_count}개의 오류 발견: {', '.join(error_summary)}{more_text}",
                            "score": max(0, 100 - (error_count * 15))  # 오류 1개당 15점 감점
                        })
                    else:
                        result.update({
                            "status": "pass", 
                            "message": f"{total_schemas}개의 구조화 데이터에서 필드 오류가 발견되지 않았습니다.",
                            "score": 100
                        })
                else:
                    result.update({"status": "pending", "message": "분석 미지원"})
            except Exception as e:
                result.update({"message": f"분석 중 오류 발생: {str(e)}"})
            
            results.append(result)
        return results

    async def _perform_security_trust_checks(self, checks_config: List[Dict]) -> List[Dict]:
        """'보안 및 신뢰성' 섹션의 보안 헤더 등을 분석합니다."""
        results = []
        
        # ID별 검사 설정과 핸들러 매핑
        check_configs = {
            1: {
                "name": "HTTPS 강제 리다이렉션",
                "handler": self._check_https_redirect,
                "pass_score": 100,
                "warning_score": 70,
                "fail_score": 0
            },
            2: {
                "name": "HSTS max-age ≥31536000",
                "handler": self._check_hsts_header,
                "pass_score": 100,
                "warning_score": 50,
                "fail_score": 0
            },
            3: {
                "name": "TLS 버전 ≥1.2",
                "handler": self._check_tls_version,
                "pass_score": 100,
                "warning_score": 70,
                "fail_score": 30
            },
            4: {
                "name": "Content-Security-Policy 존재",
                "handler": self._check_csp_header,
                "pass_score": 100,
                "fail_score": 0
            },
            5: {
                "name": "X-Frame-Options DENY",
                "handler": self._check_x_frame_options,
                "pass_score": 100,
                "fail_score": 0
            },
            6: {
                "name": "X-Content-Type-Options nosniff",
                "handler": self._check_x_content_type_options,
                "pass_score": 100,
                "fail_score": 0
            },
            9: {
                "name": "SSL Renew ≥30 일",
                "handler": self._check_ssl_renewal,
                "pass_score": 100,
                "warning_score": 50,
                "fail_score": 0
            },
            10: {
                "name": "Mixed Content 0건",
                "handler": self._check_mixed_content,
                "pass_score": 100,
                "fail_score": 0
            }
        }
        
        for item in checks_config:
            result = item.copy()
            result.update({
                "status": "fail", 
                "message": "분석 중 기술적 오류가 발생했습니다", 
                "score": 0,
                "title": item.get("title", "Unknown"),
                "importance": item.get("importance", 1),
                "criteria": item.get("criteria", "분석 중 오류 발생")
            })
            check_title = item.get("title", "").lower()

            try:
                # HTTPS 강제 리다이렉션 검사 (전용 함수 결과를 그대로 반영)
                if "https" in check_title and ("리디렉션" in check_title or "redirect" in check_title):
                    https_redirect_result = await self._check_https_redirect()
                    for k in ("status", "message", "score"):
                        if k in https_redirect_result:
                            result[k] = https_redirect_result[k]

                elif "hsts" in check_title:
                    hsts_result = await self._check_hsts_header()
                    for k in ("status", "message", "score"):
                        if k in hsts_result:
                            result[k] = hsts_result[k]

                elif "content-security-policy" in check_title:
                    if 'Content-Security-Policy' in self.headers:
                        result.update({"status": "pass", "message": "CSP 헤더가 존재하여 XSS 공격 방어에 도움이 됩니다.", "score": 100})
                    else:
                        result.update({"status": "fail", "message": "CSP(Content-Security-Policy) 헤더가 없어 보안이 취약할 수 있습니다.", "score": 0})

                elif "x-frame-options" in check_title:
                    header_val = self.headers.get('X-Frame-Options', '').upper()
                    if header_val in ['DENY', 'SAMEORIGIN']:
                        result.update({"status": "pass", "message": f"X-Frame-Options 헤더가 '{header_val}'로 설정되어 클릭재킹을 방어합니다.", "score": 100})
                    else:
                        result.update({"status": "fail", "message": "X-Frame-Options 헤더가 없어 클릭재킹 공격에 취약할 수 있습니다.", "score": 0})
                
                elif "x-content-type-options" in check_title:
                    if self.headers.get('X-Content-Type-Options', '').lower() == 'nosniff':
                        result.update({"status": "pass", "message": "X-Content-Type-Options 헤더가 'nosniff'로 설정되어 MIME 스니핑을 방지합니다.", "score": 100})
                    else:
                        result.update({"status": "fail", "message": "X-Content-Type-Options 헤더가 설정되지 않았습니다.", "score": 0})

                elif "tls 버전" in check_title or "tls version" in check_title:
                    tls_result = await self._check_tls_version()
                    if tls_result["success"]:
                        if tls_result["grade"] == "A":
                            result.update({"status": "pass", "message": f"TLS 설정이 우수합니다 (SSL Labs 등급: {tls_result['grade']}). 지원되는 프로토콜: {', '.join(tls_result['protocols'])}", "score": 100})
                        elif tls_result["grade"] in ["B", "C"]:
                            result.update({"status": "warning", "message": f"TLS 설정이 양호하지만 개선이 필요합니다 (SSL Labs 등급: {tls_result['grade']}). 지원되는 프로토콜: {', '.join(tls_result['protocols'])}", "score": 70})
                        else:
                            result.update({"status": "fail", "message": f"TLS 설정이 취약합니다 (SSL Labs 등급: {tls_result['grade']}). 지원되는 프로토콜: {', '.join(tls_result['protocols'])}", "score": 30})
                    else:
                        result.update({"status": "fail", "message": f"TLS 분석 실패: {tls_result['message']}", "score": 0})

                elif "ssl renew" in check_title or "ssl 갱신" in check_title:
                    ssl_renew_result = await self._check_ssl_renewal()
                    if ssl_renew_result["success"]:
                        days_remaining = ssl_renew_result["days_remaining"]
                        if days_remaining >= 30:
                            result.update({"status": "pass", "message": f"SSL 인증서가 {days_remaining}일 남아있어 안전합니다. 만료일: {ssl_renew_result['expiry_date']}", "score": 100})
                        elif days_remaining >= 7:
                            result.update({"status": "warning", "message": f"SSL 인증서가 {days_remaining}일 남아있어 곧 갱신이 필요합니다. 만료일: {ssl_renew_result['expiry_date']}", "score": 50})
                        else:
                            result.update({"status": "fail", "message": f"SSL 인증서가 {days_remaining}일 남아있어 긴급 갱신이 필요합니다! 만료일: {ssl_renew_result['expiry_date']}", "score": 0})
                    else:
                        result.update({"status": "fail", "message": f"SSL 인증서 분석 실패: {ssl_renew_result['message']}", "score": 0})

                elif "mixed content" in check_title or "혼합 콘텐츠" in check_title:
                    mixed_content_result = await self._check_mixed_content()
                    if mixed_content_result["success"]:
                        if mixed_content_result["mixed_content_count"] == 0:
                            result.update({"status": "pass", "message": "Mixed Content가 발견되지 않았습니다. 모든 리소스가 HTTPS를 사용합니다.", "score": 100})
                        else:
                            # 간단한 메시지만 표시하고 상세 정보는 별도 저장
                            result.update({
                                "status": "fail", 
                                "message": f"Mixed Content {mixed_content_result['mixed_content_count']}건이 발견되었습니다.", 
                                "score": 0,
                                "mixed_content_details": mixed_content_result.get('details', ''),
                                "mixed_content_count": mixed_content_result.get('mixed_content_count', 0)
                            })
                    else:
                        result.update({"status": "fail", "message": f"Mixed Content 분석 실패: {mixed_content_result['message']}", "score": 0})
                
                else:
                    result.update({"status": "pending", "message": "분석 미지원"})
            except Exception as e:
                result.update({"message": f"알 수 없는 오류: {e}"})
            
            results.append(result)
        
        return results

    async def _check_tls_version(self) -> Dict:
        """TLS 버전 및 SSL 설정을 분석합니다."""
        try:
            # URL에서 도메인 추출
            from urllib.parse import urlparse
            parsed_url = urlparse(self.url)
            domain = parsed_url.netloc
            
            # 먼저 빠른 로컬 TLS 분석 시도
            local_result = await self._check_tls_local(domain)
            if local_result["success"]:
                # 로컬 결과를 새로운 형식으로 변환
                grade = local_result.get("grade", "F")
                if grade == "A":
                    return {
                        "status": "pass",
                        "message": f"TLS 설정이 우수합니다 (등급: {grade}). 지원되는 프로토콜: {', '.join(local_result.get('protocols', []))}",
                        "score": 100
                    }
                elif grade in ["B", "C"]:
                    return {
                        "status": "warning",
                        "message": f"TLS 설정이 양호하지만 개선이 필요합니다 (등급: {grade}). 지원되는 프로토콜: {', '.join(local_result.get('protocols', []))}",
                        "score": 70
                    }
                else:
                    return {
                        "status": "fail",
                        "message": f"TLS 설정이 취약합니다 (등급: {grade}). 지원되는 프로토콜: {', '.join(local_result.get('protocols', []))}",
                        "score": 30
                    }
            
            # 로컬 분석이 실패하면 SSL Labs API 시도 (짧은 타임아웃)
            ssl_labs_result = await self._check_tls_ssl_labs(domain)
            if ssl_labs_result["success"]:
                grade = ssl_labs_result.get("grade", "F")
                if grade == "A":
                    return {
                        "status": "pass",
                        "message": f"TLS 설정이 우수합니다 (SSL Labs 등급: {grade}). 지원되는 프로토콜: {', '.join(ssl_labs_result.get('protocols', []))}",
                        "score": 100
                    }
                elif grade in ["B", "C"]:
                    return {
                        "status": "warning",
                        "message": f"TLS 설정이 양호하지만 개선이 필요합니다 (SSL Labs 등급: {grade}). 지원되는 프로토콜: {', '.join(ssl_labs_result.get('protocols', []))}",
                        "score": 70
                    }
                else:
                    return {
                        "status": "fail",
                        "message": f"TLS 설정이 취약합니다 (SSL Labs 등급: {grade}). 지원되는 프로토콜: {', '.join(ssl_labs_result.get('protocols', []))}",
                        "score": 30
                    }
            else:
                return {
                    "status": "fail",
                    "message": f"TLS 분석 실패: {ssl_labs_result.get('message', '알 수 없는 오류')}",
                    "score": 0
                }
                
        except Exception as e:
            return {
                "status": "fail",
                "message": f"TLS 분석 중 오류 발생: {str(e)}",
                "score": 0
            }

    async def _check_tls_local(self, domain: str) -> Dict:
        """로컬 TLS 연결을 통한 빠른 분석"""
        try:
            import ssl
            import socket
            
            # 기본 HTTPS 포트로 연결
            context = ssl.create_default_context()
            context.check_hostname = False
            context.verify_mode = ssl.CERT_NONE
            
            with socket.create_connection((domain, 443), timeout=10) as sock:
                with context.wrap_socket(sock, server_hostname=domain) as ssock:
                    # TLS 버전 정보 가져오기
                    version = ssock.version()
                    cipher = ssock.cipher()
                    
                    # 지원되는 프로토콜 확인
                    protocols = []
                    tls_12_supported = False
                    
                    # TLS 버전별 테스트
                    for tls_version in [ssl.TLSVersion.TLSv1_2, ssl.TLSVersion.TLSv1_3]:
                        try:
                            test_context = ssl.SSLContext(ssl.PROTOCOL_TLS)
                            test_context.minimum_version = tls_version
                            test_context.maximum_version = tls_version
                            test_context.check_hostname = False
                            test_context.verify_mode = ssl.CERT_NONE
                            
                            with socket.create_connection((domain, 443), timeout=5) as test_sock:
                                with test_context.wrap_socket(test_sock, server_hostname=domain) as test_ssock:
                                    if tls_version == ssl.TLSVersion.TLSv1_2:
                                        protocols.append("TLS 1.2")
                                        tls_12_supported = True
                                    elif tls_version == ssl.TLSVersion.TLSv1_3:
                                        protocols.append("TLS 1.3")
                                        tls_12_supported = True
                        except (ssl.SSLError, socket.timeout, ConnectionRefusedError, OSError) as e:
                            # SSL 관련 오류는 정상적인 상황 (해당 TLS 버전 미지원)
                            print(f"TLS {tls_version} 연결 실패: {e}")
                            continue
                        except Exception as e:
                            # 예상치 못한 오류는 로깅
                            print(f"TLS {tls_version} 검사 중 예상치 못한 오류: {e}")
                            continue
                    
                    # 등급 결정
                    if tls_12_supported and len(protocols) >= 2:
                        grade = "A"
                    elif tls_12_supported:
                        grade = "B"
                    else:
                        grade = "F"
                    
                    return {
                        "success": True,
                        "grade": grade,
                        "protocols": protocols,
                        "tls_12_supported": tls_12_supported,
                        "current_version": version,
                        "cipher": cipher[0] if cipher else "Unknown"
                    }
                    
        except Exception as e:
            return {
                "success": False,
                "message": f"로컬 TLS 분석 실패: {str(e)}"
            }

    async def _check_tls_ssl_labs(self, domain: str) -> Dict:
        """SSL Labs API를 사용한 상세 분석 (짧은 타임아웃)"""
        try:
            import httpx
            import json
            
            # SSL Labs API 엔드포인트
            ssl_labs_api = f"https://api.ssllabs.com/api/v3/analyze?host={domain}&all=done"
            
            async with httpx.AsyncClient(timeout=15) as client:  # 타임아웃 단축
                # SSL Labs 분석 시작
                start_response = await client.get(ssl_labs_api)
                if start_response.status_code != 200:
                    return {
                        "success": False,
                        "message": f"SSL Labs API 접근 실패: {start_response.status_code}"
                    }
                
                # 분석 결과 대기 (최대 30초로 단축)
                max_attempts = 6  # 6번 시도 (30초)
                for attempt in range(max_attempts):
                    await asyncio.sleep(5)  # 5초 대기
                    
                    response = await client.get(ssl_labs_api)
                    if response.status_code == 200:
                        data = response.json()
                        
                        if data.get('status') == 'READY':
                            endpoints = data.get('endpoints', [])
                            if endpoints:
                                endpoint = endpoints[0]
                                grade = endpoint.get('grade', 'T')
                                protocols = endpoint.get('details', {}).get('protocols', [])
                                
                                # 지원되는 TLS 버전 확인
                                supported_protocols = []
                                tls_12_supported = False
                                
                                for protocol in protocols:
                                    protocol_name = protocol.get('name', '')
                                    version = protocol.get('version', '')
                                    if protocol_name and version:
                                        supported_protocols.append(f"{protocol_name} {version}")
                                        if protocol_name == 'TLS' and version in ['1.2', '1.3']:
                                            tls_12_supported = True
                                
                                # 결과 반환
                                return {
                                    "success": True,
                                    "grade": grade,
                                    "protocols": supported_protocols,
                                    "tls_12_supported": tls_12_supported,
                                    "details": endpoint.get('details', {})
                                }
                            else:
                                return {
                                    "success": False,
                                    "message": "SSL Labs 분석 결과에서 엔드포인트 정보를 찾을 수 없습니다."
                                }
                        elif data.get('status') == 'ERROR':
                            return {
                                "success": False,
                                "message": f"SSL Labs 분석 오류: {data.get('statusMessage', '알 수 없는 오류')}"
                            }
                        # 아직 분석 중이면 계속 대기
                
                return {
                    "success": False,
                    "message": "SSL Labs 분석 시간 초과 (30초)"
                }
                
        except Exception as e:
            return {
                "success": False,
                "message": f"SSL Labs 분석 중 오류 발생: {str(e)}"
            }

    async def _check_ssl_renewal(self) -> Dict:
        """SSL 인증서 갱신 기간을 확인합니다."""
        try:
            from urllib.parse import urlparse
            import ssl
            import socket
            from datetime import datetime, timezone
            import subprocess
            import re
            import asyncio
            
            # URL에서 도메인 추출
            parsed_url = urlparse(self.url)
            domain = parsed_url.netloc
            
            # 먼저 OpenSSL 명령어로 시도 (더 안정적)
            openssl_result = await self._check_ssl_with_openssl(domain)
            if openssl_result["success"]:
                days_remaining = openssl_result["days_remaining"]
                if days_remaining >= 30:
                    return {
                        "status": "pass",
                        "message": f"SSL 인증서가 {days_remaining}일 남아있어 안전합니다. 만료일: {openssl_result['expiry_date']}",
                        "score": 100
                    }
                elif days_remaining >= 7:
                    return {
                        "status": "warning",
                        "message": f"SSL 인증서가 {days_remaining}일 남아있어 곧 갱신이 필요합니다. 만료일: {openssl_result['expiry_date']}",
                        "score": 50
                    }
                else:
                    return {
                        "status": "fail",
                        "message": f"SSL 인증서가 {days_remaining}일 남아있어 긴급 갱신이 필요합니다! 만료일: {openssl_result['expiry_date']}",
                        "score": 0
                    }
            
            # OpenSSL이 실패하면 Python ssl 모듈로 시도
            python_result = await self._check_ssl_with_python(domain)
            if python_result["success"]:
                days_remaining = python_result["days_remaining"]
                if days_remaining >= 30:
                    return {
                        "status": "pass",
                        "message": f"SSL 인증서가 {days_remaining}일 남아있어 안전합니다. 만료일: {python_result['expiry_date']}",
                        "score": 100
                    }
                elif days_remaining >= 7:
                    return {
                        "status": "warning",
                        "message": f"SSL 인증서가 {days_remaining}일 남아있어 곧 갱신이 필요합니다. 만료일: {python_result['expiry_date']}",
                        "score": 50
                    }
                else:
                    return {
                        "status": "fail",
                        "message": f"SSL 인증서가 {days_remaining}일 남아있어 긴급 갱신이 필요합니다! 만료일: {python_result['expiry_date']}",
                        "score": 0
                    }
            else:
                return {
                    "status": "fail",
                    "message": f"SSL 인증서 분석 실패: {python_result.get('message', '알 수 없는 오류')}",
                    "score": 0
                }
                        
        except Exception as e:
            return {
                "status": "fail",
                "message": f"SSL 인증서 분석 중 오류 발생: {str(e)}",
                "score": 0
            }

    async def _check_ssl_with_openssl(self, domain: str) -> Dict:
        """OpenSSL 명령어를 사용하여 SSL 인증서 정보를 가져옵니다."""
        try:
            # OpenSSL 명령어 실행
            cmd = f"echo | openssl s_client -servername {domain} -connect {domain}:443 2>/dev/null | openssl x509 -noout -dates"
            
            process = await asyncio.create_subprocess_shell(
                cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            stdout, stderr = await asyncio.wait_for(process.communicate(), timeout=15)
            
            if process.returncode != 0:
                return {
                    "success": False,
                    "message": f"OpenSSL 명령어 실행 실패: {stderr.decode()}"
                }
            
            output = stdout.decode()
            
            # notAfter 날짜 추출
            not_after_match = re.search(r'notAfter=([^\n]+)', output)
            if not not_after_match:
                return {
                    "success": False,
                    "message": "OpenSSL에서 만료일 정보를 찾을 수 없습니다."
                }
            
            not_after_str = not_after_match.group(1).strip()
            
            # 날짜 파싱
            try:
                # OpenSSL 날짜 형식: Aug 15 12:00:00 2024 GMT
                expiry_date = datetime.strptime(not_after_str, '%b %d %H:%M:%S %Y %Z')
                
                # 현재 시간과 비교
                now = datetime.now(timezone.utc)
                expiry_date = expiry_date.replace(tzinfo=timezone.utc)
                
                # 남은 일수 계산
                time_remaining = expiry_date - now
                days_remaining = time_remaining.days
                
                # 만료일을 읽기 쉬운 형식으로 변환
                expiry_date_str = expiry_date.strftime('%Y년 %m월 %d일')
                
                return {
                    "success": True,
                    "days_remaining": days_remaining,
                    "expiry_date": expiry_date_str,
                    "method": "OpenSSL"
                }
                
            except ValueError as e:
                return {
                    "success": False,
                    "message": f"OpenSSL 날짜 파싱 오류: {str(e)}"
                }
                
        except asyncio.TimeoutError:
            return {
                "success": False,
                "message": "OpenSSL 명령어 실행 시간 초과"
            }
        except Exception as e:
            return {
                "success": False,
                "message": f"OpenSSL 분석 오류: {str(e)}"
            }

    async def _check_ssl_with_python(self, domain: str) -> Dict:
        """Python ssl 모듈을 사용하여 SSL 인증서 정보를 가져옵니다."""
        try:
            import socket
            import ssl
            import httpx
            from datetime import datetime, timezone
            
            # 먼저 HTTPS 요청으로 최종 URL 확인
            final_url = None
            try:
                async with httpx.AsyncClient(timeout=10, follow_redirects=True) as client:
                    response = await client.get(f"https://{domain}")
                    final_url = str(response.url)
                    print(f"최종 HTTPS URL: {final_url}")
                    
                    # HTTP 요청을 통해 SSL 인증서 정보 확인 시도
                    try:
                        # httpx를 사용하여 SSL 인증서 정보 가져오기
                        async with httpx.AsyncClient(verify=False, timeout=10) as ssl_client:
                            ssl_response = await ssl_client.get(f"https://{domain}")
                            print(f"HTTP 요청을 통한 SSL 연결 성공: {ssl_response.status_code}")
                            # httpx에서는 SSL 인증서 정보를 직접 가져올 수 없으므로
                            # 별도의 SSL 연결을 통해 확인
                    except Exception as ssl_e:
                        print(f"HTTP 요청을 통한 SSL 인증서 확인 실패: {str(ssl_e)}")
                        
            except Exception as e:
                print(f"HTTPS 리다이렉트 확인 실패: {str(e)}")
            
            # 최종 URL에서 도메인 추출
            if final_url:
                from urllib.parse import urlparse
                parsed_url = urlparse(final_url)
                target_domain = parsed_url.netloc
                target_path = parsed_url.path
                if target_domain != domain or target_path != '/':
                    print(f"리다이렉트된 URL: {domain} → {target_domain}{target_path}")
                    # 리다이렉트된 도메인으로 SSL 연결 시도
                    domain = target_domain
                    print(f"SSL 분석 대상 도메인: {domain}")
            
            # SSL 설정 - 더 간단하고 안정적인 설정
            ssl_configs = [
                {
                    "name": "기본 TLS",
                    "context": ssl.create_default_context(),
                    "options": []
                }
            ]
            
            # 기본 HTTPS 포트만 시도
            port = 443
            
            for config in ssl_configs:
                try:
                    context = config["context"]
                    context.check_hostname = False
                    context.verify_mode = ssl.CERT_NONE
                    
                    # SSL 옵션 설정
                    for option in config["options"]:
                        context.options |= option
                    
                    # 연결 시도
                    print(f"SSL 연결 시도 중: {domain}:{port} ({config['name']})")
                    with socket.create_connection((domain, port), timeout=15) as sock:
                        print(f"TCP 연결 성공: {domain}:{port}")
                        with context.wrap_socket(sock, server_hostname=domain) as ssock:
                            print(f"SSL 핸드셰이크 성공: {ssock.version()}")
                            
                            # 인증서 정보 가져오기
                            try:
                                cert = ssock.getpeercert()
                                if cert:
                                    print(f"SSL 인증서 발견 ({config['name']}): {cert.get('subject', {})}")
                                    return await self._parse_ssl_certificate(cert, f"Python SSL ({config['name']}, 포트 {port})")
                                else:
                                    print(f"SSL 인증서 없음 ({config['name']}) - 서버가 인증서를 제공하지 않음")
                            except Exception as cert_e:
                                print(f"인증서 정보 가져오기 실패: {str(cert_e)}")
                                
                            # 대안: 인증서 체인 정보 확인
                            try:
                                cert_chain = ssock.getpeercert(binary_form=True)
                                if cert_chain:
                                    print(f"SSL 인증서 체인 발견 ({config['name']}): {len(cert_chain)} 바이트")
                                    # 바이너리 인증서를 파싱하여 정보 추출
                                    try:
                                        import ssl
                                        from cryptography import x509
                                        from cryptography.hazmat.backends import default_backend
                                        
                                        # 바이너리 인증서를 파싱
                                        cert = x509.load_der_x509_certificate(cert_chain, default_backend())
                                        
                                        # 인증서 정보 추출
                                        subject = {}
                                        for name in cert.subject:
                                            subject[str(name.oid)] = str(name.value)
                                        
                                        issuer = {}
                                        for name in cert.issuer:
                                            issuer[str(name.oid)] = str(name.value)
                                        
                                        not_after = cert.not_valid_after
                                        
                                        print(f"SSL 인증서 파싱 성공 ({config['name']}): {subject}")
                                        
                                        # 인증서 정보를 딕셔너리 형태로 변환
                                        cert_dict = {
                                            'subject': subject,
                                            'issuer': issuer,
                                            'notAfter': not_after.strftime('%b %d %H:%M:%S %Y'),
                                            'version': cert.version,
                                            'serialNumber': str(cert.serial_number)
                                        }
                                        
                                        return await self._parse_ssl_certificate(cert_dict, f"Python SSL ({config['name']}, 포트 {port})")
                                        
                                    except ImportError:
                                        print("cryptography 라이브러리가 설치되지 않음 - 바이너리 인증서 파싱 불가")
                                    except Exception as parse_e:
                                        print(f"바이너리 인증서 파싱 실패: {str(parse_e)}")
                                else:
                                    print(f"SSL 인증서 체인 없음 ({config['name']})")
                            except Exception as chain_e:
                                print(f"인증서 체인 정보 가져오기 실패: {str(chain_e)}")
                            
                except (socket.gaierror, socket.timeout, ssl.SSLError, ConnectionRefusedError, OSError) as e:
                    # 더 자세한 오류 정보 로깅
                    print(f"SSL 연결 시도 실패 ({config['name']}): {str(e)}")
                    continue  # 다음 설정 시도
                except Exception as e:
                    print(f"SSL 분석 중 예상치 못한 오류 ({config['name']}): {str(e)}")
                    continue  # 다음 설정 시도
            
            # 모든 시도에서 실패한 경우, 상세한 진단 정보 제공
            diagnostic_info = await self._get_ssl_diagnostic_info(domain)
            
            return {
                "success": False,
                "message": f"모든 SSL 설정에서 인증서 정보를 가져올 수 없습니다. 진단 정보: {diagnostic_info}",
                "diagnostic": diagnostic_info
            }
                        
        except Exception as e:
            return {
                "success": False,
                "message": f"Python SSL 분석 중 오류 발생: {str(e)}"
            }

    async def _parse_ssl_certificate(self, cert: dict, method: str) -> Dict:
        """SSL 인증서 정보를 파싱합니다."""
        try:
            from datetime import datetime, timezone
            
            # 인증서 만료일 파싱
            not_after = cert.get('notAfter')
            if not not_after:
                return {
                    "success": False,
                    "message": "인증서 만료일 정보 없음"
                }
            
            # 만료일을 datetime 객체로 변환
            try:
                # 다양한 날짜 형식 처리
                expiry_date = None
                date_formats = [
                    '%b %d %H:%M:%S %Y',
                    '%b %d %H:%M:%S %Y %Z',
                    '%b %d %H:%M:%S %Y GMT',
                    '%Y-%m-%d %H:%M:%S',
                    '%Y%m%d%H%M%SZ'
                ]
                
                for fmt in date_formats:
                    try:
                        expiry_date = datetime.strptime(not_after.strip(), fmt)
                        break
                    except ValueError:
                        continue
                
                if not expiry_date:
                    # 기본 파싱 시도 (timezone 없이)
                    expiry_date = datetime.strptime(not_after.strip(), '%b %d %H:%M:%S %Y')
                
                # 현재 시간과 비교
                now = datetime.now(timezone.utc)
                if expiry_date.tzinfo is None:
                    # timezone 정보가 없으면 UTC로 가정
                    expiry_date = expiry_date.replace(tzinfo=timezone.utc)
                
                # 남은 일수 계산
                time_remaining = expiry_date - now
                days_remaining = time_remaining.days
                
                # 만료일을 읽기 쉬운 형식으로 변환
                expiry_date_str = expiry_date.strftime('%Y년 %m월 %d일')
                
                return {
                    "success": True,
                    "days_remaining": days_remaining,
                    "expiry_date": expiry_date_str,
                    "cert_issuer": cert.get('issuer', {}),
                    "cert_subject": cert.get('subject', {}),
                    "serial_number": cert.get('serialNumber', ''),
                    "version": cert.get('version', ''),
                    "method": method
                }
                
            except ValueError as e:
                return {
                    "success": False,
                    "message": f"인증서 날짜 파싱 실패: {str(e)}"
                }
                
        except Exception as e:
            return {
                "success": False,
                "message": f"SSL 인증서 파싱 중 오류 발생: {str(e)}"
            }

    async def _get_ssl_diagnostic_info(self, domain: str) -> str:
        """SSL 연결 문제 진단 정보를 수집합니다."""
        try:
            import socket
            import httpx
            
            diagnostic_results = []
            
            # 1. DNS 해석 확인
            try:
                ip = socket.gethostbyname(domain)
                diagnostic_results.append(f"DNS 해석 성공: {domain} → {ip}")
            except socket.gaierror:
                diagnostic_results.append(f"DNS 해석 실패: {domain}")
            
            # 2. HTTP 응답 확인
            try:
                async with httpx.AsyncClient(timeout=10) as client:
                    response = await client.get(f"http://{domain}")
                    diagnostic_results.append(f"HTTP 응답: {response.status_code}")
                    if response.status_code in [301, 302]:
                        location = response.headers.get('location', '')
                        diagnostic_results.append(f"리다이렉트: {location}")
            except Exception as e:
                diagnostic_results.append(f"HTTP 요청 실패: {str(e)}")
            
            # 3. HTTPS 응답 확인
            try:
                async with httpx.AsyncClient(timeout=10) as client:
                    response = await client.get(f"https://{domain}")
                    diagnostic_results.append(f"HTTPS 응답: {response.status_code}")
            except Exception as e:
                diagnostic_results.append(f"HTTPS 요청 실패: {str(e)}")
            
            # 4. 포트 연결 확인
            port = 443
            try:
                sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                sock.settimeout(10)
                result = sock.connect_ex((domain, port))
                sock.close()
                
                if result == 0:
                    diagnostic_results.append(f"포트 {port} 연결 가능")
                else:
                    diagnostic_results.append(f"포트 {port} 연결 불가 (오류 코드: {result})")
            except Exception as e:
                diagnostic_results.append(f"포트 {port} 테스트 실패: {str(e)}")
            
            # 5. SSL 연결 시도
            try:
                import ssl
                context = ssl.create_default_context()
                context.check_hostname = False
                context.verify_mode = ssl.CERT_NONE
                
                with socket.create_connection((domain, port), timeout=10) as sock:
                    with context.wrap_socket(sock, server_hostname=domain) as ssock:
                        cert = ssock.getpeercert()
                        if cert:
                            diagnostic_results.append("SSL 인증서 확인 성공")
                            # 인증서 정보 추가
                            subject = cert.get('subject', {})
                            if subject:
                                cn = None
                                for item in subject:
                                    if item[0][0] == 'commonName':
                                        cn = item[0][1]
                                        break
                                if cn:
                                    diagnostic_results.append(f"인증서 주체: {cn}")
                        else:
                            diagnostic_results.append("SSL 인증서 없음")
            except Exception as e:
                diagnostic_results.append(f"SSL 연결 실패: {str(e)}")
            
            return "; ".join(diagnostic_results)
            
        except Exception as e:
            return f"진단 정보 수집 실패: {str(e)}"

    async def _check_https_redirect(self) -> Dict:
        """HTTPS 강제 리다이렉션을 검사합니다."""
        try:
            # 현재 URL이 HTTPS인지 확인
            if not self.url.startswith('https://'):
                return {
                    "status": "fail",
                    "message": "현재 URL이 HTTPS가 아닙니다.",
                    "score": 0
                }
            
            # HTTP URL로 테스트 요청 보내기
            http_url = self.url.replace('https://', 'http://')
            
            # httpx를 사용하여 비동기 요청
            import httpx
            async with httpx.AsyncClient(follow_redirects=False, timeout=10) as client:
                try:
                    response = await client.get(http_url)
                    
                    # 첫 번째 응답이 리다이렉트인지 확인
                    if response.status_code in [301, 302, 303, 307, 308]:
                        location_header = response.headers.get('location', '')
                        if location_header.startswith('https://'):
                            if response.status_code == 301:
                                return {
                                    "status": "pass",
                                    "message": "HTTPS 강제 리다이렉션이 301 상태코드로 올바르게 설정되었습니다.",
                                    "score": 100
                                }
                            else:
                                return {
                                    "status": "warning",
                                    "message": f"HTTPS 리다이렉션이 {response.status_code} 상태코드로 설정되었습니다. 301을 권장합니다.",
                                    "score": 70
                                }
                        else:
                            return {
                                "status": "fail",
                                "message": f"리다이렉트가 HTTPS가 아닙니다: {location_header}",
                                "score": 0
                            }
                    else:
                        return {
                            "status": "fail",
                            "message": f"HTTP 요청이 리다이렉트되지 않았습니다 (상태코드: {response.status_code})",
                            "score": 0
                        }
                        
                except httpx.RequestError as e:
                    # HTTP 요청 자체가 실패한 경우 (예: HTTP 서비스가 없는 경우)
                    return {
                        "status": "fail",
                        "message": f"HTTP 요청 실패: {e}",
                        "score": 0
                    }
                    
        except Exception as e:
            return {
                "status": "fail",
                "message": f"검사 중 오류 발생: {e}",
                "score": 0
            }

    async def _check_hsts_header(self) -> Dict:
        """HSTS 헤더를 검사합니다."""
        try:
            hsts_header = self.headers.get('Strict-Transport-Security')
            if hsts_header:
                max_age_match = re.search(r'max-age=(\d+)', hsts_header)
                if max_age_match and int(max_age_match.group(1)) >= 31536000:
                    return {
                        "status": "pass",
                        "message": "HSTS 헤더가 1년 이상으로 강력하게 설정되었습니다.",
                        "score": 100
                    }
                else:
                    return {
                        "status": "warning",
                        "message": "HSTS 헤더가 존재하지만 max-age가 1년 미만입니다.",
                        "score": 50
                    }
            else:
                return {
                    "status": "fail",
                    "message": "HSTS(Strict-Transport-Security) 헤더가 없습니다.",
                    "score": 0
                }
        except Exception as e:
            return {
                "status": "fail",
                "message": f"HSTS 헤더 검사 중 오류: {str(e)}",
                "score": 0
            }

    async def _check_csp_header(self) -> Dict:
        """Content-Security-Policy 헤더를 검사합니다."""
        try:
            if 'Content-Security-Policy' in self.headers:
                return {
                    "status": "pass",
                    "message": "CSP 헤더가 존재하여 XSS 공격 방어에 도움이 됩니다.",
                    "score": 100
                }
            else:
                return {
                    "status": "fail",
                    "message": "CSP(Content-Security-Policy) 헤더가 없어 보안이 취약할 수 있습니다.",
                    "score": 0
                }
        except Exception as e:
            return {
                "status": "fail",
                "message": f"CSP 헤더 검사 중 오류: {str(e)}",
                "score": 0
            }

    async def _check_x_frame_options(self) -> Dict:
        """X-Frame-Options 헤더를 검사합니다."""
        try:
            header_val = self.headers.get('X-Frame-Options', '').upper()
            if header_val in ['DENY', 'SAMEORIGIN']:
                return {
                    "status": "pass",
                    "message": f"X-Frame-Options 헤더가 '{header_val}'로 설정되어 클릭재킹을 방어합니다.",
                    "score": 100
                }
            else:
                return {
                    "status": "fail",
                    "message": "X-Frame-Options 헤더가 없어 클릭재킹 공격에 취약할 수 있습니다.",
                    "score": 0
                }
        except Exception as e:
            return {
                "status": "fail",
                "message": f"X-Frame-Options 헤더 검사 중 오류: {str(e)}",
                "score": 0
            }

    async def _check_x_content_type_options(self) -> Dict:
        """X-Content-Type-Options 헤더를 검사합니다."""
        try:
            if self.headers.get('X-Content-Type-Options', '').lower() == 'nosniff':
                return {
                    "status": "pass",
                    "message": "X-Content-Type-Options 헤더가 'nosniff'로 설정되어 MIME 스니핑을 방지합니다.",
                    "score": 100
                }
            else:
                return {
                    "status": "fail",
                    "message": "X-Content-Type-Options 헤더가 설정되지 않았습니다.",
                    "score": 0
                }
        except Exception as e:
            return {
                "status": "fail",
                "message": f"X-Content-Type-Options 헤더 검사 중 오류: {str(e)}",
                "score": 0
            }

    def _perform_web_performance_checks(self, checks_config: List[Dict]) -> List[Dict]:
        """'웹 성능 및 기술적 최적화' 섹션을 PageSpeed Insights 결과 또는 기본 메트릭으로 분석합니다."""
        results = []
        # PageSpeed Insights 결과 또는 기본 메트릭에서 데이터를 가져옵니다.
        lighthouse_result = self.pagespeed_results.get('lighthouseResult', {})
        metrics = lighthouse_result.get('audits', {})
        basic_metrics = self.pagespeed_results.get('basicMetrics', {})
        
        for item in checks_config:
            result = item.copy()
            result.update({"status": "fail", "message": "성능 데이터를 가져올 수 없습니다.", "score": 0})
            check_title = item.get("title", "").lower()

            try:
                if "lcp" in check_title:
                    lcp_metric = metrics.get('largest-contentful-paint', {})
                    lcp_value = lcp_metric.get('numericValue', 0) / 1000
                    
                    # 기본 메트릭이 있는 경우 페이지 로드 시간을 대체로 사용
                    if lcp_value == 0 and basic_metrics:
                        page_load_time = basic_metrics.get('pageLoadTime', 0) / 1000
                        if page_load_time > 0:
                            lcp_value = page_load_time * 0.6  # 일반적으로 LCP는 전체 로드 시간의 60% 정도
                    
                    if lcp_value > 0 and lcp_value <= 2.5:
                        result.update({"status": "pass", "message": f"LCP가 {lcp_value:.2f}초로 우수합니다.", "score": 100})
                    else:
                        result.update({"message": f"LCP가 {lcp_value:.2f}초로 개선이 필요합니다 (권장: 2.5초 이하).", "score": 30 if lcp_value > 4 else 60})

                elif "inp" in check_title:
                    # PageSpeed API v5는 INP를 직접 제공하지 않음. TBT로 대체하여 유사하게 판단.
                    tbt_metric = metrics.get('total-blocking-time', {})
                    tbt_value = tbt_metric.get('numericValue', 0)
                    
                    # 기본 메트릭이 있는 경우 DOM 로드 시간을 대체로 사용
                    if tbt_value == 0 and basic_metrics:
                        dom_load_time = basic_metrics.get('domContentLoaded', 0)
                        if dom_load_time > 0:
                            tbt_value = dom_load_time * 0.3  # 일반적으로 TBT는 DOM 로드 시간의 30% 정도
                    
                    if tbt_value <= 200:
                         result.update({"status": "pass", "message": f"TBT가 {tbt_value:.0f}ms로 우수하여 INP도 양호할 것으로 예상됩니다.", "score": 100})
                    else:
                         result.update({"message": f"TBT가 {tbt_value:.0f}ms로 높아 INP 개선이 필요할 수 있습니다 (권장: 200ms 이하).", "score": 30 if tbt_value > 600 else 60})

                elif "cls" in check_title:
                    cls_metric = metrics.get('cumulative-layout-shift', {})
                    cls_value = cls_metric.get('numericValue', 0)
                    
                    # 기본 메트릭이 있는 경우 이미지 수를 기반으로 추정
                    if cls_value == 0 and basic_metrics:
                        image_count = basic_metrics.get('imageCount', 0)
                        if image_count > 10:
                            cls_value = 0.15  # 이미지가 많으면 CLS가 높을 가능성
                        elif image_count > 5:
                            cls_value = 0.1   # 중간 정도
                        else:
                            cls_value = 0.05  # 이미지가 적으면 CLS가 낮을 가능성
                    
                    if cls_value < 0.1:
                        result.update({"status": "pass", "message": f"CLS가 {cls_value:.3f}로 매우 안정적입니다.", "score": 100})
                    else:
                        result.update({"message": f"CLS가 {cls_value:.3f}로 레이아웃 불안정성이 높습니다 (권장: 0.1 미만).", "score": 30 if cls_value > 0.25 else 60})

                elif "ttfb" in check_title:
                    ttfb_metric = metrics.get('server-response-time', {})
                    ttfb_value = ttfb_metric.get('numericValue', 0) / 1000
                    
                    # 기본 메트릭이 있는 경우 DOM 로드 시간을 대체로 사용
                    if ttfb_value == 0 and basic_metrics:
                        dom_load_time = basic_metrics.get('domContentLoaded', 0) / 1000
                        if dom_load_time > 0:
                            ttfb_value = dom_load_time * 0.4  # 일반적으로 TTFB는 DOM 로드 시간의 40% 정도
                    
                    if ttfb_value > 0 and ttfb_value <= 0.8:
                        result.update({"status": "pass", "message": f"TTFB가 {ttfb_value:.3f}초로 빠릅니다.", "score": 100})
                    else:
                        result.update({"message": f"TTFB가 {ttfb_value:.3f}초로 느립니다 (권장: 0.8초 이하).", "score": 40})

                elif "fcp" in check_title:
                    # First Contentful Paint (초기 콘텐츠 표시 시간)
                    fcp_metric = metrics.get('first-contentful-paint', {})
                    fcp_value = fcp_metric.get('numericValue', 0) / 1000  # ms → s

                    # 기본 메트릭이 있는 경우 DOMContentLoaded를 기반으로 근사치 계산
                    if fcp_value == 0 and basic_metrics:
                        try:
                            dcl_sec = basic_metrics.get('domContentLoaded', 0) / 1000
                        except Exception:
                            dcl_sec = 0
                        if dcl_sec > 0:
                            fcp_value = max(0.1, dcl_sec * 0.5)

                    if fcp_value > 0 and fcp_value <= 1.8:
                        result.update({"status": "pass", "message": f"FCP가 {fcp_value:.2f}초로 우수합니다.", "score": 100})
                    elif fcp_value > 0 and fcp_value <= 3.0:
                        result.update({"status": "warning", "message": f"FCP가 {fcp_value:.2f}초로 개선 여지가 있습니다 (권장: 1.8초 이하).", "score": 60})
                    else:
                        result.update({"message": f"FCP 데이터를 확인할 수 없거나 값이 높습니다 (측정값: {fcp_value:.2f}s).", "score": 30 if fcp_value > 3.0 else 0})
                
                # ✅ 모바일 친화성 검사 로직 추가
                elif "모바일 친화성" in check_title or "viewport" in check_title:
                    # soup에서 직접 viewport 태그를 확인
                    viewport_tag = self.soup.find('meta', attrs={'name': 'viewport'})
                    if viewport_tag and 'width=device-width' in viewport_tag.get('content', ''):
                         result.update({"status": "pass", "message": "Viewport 메타 태그가 올바르게 설정되었습니다.", "score": 100})
                    else:
                         result.update({"message": "페이지가 모바일 친화적이지 않거나 Viewport 설정이 올바르지 않습니다.", "score": 20})
                
                # ✅ 추가 성능 메트릭들
                elif "페이지 크기" in check_title:
                    if basic_metrics:
                        total_size_kb = basic_metrics.get('totalSizeKB', 0)
                        if total_size_kb <= 500:  # 500KB 이하
                            result.update({"status": "pass", "message": f"페이지 크기가 {total_size_kb:.1f}KB로 적절합니다.", "score": 100})
                        elif total_size_kb <= 1000:  # 1MB 이하
                            result.update({"status": "warning", "message": f"페이지 크기가 {total_size_kb:.1f}KB로 다소 큽니다.", "score": 70})
                        else:
                            result.update({"message": f"페이지 크기가 {total_size_kb:.1f}KB로 너무 큽니다 (500KB 이하 권장).", "score": 30})
                    else:
                        result.update({"message": "페이지 크기 정보를 가져올 수 없습니다."})
                
                elif "이미지 최적화" in check_title:
                    if basic_metrics:
                        image_size_kb = basic_metrics.get('imageSizeKB', 0)
                        total_size_kb = basic_metrics.get('totalSizeKB', 1)
                        image_ratio = (image_size_kb / total_size_kb) * 100 if total_size_kb > 0 else 0
                        
                        if image_ratio <= 50:  # 이미지가 전체의 50% 이하
                            result.update({"status": "pass", "message": f"이미지 크기가 전체의 {image_ratio:.1f}%로 적절합니다.", "score": 100})
                        elif image_ratio <= 70:
                            result.update({"status": "warning", "message": f"이미지 크기가 전체의 {image_ratio:.1f}%로 다소 큽니다.", "score": 70})
                        else:
                            result.update({"message": f"이미지 크기가 전체의 {image_ratio:.1f}%로 너무 큽니다 (50% 이하 권장).", "score": 30})
                    else:
                        result.update({"message": "이미지 크기 정보를 가져올 수 없습니다."})
                
                else:
                    result.update({"status": "pending", "message": "분석 미지원"})
            except Exception as e:
                result.update({"message": f"알 수 없는 오류: {e}"})
            
            results.append(result)
        return results

    async def _perform_url_content_standardization_checks(self, checks_config: List[Dict]) -> List[Dict]:
        """'URL 및 콘텐츠 표준화' 섹션을 전용 분석기와 AI 분석기로 분석합니다."""
        try:
            # URL 콘텐츠 분석기 초기화
            url_content_analyzer = URLContentAnalyzer(self.url, self.soup, self.headers)
            
            # 기술적 분석 실행
            technical_results = await url_content_analyzer.analyze_url_content_standardization(checks_config)
            
            # AI 분석 실행 (웹사이트 데이터 수집 후)
            ai_results = None
            if self.ai_analyzer:
                try:
                    # 웹사이트 데이터 수집
                    website_data = await self._collect_website_data_for_ai()
                    
                    # AI 분석 수행
                    ai_analysis = await self.ai_analyzer.analyze_url_content_standardization(self.url, website_data)
                    
                    if not ai_analysis.get("error"):
                        ai_results = ai_analysis
                        print(f"🤖 URL 콘텐츠 표준화 AI 분석 완료")
                    else:
                        print(f"⚠️ URL 콘텐츠 표준화 AI 분석 실패: {ai_analysis.get('error')}")
                        
                except Exception as e:
                    print(f"⚠️ URL 콘텐츠 표준화 AI 분석 오류: {e}")
            
            # 기술적 분석과 AI 분석 결과 결합
            combined_results = self._combine_technical_and_ai_results(technical_results, ai_results, checks_config)
            
            print(f"🔍 URL 콘텐츠 표준화 분석 완료: {len(combined_results)}개 항목")
            return combined_results
            
        except Exception as e:
            print(f"❌ URL 콘텐츠 표준화 분석 오류: {e}")
            # 오류 발생 시 기본 결과 반환
            results = []
            for item in checks_config:
                result = item.copy()
                result.update({
                    "status": "fail", 
                    "message": f"분석 오류: {str(e)}", 
                    "score": 0,
                    "title": item.get("title", "Unknown"),
                    "importance": item.get("importance", 1),
                    "criteria": item.get("criteria", "분석 중 오류 발생")
                })
                results.append(result)
            return results

    # (삭제됨) E-E-A-T 관련 섹션 분석 함수는 더 이상 사용하지 않습니다.

    # (삭제) E-E-A-T 관련 헬퍼 메소드 제거

    

    

    

    

    

    

    

    async def _check_mixed_content(self) -> Dict:
        """Mixed Content (혼합 콘텐츠) 검사를 수행합니다."""
        try:
            mixed_content_count = 0
            mixed_content_details = []
            
            # HTTPS 페이지에서 HTTP 리소스 검사
            if self.url.startswith('https://'):
                # 1. 이미지 태그 검사
                img_tags = self.soup.find_all('img')
                for img in img_tags:
                    src = img.get('src', '')
                    if src.startswith('http://'):
                        mixed_content_count += 1
                        mixed_content_details.append(f"이미지: {src}")
                
                # 2. 스크립트 태그 검사
                script_tags = self.soup.find_all('script')
                for script in script_tags:
                    src = script.get('src', '')
                    if src.startswith('http://'):
                        mixed_content_count += 1
                        mixed_content_details.append(f"스크립트: {src}")
                
                # 3. CSS 링크 검사
                link_tags = self.soup.find_all('link', rel='stylesheet')
                for link in link_tags:
                    href = link.get('href', '')
                    if href.startswith('http://'):
                        mixed_content_count += 1
                        mixed_content_details.append(f"CSS: {href}")
                
                # 4. iframe 태그 검사
                iframe_tags = self.soup.find_all('iframe')
                for iframe in iframe_tags:
                    src = iframe.get('src', '')
                    if src.startswith('http://'):
                        mixed_content_count += 1
                        mixed_content_details.append(f"iframe: {src}")
                
                # 5. 오디오/비디오 태그 검사
                media_tags = self.soup.find_all(['audio', 'video'])
                for media in media_tags:
                    src = media.get('src', '')
                    if src.startswith('http://'):
                        mixed_content_count += 1
                        mixed_content_details.append(f"미디어: {src}")
                
                # 6. object/embed 태그 검사
                object_tags = self.soup.find_all(['object', 'embed'])
                for obj in object_tags:
                    src = obj.get('src', '') or obj.get('data', '')
                    if src.startswith('http://'):
                        mixed_content_count += 1
                        mixed_content_details.append(f"객체: {src}")
                
                # 7. 인라인 스타일에서 HTTP URL 검사
                elements_with_style = self.soup.find_all(style=True)
                for element in elements_with_style:
                    style_content = element.get('style', '')
                    if 'http://' in style_content:
                        mixed_content_count += 1
                        mixed_content_details.append(f"인라인 스타일: {style_content[:50]}...")
                
                # 8. style 태그 내부의 HTTP URL 검사
                style_tags = self.soup.find_all('style')
                for style in style_tags:
                    style_content = style.string or ''
                    if 'http://' in style_content:
                        mixed_content_count += 1
                        mixed_content_details.append(f"스타일 태그: {style_content[:50]}...")
                
                # 9. JavaScript 코드 내부의 HTTP URL 검사
                script_contents = self.soup.find_all('script')
                for script in script_contents:
                    if script.string:
                        script_content = script.string
                        if 'http://' in script_content:
                            mixed_content_count += 1
                            mixed_content_details.append(f"JavaScript: {script_content[:50]}...")
                
                # 10. 메타 태그의 HTTP URL 검사
                meta_tags = self.soup.find_all('meta')
                for meta in meta_tags:
                    content = meta.get('content', '')
                    if content.startswith('http://'):
                        mixed_content_count += 1
                        mixed_content_details.append(f"메타 태그: {content}")
            
            details_text = "; ".join(mixed_content_details[:5])  # 최대 5개만 표시
            if len(mixed_content_details) > 5:
                details_text += f" 외 {len(mixed_content_details) - 5}건"
            
            if mixed_content_count == 0:
                return {
                    "status": "pass",
                    "message": "Mixed Content가 발견되지 않았습니다. 모든 리소스가 HTTPS를 사용합니다.",
                    "score": 100,
                    "mixed_content_count": 0,
                    "details": "발견된 Mixed Content 없음"
                }
            else:
                return {
                    "status": "fail",
                    "message": f"Mixed Content {mixed_content_count}건이 발견되었습니다.",
                    "score": 0,
                    "mixed_content_count": mixed_content_count,
                    "details": details_text if mixed_content_details else "발견된 Mixed Content 없음"
                }
            
        except Exception as e:
            return {
                "status": "fail",
                "message": f"Mixed Content 분석 중 오류 발생: {str(e)}",
                "score": 0,
                "mixed_content_count": 0,
                "details": "분석 실패"
            }

    def _normalize_url(self, url: str) -> str:
        """URL을 정규화하여 비교하기 쉽게 만듭니다 (www 제거, / 제거 등)."""
        parsed = urllib.parse.urlparse(url.lower())
        netloc = parsed.netloc.replace('www.', '')
        path = parsed.path.rstrip('/')
        return f"{parsed.scheme}://{netloc}{path}"

    def _calculate_average_score(self, scores: List[int]) -> int:
        if not scores:
            return 100 # 또는 0, 정책에 따라
        return int(round(sum(scores) / len(scores)))

    def _calculate_weighted_score(self, checks: List[Dict]) -> int:
        relevant_checks = [c for c in checks if c.get("status") != "pending"]
        if not relevant_checks: return 100
        total_importance = sum(c.get('importance', 1) for c in relevant_checks)
        weighted_score_sum = sum(c.get('score', 0) * c.get('importance', 1) for c in relevant_checks)
        return int(round(weighted_score_sum / total_importance)) if total_importance > 0 else 100
        
    def _compile_final_result(self, categories_results: List[Dict]) -> Dict:
        all_checks = [check for cat in categories_results for sec in cat["sections"] for check in sec["checks"]]
        total_checks = len(all_checks)
        passed_checks = len([c for c in all_checks if c["status"] == "pass"])
        
        category_scores = {cat["category_id"]: cat["category_score"] for cat in categories_results}
        
        # 가중 평균으로 전체 점수 계산 (단일 카테고리)
        weights = {"technical_seo": 1.0}
        weighted_sum = sum(category_scores.get(cid, 0) * w for cid, w in weights.items())
        total_weight = sum(w for cid, w in weights.items() if cid in category_scores)
        overall_score = int(round(weighted_sum / total_weight)) if total_weight > 0 else 0

        failed_checks = [c for c in all_checks if c["status"] == 'fail']
        sorted_failed = sorted(failed_checks, key=lambda x: x.get('importance', 0), reverse=True)

        return {
            "url": self.url,
            "checklist_version": self.checklist.get("seo_checklist", {}).get("metadata", {}).get("version", "1.0"),
            "categories": categories_results,
            "overall_score": overall_score,
            "category_scores": category_scores,
            "total_checks": total_checks,
            "passed_checks": passed_checks,
            "failed_checks": total_checks - passed_checks,
            "critical_issues": [f"{c['title']}: {c['message']}" for c in sorted_failed if c.get('importance', 0) >= 4][:5],
            "recommendations": [{
                "priority": "높음" if c.get('importance', 0) >= 4 else "중간",
                "title": c['title'], "description": c['message'], "category": "N/A", "expected_impact": "개선 필요"
            } for c in sorted_failed]
        }

    async def _collect_website_data_for_ai(self) -> Dict:
        """AI 분석을 위한 웹사이트 데이터를 수집합니다."""
        try:
            website_data = {
                "main_page_content": "",
                "internal_links": [],
                "navigation": {},
                "sitemap_urls": [],
                "url_structure_analysis": {},
                "depth_analysis": {},
                "anchor_texts": []
            }
            
            # 메인 페이지 콘텐츠 수집
            if self.soup:
                # 페이지 콘텐츠 추출
                main_content = self.soup.get_text() if self.soup else ""
                website_data["main_page_content"] = main_content[:5000]  # 처음 5000자만
                
                # 내부 링크 수집 및 분석
                internal_links = []
                anchor_texts = []
                
                for link in self.soup.find_all('a', href=True):
                    href = link.get('href', '')
                    anchor_text = link.get_text(strip=True)
                    
                    # 내부 링크인지 확인
                    if href.startswith('/') or self._is_same_domain(urllib.parse.urljoin(self.url, href)):
                        full_url = urllib.parse.urljoin(self.url, href)
                        internal_links.append(full_url)
                        if anchor_text:
                            anchor_texts.append(anchor_text)
                
                website_data["internal_links"] = list(set(internal_links))[:50]  # 중복 제거, 최대 50개
                website_data["anchor_texts"] = anchor_texts[:100]  # 최대 100개
                
                # 네비게이션 구조 분석
                nav_elements = self.soup.find_all(['nav', 'ul', 'ol'], class_=lambda x: x and any(word in x.lower() for word in ['nav', 'menu', 'header']))
                navigation_structure = {}
                
                for i, nav in enumerate(nav_elements[:3]):  # 최대 3개 네비게이션
                    nav_links = nav.find_all('a', href=True)
                    navigation_structure[f"nav_{i+1}"] = {
                        "link_count": len(nav_links),
                        "links": [link.get('href') for link in nav_links[:10]],  # 최대 10개 링크
                        "text": nav.get_text()[:500]  # 최대 500자
                    }
                
                website_data["navigation"] = navigation_structure
                
                # URL 구조 분석
                from urllib.parse import urlparse
                parsed_url = urlparse(self.url)
                path_parts = [part for part in parsed_url.path.split('/') if part]
                
                website_data["url_structure_analysis"] = {
                    "domain": parsed_url.netloc,
                    "path_depth": len(path_parts),
                    "path_parts": path_parts,
                    "has_parameters": bool(parsed_url.query),
                    "scheme": parsed_url.scheme
                }
                
                # 사이트맵 URL 수집 시도
                sitemap_urls = []
                try:
                    sitemap_url = f"{parsed_url.scheme}://{parsed_url.netloc}/sitemap.xml"
                    async with httpx.AsyncClient(timeout=10.0) as client:
                        response = await client.get(sitemap_url)
                        if response.status_code == 200:
                            # 간단한 사이트맵 파싱
                            import re
                            urls_in_sitemap = re.findall(r'<loc>(.*?)</loc>', response.text)
                            sitemap_urls = urls_in_sitemap[:100]  # 최대 100개
                except:
                    pass  # 사이트맵이 없어도 계속 진행
                    
                website_data["sitemap_urls"] = sitemap_urls
                
                # 깊이 분석을 위한 URL 샘플링
                depth_analysis = {}
                for url in internal_links[:20]:  # 최대 20개 URL 분석
                    try:
                        parsed = urlparse(url)
                        path_depth = len([part for part in parsed.path.split('/') if part])
                        depth_analysis[url] = path_depth
                    except:
                        continue
                        
                website_data["depth_analysis"] = depth_analysis
            
            print(f"📊 사이트 구조 데이터 수집 완료: 내부링크 {len(website_data['internal_links'])}개, 앵커텍스트 {len(website_data['anchor_texts'])}개")
            return website_data
            
        except Exception as e:
            print(f"❌ 웹사이트 데이터 수집 오류: {e}")
            return {"error": str(e)}

    def _combine_technical_and_ai_results(self, technical_results: List[Dict], ai_results: Dict, checks_config: List[Dict]) -> List[Dict]:
        """기술적 분석과 AI 분석 결과를 결합합니다."""
        combined_results = []
        
        for i, item in enumerate(checks_config):
            technical_result = technical_results[i] if i < len(technical_results) else item.copy()
            
            # 필수 필드들이 없으면 원본 item에서 복사
            if 'id' not in technical_result and 'id' in item:
                technical_result['id'] = item['id']
            if 'title' not in technical_result and 'title' in item:
                technical_result['title'] = item['title']
            if 'importance' not in technical_result and 'importance' in item:
                technical_result['importance'] = item['importance']
            if 'criteria' not in technical_result and 'criteria' in item:
                technical_result['criteria'] = item['criteria']
            
            # AI 분석 결과가 있으면 보완
            if ai_results and not ai_results.get("error"):
                ai_enhanced_result = self._enhance_with_ai_insights(technical_result, ai_results, item)
                combined_results.append(ai_enhanced_result)
            else:
                combined_results.append(technical_result)
        
        return combined_results

    def _enhance_with_ai_insights(self, technical_result: Dict, ai_results: Dict, item: Dict) -> Dict:
        """기술적 분석 결과에 AI 인사이트를 보완합니다."""
        enhanced_result = technical_result.copy()
        check_title = item.get("title", "").lower()
        
        # AI 분석 결과에서 관련 정보 찾기
        url_management = ai_results.get("url_management", {})
        content_duplication = ai_results.get("content_duplication", {})
        
        # 체크 항목별 AI 인사이트 매핑
        ai_insight = None
        if "self-canonical" in check_title:
            ai_insight = url_management.get("self_canonical")
        elif "canonical 체인" in check_title:
            ai_insight = url_management.get("canonical_chains")
        elif "http vs https" in check_title:
            ai_insight = url_management.get("http_https_mixed")
        elif "title 중복" in check_title:
            ai_insight = content_duplication.get("title_duplicates")
        elif "meta description 중복" in check_title:
            ai_insight = content_duplication.get("description_duplicates")
        elif "해시 중복" in check_title:
            ai_insight = content_duplication.get("hash_duplicates")
        elif "near-duplicate" in check_title:
            ai_insight = content_duplication.get("near_duplicate_content")
        elif "thin 단어수" in check_title:
            ai_insight = content_duplication.get("thin_content")
        
        # AI 인사이트가 있고 의미있는 내용이 있을 때만 메시지 보완
        if ai_insight and isinstance(ai_insight, dict):
            ai_evidence = ai_insight.get("evidence", "")
            ai_improvement = ai_insight.get("improvement", "")
            
            # AI 분석 결과와 개선안을 통합하여 자연스러운 문장으로 구성
            if ai_evidence and ai_evidence.strip() and ai_evidence != "확인 불가":
                if ai_improvement and ai_improvement.strip() and ai_improvement != "확인 불가" and enhanced_result.get("status") != "pass":
                    # 분석 결과와 개선안을 함께 포함
                    enhanced_result["message"] += f" {ai_evidence} {ai_improvement}"
                else:
                    # 분석 결과만 포함
                    enhanced_result["message"] += f" {ai_evidence}"
        
        return enhanced_result

    def _is_same_domain(self, url: str) -> bool:
        """URL이 같은 도메인인지 확인합니다."""
        try:
            def normalize(host: str) -> str:
                if host.startswith('www.'):
                    return host[4:]
                return host
            parsed = urllib.parse.urlparse(url)
            return normalize(parsed.netloc) == normalize(urllib.parse.urlparse(self.url).netloc)
        except:
            return False 

    async def _perform_site_structure_ux_checks(self, checks_config: List[Dict]) -> List[Dict]:
        """사이트 구조 및 사용자 경험 검사 수행 (AI + 기술적 분석 결합)"""
        try:
            # 1. 기술적 분석 수행 (체크리스트 축소에 따라 기술 검사는 기본 pending으로 단순화)
            technical_results = self._perform_technical_site_structure_checks(checks_config)
            
            # 2. AI 분석 수행
            ai_results = None
            if self.ai_analyzer:
                try:
                    # 웹사이트 데이터 수집
                    website_data = await self._collect_website_data_for_ai()
                    
                    # AI 분석 수행
                    ai_analysis = await self.ai_analyzer.analyze_site_structure_ux(self.url, website_data)
                    
                    if not ai_analysis.get("error"):
                        ai_results = ai_analysis
                        print(f"🤖 사이트 구조 AI 분석 완료")
                    else:
                        print(f"⚠️ 사이트 구조 AI 분석 실패: {ai_analysis.get('error')}")
                        
                except Exception as e:
                    print(f"❌ 사이트 구조 AI 분석 중 오류: {str(e)}")
            
            # 3. 기술적 분석과 AI 분석 결과 결합
            combined_results = self._combine_site_structure_results(technical_results, ai_results, checks_config)
            
            return combined_results
            
        except Exception as e:
            print(f"❌ 사이트 구조 분석 중 오류: {str(e)}")
            # 오류 발생 시 기본 결과 반환
            return self._get_default_site_structure_results(checks_config, str(e))

    def _combine_site_structure_results(self, technical_results: List[Dict], ai_results: Dict, checks_config: List[Dict]) -> List[Dict]:
        """기술적 분석과 AI 분석 결과를 결합 (체크리스트 축소 반영)"""
        combined_results = []
        for item in checks_config:
            item_id = item.get("id")
            item_title = item.get("title", "")
            # 해당 기술 결과 찾기
            tech_result = next((t for t in technical_results if t.get("id") == item_id), None)
            final_result = tech_result.copy() if tech_result else {
                "id": item_id,
                "title": item_title,
                "importance": item.get("importance", 3),
                "criteria": item.get("criteria", ""),
                "status": "pending",
                "message": "체크리스트 최소화에 따라 기술적 검사는 생략되었습니다.",
                "score": 50
            }

            # Facet 필터 crawl 관리에 대해서만 AI 결과 보완
            if ai_results and ai_results.get("analysis_results") and item_id == 12 and "Facet" in item_title:
                ai_analysis = ai_results["analysis_results"].get("facet_crawl_management")
                if ai_analysis and isinstance(ai_analysis, dict):
                    ai_score = ai_analysis.get("score", 0)
                    ai_details = ai_analysis.get("details", "AI 분석 완료")
                    status = "pass" if ai_score >= 80 else "warning" if ai_score >= 40 else "fail"
                    final_result.update({
                        "status": status,
                        "message": ai_details,
                        "score": ai_score,
                        "evidence": final_result.get("evidence", "AI 분석 병합")
                    })

            combined_results.append(final_result)
        return combined_results

    def _get_default_site_structure_results(self, checks_config: List[Dict], error: str) -> List[Dict]:
        """사이트 구조 분석 실패 시 기본 결과 반환"""
        results = []
        for item in checks_config:
            results.append({
                "id": item.get("id"),
                "title": item.get("title"),
                "importance": item.get("importance", 3),
                "criteria": item.get("criteria", ""),
                "status": "fail",
                "message": "웹페이지를 불러올 수 없어 분석이 불가능합니다",
                "score": 0,
                "evidence": "분석 중 오류가 발생했습니다"
            })
        return results

    def _perform_technical_site_structure_checks(self, checks_config: List[Dict]) -> List[Dict]:
        """기술적 사이트 구조 검사 수행 (축소된 체크리스트 기준으로 단순화)"""
        results = []
        for item in checks_config:
            results.append({
                "id": item.get("id"),
                "title": item.get("title"),
                "importance": item.get("importance", 3),
                "criteria": item.get("criteria", ""),
                "status": "pending",
                "message": "체크리스트 최소화에 따라 기술적 검사는 생략되었습니다.",
                "score": 50,
                "evidence": "AI 분석 위주로 평가"
            })
        return results

    def _combine_site_structure_results(self, technical_results: List[Dict], ai_results: Dict, checks_config: List[Dict]) -> List[Dict]:
        """기술적 분석과 AI 분석 결과를 결합"""
        combined_results = []
        
        for item in checks_config:
            item_id = item.get("id")
            item_title = item.get("title", "")
            item_importance = item.get("importance", 3)
            item_criteria = item.get("criteria", "")
            
            # 기술적 분석 결과 찾기
            technical_result = None
            for tech_result in technical_results:
                if tech_result.get("id") == item_id:
                    technical_result = tech_result
                    break
            
            # AI 분석 결과에서 해당 항목 찾기 (더 포괄적인 매칭)
            ai_analysis = None
            if ai_results and ai_results.get("analysis_results"):
                analysis_results = ai_results["analysis_results"]
                
                # 체크리스트 항목과 AI 결과 매칭 (정확한 매칭)
                if item_id == 1 and "평균 깊이" in item_title and "클릭" in item_title:
                    ai_analysis = analysis_results.get("average_depth")
                elif item_id == 2 and "Orphan" in item_title:
                    ai_analysis = analysis_results.get("orphan_pages")  
                elif item_id == 3 and "Anchor" in item_title and "다양성" in item_title:
                    ai_analysis = analysis_results.get("anchor_diversity")
                elif item_id == 4 and "Main Nav" in item_title and "계층" in item_title:
                    ai_analysis = analysis_results.get("navigation_hierarchy")
                elif item_id == 10 and "Contextual" in item_title and "링크" in item_title:
                    ai_analysis = analysis_results.get("contextual_links")
                elif item_id == 12 and "Facet" in item_title and "crawl" in item_title:
                    ai_analysis = analysis_results.get("facet_crawl_management")
            
            # 결과 결합 로직 개선
            combined_result = None
            
            if technical_result and technical_result.get("status") != "pending":
                # 기술적 분석 결과가 우선 (HTTP 상태, 리다이렉트, viewport meta)
                combined_result = technical_result.copy()
                
                # AI 분석 결과로 보완 (일관성 검증)
                if ai_analysis and ai_analysis.get("details"):
                    ai_score = ai_analysis.get("score", 0)
                    ai_details = ai_analysis.get("details", "")
                    
                    # AI 분석과 기술적 분석이 상충하지 않는 경우에만 보완
                    tech_score = combined_result.get("score", 0)
                    
                    if abs(tech_score - ai_score) <= 30:  # 점수 차이가 30점 이하인 경우만 조합
                        combined_score = int((tech_score * 0.7) + (ai_score * 0.3))
                        combined_result["score"] = combined_score
                        
                        if ai_details != "분석 실패" and len(ai_details) > 10:
                            combined_result["message"] += f" (AI 보완: {ai_details[:80]})"
                        
            elif ai_analysis:
                # AI 분석 결과만 있는 경우
                ai_status = ai_analysis.get("status", "pending")
                ai_score = ai_analysis.get("score", 0)
                ai_details = ai_analysis.get("details", "AI 분석 완료")
                
                # AI 상태를 표준 상태로 변환
                if ai_status == "통과" or ai_score >= 80:
                    status = "pass"
                elif ai_status == "실패" or ai_score < 40:
                    status = "fail" 
                else:
                    status = "warning"
                
                # AI 분석 메시지 정제 (명확하고 간결하게)
                if "고아 페이지가 발견되지 않았습니다" in ai_details:
                    message = "AI 분석 결과 고아 페이지가 발견되지 않았습니다."
                    status = "pass"
                    ai_score = max(ai_score, 80)
                elif "분석 실패" in ai_details:
                    message = "AI 분석에서 충분한 데이터를 확보하지 못했습니다."
                    status = "warning"
                    ai_score = 50
                else:
                    # AI 상세 내용을 자연스럽게 요약
                    if len(ai_details) > 100:
                        sentences = ai_details.split('. ')
                        if len(sentences) > 1:
                            message = sentences[0] + '.'
                        else:
                            message = ai_details[:100].rstrip() + '.'
                    else:
                        message = ai_details
                
                combined_result = {
                    "id": item_id,
                    "title": item_title,
                    "importance": item_importance,
                    "criteria": item_criteria,
                    "status": status,
                    "message": message,
                    "score": ai_score,
                    "evidence": self._format_ai_evidence(ai_analysis)
                }
                
            else:
                # 분석 결과가 없는 경우 기본값 제공
                combined_result = {
                    "id": item_id,
                    "title": item_title,
                    "importance": item_importance,
                    "criteria": item_criteria,
                    "status": "warning",
                    "message": "분석에 필요한 데이터가 부족합니다.",
                    "score": 30,  # 0점 대신 30점으로 설정
                    "evidence": "데이터 부족 - 수동 검토 또는 사이트 개선 후 재분석 권장"
                }
            
            combined_results.append(combined_result)
        
        return combined_results

    def _format_ai_evidence(self, ai_analysis: Dict) -> str:
        """AI 분석 결과를 증거 문자열로 포맷팅"""
        evidence_parts = []
        
        # 점수 정보
        if "score" in ai_analysis:
            evidence_parts.append(f"AI 점수: {ai_analysis['score']}")
        
        # 구체적 수치 정보
        if "current_depth" in ai_analysis:
            evidence_parts.append(f"현재 깊이: {ai_analysis['current_depth']}")
        elif "orphan_count" in ai_analysis:
            evidence_parts.append(f"고아 페이지: {ai_analysis['orphan_count']}개")
        elif "diversity_score" in ai_analysis:
            evidence_parts.append(f"다양성 점수: {ai_analysis['diversity_score']}")
        elif "hierarchy_levels" in ai_analysis:
            evidence_parts.append(f"계층 수: {ai_analysis['hierarchy_levels']}")
        elif "contextual_ratio" in ai_analysis:
            evidence_parts.append(f"컨텍스트 비율: {ai_analysis['contextual_ratio']}")
        
        return ", ".join(evidence_parts) if evidence_parts else "AI 분석 완료"

    def _get_default_site_structure_results(self, checks_config: List[Dict], error: str) -> List[Dict]:
        """사이트 구조 분석 실패 시 기본 결과 반환"""
        results = []
        for item in checks_config:
            results.append({
                "id": item.get("id"),
                "title": item.get("title"),
                "importance": item.get("importance", 3),
                "criteria": item.get("criteria", ""),
                "status": "fail",
                "message": f"분석 실패: {error}",
                "score": 0,
                "evidence": "시스템 오류로 인한 분석 불가"
            })
        return results

    def _check_internal_links_http_status(self) -> Dict:
        """내부 링크의 HTTP 상태 코드 체크"""
        try:
            if not self.soup:
                return {
                    "status": "fail",
                    "message": "웹페이지를 불러올 수 없어 분석이 불가능합니다",
                    "score": 0,
                    "evidence": "페이지 접근 실패 - URL 확인 필요"
                }
            
            # 내부 링크 수집
            internal_links = []
            for link in self.soup.find_all('a', href=True):
                href = link['href']
                if href.startswith('/') or self.url.split('//')[1].split('/')[0] in href:
                    if href.startswith('/'):
                        href = f"{self.url.rstrip('/')}{href}"
                    internal_links.append(href)
            
            if not internal_links:
                return {
                    "status": "warning",
                    "message": "내부 링크를 찾을 수 없음",
                    "score": 50,
                    "evidence": "분석할 내부 링크 없음"
                }
            
            # 샘플링 (최대 20개)
            sample_links = internal_links[:20]
            broken_links = []
            
            for link in sample_links:
                try:
                    response = requests.head(link, timeout=10, allow_redirects=True)
                    if response.status_code >= 400:
                        broken_links.append((link, response.status_code))
                except:
                    broken_links.append((link, "연결 실패"))
            
            broken_count = len(broken_links)
            total_checked = len(sample_links)
            success_rate = ((total_checked - broken_count) / total_checked) * 100
            
            if broken_count == 0:
                return {
                    "status": "pass",
                    "message": f"모든 내부 링크가 정상 작동합니다. ({total_checked}개 확인)",
                    "score": 100,
                    "evidence": f"검증된 링크: {total_checked}개, 오류: 0개 - 우수한 링크 관리"
                }
            else:
                return {
                    "status": "fail",
                    "message": f"일부 내부 링크에서 오류가 발견되었습니다. ({broken_count}/{total_checked}개)",
                    "score": max(0, int(success_rate)),
                    "evidence": f"문제 링크 예시: {broken_links[:2]} (성공률: {success_rate:.0f}%)"  # 처음 2개만 표시
                }
                
        except Exception as e:
            return {
                "status": "fail",
                "message": "내부 링크 상태 확인 중 기술적 오류가 발생했습니다",
                "score": 0,
                "evidence": "시스템 오류가 발생했습니다"
            }

    def _check_redirect_hops(self) -> Dict:
        """리다이렉트 체인 체크"""
        try:
            if not self.soup:
                return {
                    "status": "fail",
                    "message": "웹페이지를 불러올 수 없어 분석이 불가능합니다",
                    "score": 0,
                    "evidence": "페이지 접근 실패 - URL 확인 필요"
                }
            
            # 내부 링크 수집
            internal_links = []
            for link in self.soup.find_all('a', href=True):
                href = link['href']
                if href.startswith('/') or self.url.split('//')[1].split('/')[0] in href:
                    if href.startswith('/'):
                        href = f"{self.url.rstrip('/')}{href}"
                    internal_links.append(href)
            
            if not internal_links:
                return {
                    "status": "warning",
                    "message": "내부 링크를 찾을 수 없음",
                    "score": 50,
                    "evidence": "분석할 내부 링크 없음"
                }
            
            # 샘플링 (최대 10개)
            sample_links = internal_links[:10]
            redirect_issues = []
            
            for link in sample_links:
                try:
                    # 리다이렉트 추적
                    response = requests.get(link, timeout=10, allow_redirects=False)
                    redirect_count = 0
                    current_url = link
                    
                    while response.status_code in [301, 302, 303, 307, 308] and redirect_count < 5:
                        redirect_count += 1
                        current_url = response.headers.get('Location', '')
                        if current_url:
                            response = requests.get(current_url, timeout=10, allow_redirects=False)
                        else:
                            break
                    
                    if redirect_count > 1:
                        redirect_issues.append((link, redirect_count))
                        
                except:
                    continue
            
            total_checked = len(sample_links)
            issues_count = len(redirect_issues)
            
            if issues_count == 0:
                return {
                    "status": "pass",
                    "message": f"모든 내부 링크가 직접 연결되어 있습니다. ({total_checked}개 확인)",
                    "score": 100,
                    "evidence": f"검증된 링크: {total_checked}개, 다중 리다이렉트: 0개 - 최적화된 링크 구조"
                }
            else:
                return {
                    "status": "fail",
                    "message": f"다중 리다이렉트가 발견되었습니다. ({issues_count}/{total_checked}개)",
                    "score": max(0, int(((total_checked - issues_count) / total_checked) * 100)),
                    "evidence": f"문제 링크 예시: {redirect_issues[:2]} (직접 연결로 변경 권장)"
                }
                
        except Exception as e:
            return {
                "status": "fail",
                "message": "리다이렉트 체인 확인 중 기술적 오류가 발생했습니다",
                "score": 0,
                "evidence": "시스템 오류가 발생했습니다"
            }

    def _check_viewport_meta(self) -> Dict:
        """Viewport meta 태그 존재 여부 확인"""
        try:
            if not self.soup:
                return {
                    "status": "fail",
                    "message": "웹페이지를 불러올 수 없어 분석이 불가능합니다",
                    "score": 0,
                    "evidence": "페이지 접근 실패 - URL 확인 필요"
                }
            
            # viewport meta 태그 찾기
            viewport_meta = self.soup.find('meta', attrs={'name': 'viewport'})
            
            if viewport_meta:
                content = viewport_meta.get('content', '')
                if 'width=device-width' in content:
                    return {
                        "status": "pass",
                        "message": "Viewport meta 태그가 올바르게 설정되어 있습니다.",
                        "score": 100,
                        "evidence": f"viewport 설정: {content}"
                    }
                else:
                    return {
                        "status": "warning",
                        "message": "Viewport meta 태그가 있지만 width=device-width가 누락되었습니다.",
                        "score": 70,
                        "evidence": f"현재 설정: {content} | 권장: width=device-width 포함"
                    }
            else:
                return {
                    "status": "fail",
                                            "message": "Viewport meta 태그가 없습니다.",
                    "score": 0,
                    "evidence": "viewport 태그 누락 - 모바일 SEO에 치명적"
                }
                
        except Exception as e:
            return {
                "status": "fail",
                "message": "viewport 태그 분석 중 기술적 오류가 발생했습니다",
                "score": 0,
                "evidence": "시스템 오류가 발생했습니다"
            }

    def _check_average_depth(self) -> Dict:
        """평균 깊이 체크 (기술적 분석)"""
        try:
            if not self.soup:
                return {
                    "status": "fail",
                    "message": "웹페이지를 불러올 수 없어 분석이 불가능합니다",
                    "score": 0,
                    "evidence": "페이지 접근 실패 - URL 확인 필요"
                }
            
            # 내부 링크 수집
            internal_links = []
            for link in self.soup.find_all('a', href=True):
                href = link.get('href', '')
                if href.startswith('/') or self._is_same_domain(urllib.parse.urljoin(self.url, href)):
                    full_url = urllib.parse.urljoin(self.url, href)
                    internal_links.append(full_url)
            
            if not internal_links:
                return {
                    "status": "warning",
                    "message": "내부 링크가 발견되지 않았습니다. 사이트 내 페이지 간 연결을 추가하세요",
                    "score": 30,
                    "evidence": "내부 링크 부족 - 사이트 네비게이션 개선 필요"
                }
            
            # URL 깊이 계산
            from urllib.parse import urlparse
            depths = []
            
            for url in set(internal_links):  # 중복 제거
                try:
                    parsed = urlparse(url)
                    path_parts = [part for part in parsed.path.split('/') if part]
                    depth = len(path_parts)
                    depths.append(depth)
                except:
                    continue
            
            if not depths:
                return {
                    "status": "warning",
                    "message": "URL 구조 분석에 실패했습니다.",
                    "score": 40,
                    "evidence": "URL 구조 파싱 오류 - 표준 URL 형식 권장"
                }
            
            average_depth = sum(depths) / len(depths)
            max_depth = max(depths)
            
            # 평가 기준: ≤4 클릭
            if average_depth <= 4:
                score = 100 - (average_depth - 1) * 10  # 깊이가 증가할수록 점수 감소
                status = "pass"
                message = f"평균 {average_depth:.1f}번 클릭으로 페이지 접근이 가능합니다."
            elif average_depth <= 6:
                score = 60 - (average_depth - 4) * 10
                status = "warning"
                message = f"평균 {average_depth:.1f}번 클릭이 필요합니다. 구조 개선을 권장합니다."
            else:
                score = 20
                status = "fail"
                message = f"평균 {average_depth:.1f}번 클릭으로 접근성이 떨어집니다. 사이트 구조를 단순화하세요."
            
            return {
                "status": status,
                "message": message,
                "score": max(0, int(score)),
                "evidence": f"분석 URL: {len(depths)}개, 평균: {average_depth:.1f}, 최대: {max_depth}"
            }
            
        except Exception as e:
            return {
                "status": "fail",
                "message": "사이트 구조 깊이 분석 중 기술적 오류가 발생했습니다",
                "score": 0,
                "evidence": "시스템 오류가 발생했습니다"
            }

    def _check_anchor_diversity(self) -> Dict:
        """앵커 텍스트 다양성 체크"""
        try:
            if not self.soup:
                return {
                    "status": "fail",
                    "message": "웹페이지를 불러올 수 없어 분석이 불가능합니다",
                    "score": 0,
                    "evidence": "페이지 접근 실패 - URL 확인 필요"
                }
            
            # 앵커 텍스트 수집
            anchor_texts = []
            for link in self.soup.find_all('a', href=True):
                anchor_text = link.get_text(strip=True)
                if anchor_text and len(anchor_text) > 1:  # 의미있는 텍스트만
                    anchor_texts.append(anchor_text.lower())
            
            if len(anchor_texts) < 3:
                return {
                    "status": "warning",
                    "message": f"앵커 텍스트가 부족합니다. ({len(anchor_texts)}개)",
                    "score": 40,
                    "evidence": "링크 텍스트 부족 - SEO 키워드 활용 권장"
                }
            
            # 다양성 계산 (엔트로피 기반)
            from collections import Counter
            text_counts = Counter(anchor_texts)
            total_count = len(anchor_texts)
            unique_count = len(set(anchor_texts))
            
            # 단순 다양성 지수 계산
            diversity_score = unique_count / total_count
            
            # 반복 패턴 분석
            repeated_texts = {text: count for text, count in text_counts.items() if count > 1}
            
            # 평가 기준: ≥0.3
            if diversity_score >= 0.3:
                score = min(100, int(diversity_score * 100))
                status = "pass"
                message = f"앵커 텍스트 다양성이 충족됩니다. (다양성: {diversity_score:.2f})"
            elif diversity_score >= 0.2:
                score = 60
                status = "warning"
                message = f"앵커 텍스트 다양성이 보통입니다. 개선을 권장합니다. (다양성: {diversity_score:.2f})"
            else:
                score = 30
                status = "fail"
                message = f"앵커 텍스트가 반복적입니다. 다양한 키워드 사용이 필요합니다. (다양성: {diversity_score:.2f})"
            
            evidence_parts = [
                f"전체 {total_count}개",
                f"고유 {unique_count}개", 
                f"다양성: {diversity_score:.2f}"
            ]
            
            if repeated_texts:
                top_repeated = list(repeated_texts.items())[:2]
                evidence_parts.append(f"반복: {top_repeated}")
            
            return {
                "status": status,
                "message": message,
                "score": score,
                "evidence": ", ".join(evidence_parts)
            }
            
        except Exception as e:
            return {
                "status": "fail",
                "message": "앵커 텍스트 다양성 분석 중 기술적 오류가 발생했습니다",
                "score": 0,
                "evidence": "시스템 오류가 발생했습니다"
            }

    def _check_orphan_pages_basic(self) -> Dict:
        """기본적인 고아 페이지 검출"""
        try:
            if not self.soup:
                return {
                    "status": "fail",
                    "message": "웹페이지를 불러올 수 없어 분석이 불가능합니다",
                    "score": 0,
                    "evidence": "페이지 접근 실패 - URL 확인 필요"
                }
            
            # 현재 페이지의 내부 링크 분석으로 기본적인 검출
            internal_links = []
            for link in self.soup.find_all('a', href=True):
                href = link.get('href', '')
                if href.startswith('/') or self._is_same_domain(urllib.parse.urljoin(self.url, href)):
                    full_url = urllib.parse.urljoin(self.url, href)
                    internal_links.append(full_url)
            
            # 네비게이션/메뉴에 포함된 링크 분석
            nav_links = []
            nav_elements = self.soup.find_all(['nav', 'ul', 'ol'], class_=lambda x: x and any(word in x.lower() for word in ['nav', 'menu', 'header']))
            
            for nav in nav_elements:
                for link in nav.find_all('a', href=True):
                    href = link.get('href', '')
                    if href.startswith('/') or self._is_same_domain(urllib.parse.urljoin(self.url, href)):
                        full_url = urllib.parse.urljoin(self.url, href)
                        nav_links.append(full_url)
            
            total_internal = len(set(internal_links))
            nav_linked = len(set(nav_links))
            
            # 내부 링크가 거의 없는 경우 특별 처리
            if total_internal <= 1:
                return {
                    "status": "warning",
                    "message": "내부 링크가 부족하여 고아 페이지 분석이 어렵습니다.",
                    "score": 50,
                    "evidence": f"분석 가능한 내부링크: {total_internal}개 - 추가 분석 필요"
                }
            
            coverage_ratio = nav_linked / total_internal
            
            # 기본적인 평가 (완전한 고아 페이지 검출은 사이트맵 비교 필요)
            if coverage_ratio >= 0.8:
                return {
                    "status": "pass",
                    "message": f"대부분 페이지가 네비게이션으로 연결되어 있습니다. ({coverage_ratio:.0%})",
                    "score": 90,
                    "evidence": f"내부링크 {total_internal}개 중 {nav_linked}개가 네비게이션 연결"
                }
            elif coverage_ratio >= 0.5:
                return {
                    "status": "warning", 
                    "message": f"일부 페이지가 네비게이션에서 누락될 수 있습니다. ({coverage_ratio:.0%})",
                    "score": 60,
                    "evidence": f"네비게이션 커버리지: {coverage_ratio:.1%} - 개선 여지 있음"
                }
            else:
                # coverage_ratio가 0이면 다른 메시지, 아니면 원래 메시지
                if coverage_ratio == 0:
                    return {
                        "status": "warning",
                        "message": "네비게이션 링크가 부족하여 정확한 고아 페이지 분석이 어렵습니다.",
                        "score": 50,
                        "evidence": f"네비게이션 링크: {nav_linked}개, 내부링크: {total_internal}개 - 사이트맵 분석 권장"
                    }
                else:
                    return {
                        "status": "warning",
                        "message": f"고아 페이지 존재 가능성이 있습니다. ({coverage_ratio:.0%})",
                        "score": 40,
                        "evidence": f"네비게이션 커버리지 낮음: {coverage_ratio:.1%} - 사이트맵 분석 권장"
                    }
                
        except Exception as e:
            return {
                "status": "fail",
                "message": "고아 페이지 검출 분석 중 기술적 오류가 발생했습니다",
                "score": 0,
                "evidence": "시스템 오류가 발생했습니다"
            }

    def _check_average_depth(self) -> Dict:
        """평균 깊이 ≤4 클릭 검사"""
        try:
            # BFS를 사용하여 페이지 깊이 계산
            if not self.soup:
                return {
                    "status": "fail",
                    "message": "웹페이지를 불러올 수 없어 분석이 불가능합니다",
                    "score": 0,
                    "evidence": "페이지 로드 실패"
                }
            
            # 내부 링크 수집
            internal_links = []
            for link in self.soup.find_all('a', href=True):
                href = link.get('href', '')
                if href.startswith('/') or self._is_same_domain(urllib.parse.urljoin(self.url, href)):
                    full_url = urllib.parse.urljoin(self.url, href)
                    internal_links.append(full_url)
            
            # 고유한 내부 링크만 추출
            unique_links = list(set(internal_links))
            
            if len(unique_links) <= 1:
                return {
                    "status": "warning",
                    "message": "⚠️ 내부 링크가 부족하여 깊이 분석이 어렵습니다.",
                    "score": 50,
                    "evidence": f"분석 가능한 내부링크: {len(unique_links)}개"
                }
            
            # 단순화된 깊이 계산 (URL 경로 기반)
            total_depth = 0
            for link in unique_links:
                try:
                    parsed = urllib.parse.urlparse(link)
                    path_segments = [seg for seg in parsed.path.split('/') if seg]
                    depth = len(path_segments)
                    total_depth += depth
                except:
                    total_depth += 2  # 기본 깊이
            
            average_depth = total_depth / len(unique_links) if unique_links else 0
            
            # 결과 평가
            if average_depth <= 4:
                return {
                    "status": "pass",
                    "message": f"✅ 평균 {average_depth:.1f}번 클릭으로 페이지 접근이 가능합니다.",
                    "score": 90,
                    "evidence": f"{len(unique_links)}개 링크 분석 (평균 깊이: {average_depth:.1f})"
                }
            elif average_depth <= 6:
                return {
                    "status": "warning",
                    "message": f"⚠️ 평균 {average_depth:.1f}번 클릭으로 다소 깊습니다.",
                    "score": 60,
                    "evidence": f"권장 깊이(4클릭) 초과: {average_depth:.1f}클릭"
                }
            else:
                return {
                    "status": "fail",
                    "message": f"❌ 평균 {average_depth:.1f}번 클릭으로 너무 깊습니다.",
                    "score": 30,
                    "evidence": f"매우 깊은 구조: {average_depth:.1f}클릭 (권장: 4클릭)"
                }
                
        except Exception as e:
            return {
                "status": "fail",
                "message": "깊이 분석 중 기술적 오류가 발생했습니다",
                "score": 0,
                "evidence": "분석 중 오류가 발생했습니다"
            }
    
    def cleanup_resources(self):
        """리소스 정리 및 프로세스 종료"""
        try:
            print("🧹 SEOAnalyzer 리소스 정리 시작...")
            
            # 1. Selenium 드라이버 종료
            if self.driver:
                try:
                    self.driver.quit()
                    print("✅ Selenium 드라이버 종료 완료")
                except Exception as e:
                    print(f"⚠️ 드라이버 종료 중 오류: {e}")
                finally:
                    self.driver = None
            
            # 2. Chrome/Chromium 프로세스 강제 종료
            try:
                import subprocess
                subprocess.run(['pkill', '-f', 'chrome|chromium|chromedriver'], 
                             capture_output=True, text=True, timeout=5)
                print("✅ Chrome 프로세스 정리 완료")
            except Exception as e:
                print(f"⚠️ Chrome 프로세스 정리 중 오류: {e}")
            
            # 3. 임시 파일 정리
            try:
                import tempfile
                import shutil
                temp_dir = tempfile.gettempdir()
                chrome_dirs = [d for d in os.listdir(temp_dir) if 'chrome' in d.lower()]
                for chrome_dir in chrome_dirs[:5]:  # 최대 5개만 정리
                    try:
                        shutil.rmtree(os.path.join(temp_dir, chrome_dir))
                    except:
                        pass
                print("✅ 임시 파일 정리 완료")
            except Exception as e:
                print(f"⚠️ 임시 파일 정리 중 오류: {e}")
                
            print("🎉 SEOAnalyzer 리소스 정리 완료")
            
        except Exception as e:
            print(f"❌ 리소스 정리 중 전체 오류: {e}")
    
    def __del__(self):
        """소멸자에서 자동 정리"""
        try:
            self.cleanup_resources()
        except:
            pass  # 소멸자에서는 예외를 무시