from fastapi import FastAPI, HTTPException, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import Response
from pydantic import BaseModel, HttpUrl, Field
import uvicorn
from typing import Optional, Dict, List, Any
import os
from dotenv import load_dotenv
import json

from seo_analyzer import SEOA<PERSON>yzer
from ai_analyzer import AIAnalyzer

load_dotenv()

# ✅ PageSpeed API 키를 환경변수에서 로드
PAGESPEED_API_KEY = os.getenv("PAGESPEED_API_KEY")

app = FastAPI(
    title="SEO 분석 도구",
    description="웹사이트 SEO 분석 및 AI 기반 개선 제안",
    version="1.0.0"
)

# 환경에 따른 CORS 설정
is_development = os.getenv("ENVIRONMENT", "development") != "production"
production_ip = os.getenv("PRODUCTION_IP", "**************")

# CORS origins 동적 설정
if is_development:
    origins = [
        "http://localhost:4000",  # 로컬 개발 (다른 포트)
        "http://localhost:5173",  # Vite 개발 서버
        "http://127.0.0.1:4000",     # 로컬호스트
        "http://************:4000",  # 로컬 네트워크 IP
    ]
else:
    origins = [
        "https://xe-seo.vercel.app",  # Vercel 메인 배포
        f"http://{production_ip}:4000",  # 배포 환경 프론트엔드 (백업)
        f"https://{production_ip}:4000",  # HTTPS 지원 (백업)
        "https://*.vercel.app",   # Vercel 기타 배포
    ]

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 개발 중이므로 모든 origin 허용
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
    expose_headers=["*"]
)

# OPTIONS 요청 처리를 위한 미들웨어
@app.middleware("http")
async def options_middleware(request: Request, call_next):
    if request.method == "OPTIONS":
        response = Response()
        response.headers["Access-Control-Allow-Origin"] = "*"
        response.headers["Access-Control-Allow-Methods"] = "GET, POST, OPTIONS"
        response.headers["Access-Control-Allow-Headers"] = "*"
        response.headers["Access-Control-Max-Age"] = "86400"
        return response
    
    response = await call_next(request)
    return response



class URLRequest(BaseModel):
    url: HttpUrl
    ai_provider: Optional[str] = "gemini"  # 현재는 "gemini"만 지원

class CheckInfo(BaseModel):
    id: int
    title: str
    importance: int
    criteria: str
    status: str
    message: str
    score: int

class SectionResult(BaseModel):
    section_id: str
    section_name: str
    checks: List[CheckInfo]
    total_checks: int
    passed_checks: int
    section_score: int
    ai_suggestions: Optional[List[Dict[str, Any]]] = None # AI 제안을 담을 필드 추가

class CategoryResult(BaseModel):
    category_id: str
    category_name: str
    sections: List[SectionResult]
    category_score: int

class Recommendation(BaseModel):
    priority: str = Field(..., description="추천의 우선순위 (예: 높음, 중간, 낮음)")
    title: str = Field(..., description="개선 항목의 제목")
    description: str = Field(..., description="개선 항목에 대한 상세 설명")
    category: str = Field(..., description="관련 SEO 카테고리 (예: 메타 태그, 성능)")
    expected_impact: str = Field(..., description="예상되는 SEO 점수 영향 또는 효과")


class AIInsights(BaseModel):
    overall_summary: str
    seo_level: str
    strengths: List[str]
    priority_recommendations: List[Recommendation]
    technical_insights: List[str]
    content_insights: List[str]
    expected_score_improvement: str
    implementation_timeline: str

class SEOResult(BaseModel):
    url: str
    checklist_version: str
    categories: List[CategoryResult]
    overall_score: int
    category_scores: Dict[str, int]
    total_checks: int
    passed_checks: int
    failed_checks: int
    ai_analysis: AIInsights
    recommendations: List[Recommendation] = Field(description="상위 추천 목록")
    critical_issues: List[str]

@app.get("/")
async def read_root():
    """API 상태 확인"""
    return {"message": "SEO 분석 API 서버가 실행 중입니다!", "version": "1.0.0"}

@app.get("/api-status")
async def get_api_status():
    """API 키 상태를 확인하는 엔드포인트"""
    try:
        # AIAnalyzer 인스턴스 생성하여 상태 확인
        ai_analyzer = AIAnalyzer()
        api_status = ai_analyzer.get_api_key_status()
        
        # 민감한 정보 제거
        safe_status = {
            'current_key_index': api_status['current_key_index'],
            'total_keys': api_status['total_keys'],
            'usage_stats': {}
        }
        
        # API 키는 마스킹하여 표시
        for i, (key, stats) in enumerate(api_status['usage_stats'].items()):
            masked_key = f"***{key[-4:]}" if len(key) > 4 else "***"
            safe_status['usage_stats'][masked_key] = {
                'success': stats['success'],
                'failure': stats['failure'],
                'last_used': stats['last_used']
            }
        
        return {
            "status": "success",
            "api_key_status": safe_status,
            "timestamp": "2023-12-21T10:30:45.123456"
        }
    except Exception as e:
        return {
            "status": "error",
            "message": str(e),
            "timestamp": "2023-12-21T10:30:45.123456"
        }

@app.options("/analyze")
async def options_analyze():
    """CORS preflight 요청 처리"""
    return Response(
        status_code=200,
        headers={
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Methods": "GET, POST, OPTIONS",
            "Access-Control-Allow-Headers": "*",
            "Access-Control-Max-Age": "86400",
        }
    )

@app.post("/analyze", response_model=SEOResult)
async def analyze_seo(request: URLRequest):
    """SEO 분석 수행"""
    # ✅ PageSpeed API 키가 없으면 오류 발생
    if not PAGESPEED_API_KEY:
        raise HTTPException(status_code=500, detail="PageSpeed Insights API 키가 설정되지 않았습니다.")

    try:
        checklist_path = os.path.join(os.path.dirname(__file__), "seo-checklist.json")
        with open(checklist_path, "r", encoding="utf-8") as f:
            checklist_data = json.load(f)

        # ✅ SEOAnalyzer를 새로운 비동기 팩토리 메소드로 생성
        seo_analyzer = await SEOAnalyzer.create(str(request.url), checklist_data)
        analysis_result = await seo_analyzer.analyze()

        # ✅ 분석 결과에 에러가 있는지 확인하고, 있다면 즉시 예외 발생
        if "error" in analysis_result:
            raise HTTPException(status_code=500, detail=f"SEO 분석 실패: {analysis_result['error']}")

        # AI 분석기 초기화 및 결과 결합
        ai_analyzer = AIAnalyzer()
        combined_result_dict = await ai_analyzer.combine_analysis_with_ai_insights(analysis_result)

        # ✅ AI가 생성한 추천 목록을 최종 결과에 포함
        combined_result_dict["recommendations"] = combined_result_dict["ai_analysis"].get("priority_recommendations", [])
        
        return SEOResult(**combined_result_dict)
    
    except Exception as e:
        # 서버 로그에 전체 오류 기록
        print(f"Unhandled error during SEO analysis for URL {request.url}: {e}")
        # Traceback을 로깅하여 디버깅 정보 확보
        import traceback
        traceback.print_exc()
        
        # 사용자에게 반환될 오류 응답 생성
        error_response = {
            "url": request.url,
            "overall_score": 0,
            "categories": [],
            "ai_analysis": {
                "overall_summary": f"'{request.url}'을(를) 분석하는 중 서버 오류가 발생했습니다. 잠시 후 다시 시도해 주세요. 오류: {str(e)}",
                "seo_level": "분석 실패",
                "strengths": [],
                "priority_recommendations": [],
                "technical_insights": [],
                "content_insights": [],
                "expected_score_improvement": "N/A",
                "implementation_timeline": "N/A"
            },
            "recommendations": []
        }
        # FastAPI가 자동으로 500 상태 코드를 반환하도록 예외 발생
        raise HTTPException(
            status_code=500,
            detail=f"분석 중 오류 발생: '{e.__class__.__name__}'"
        ) from e

@app.get("/analyze/test", response_model=SEOResult)
async def analyze_test():
    """풀 케이스 테스트 데이터 반환"""
    sample_data = {
        "url": "https://example.com",
        "checklist_version": "2.0",
        "categories": [
            {
                "category_id": "technical_seo",
                "category_name": "기술 SEO 6개 영역",
                "category_score": 92,
                "sections": [
                    {
                        "section_id": "crawling_indexing",
                        "section_name": "크롤링 및 색인 관리",
                        "checks": [
                            {
                                "id": 1,
                                "title": "robots.txt 200 응답",
                                "importance": 5,
                                "criteria": "GET /robots.txt → 200 OK",
                                "status": "pass",
                                "message": "robots.txt가 200 OK를 반환합니다.",
                                "score": 100
                            },
                            {
                                "id": 2,
                                "title": "User-agent=* Disallow 비율",
                                "importance": 5,
                                "criteria": "핵심 디렉터리 차단 0%",
                                "status": "pass",
                                "message": "모든 검색엔진의 접근을 막는 'Disallow: /' 규칙이 없습니다.",
                                "score": 100
                            },
                            {
                                "id": 3,
                                "title": "Sitemap 지시자 존재",
                                "importance": 4,
                                "criteria": "Sitemap: ≥1 줄",
                                "status": "pass",
                                "message": "robots.txt에 사이트맵 주소 1개를 확인했습니다.",
                                "score": 100
                            },
                            {
                                "id": 7,
                                "title": "<lastmod> 정확도",
                                "importance": 2,
                                "criteria": "페이지 갱신 ±72 h",
                                "status": "pass",
                                "message": "lastmod 정보가 샘플 5개 모두 정확합니다 (±72시간).",
                                "score": 100
                            }
                        ],
                        "total_checks": 4,
                        "passed_checks": 4,
                        "section_score": 100,
                        "ai_suggestions": []
                    },
                    {
                        "section_id": "page_optimization",
                        "section_name": "페이지 최적화",
                        "checks": [
                            {
                                "id": 1,
                                "title": "Title 30–60 자",
                                "importance": 5,
                                "criteria": "주요 키워드 포함",
                                "status": "pass",
                                "message": "제목 길이 최적화됨 (45자)",
                                "score": 100
                            },
                            {
                                "id": 2,
                                "title": "Meta Description 150–160 자",
                                "importance": 4,
                                "criteria": "타겟 키워드 포함",
                                "status": "pass",
                                "message": "메타 디스크립션 최적화됨 (155자)",
                                "score": 100
                            },
                            {
                                "id": 3,
                                "title": "H1 태그 1개",
                                "importance": 4,
                                "criteria": "페이지당 1개",
                                "status": "pass",
                                "message": "H1 태그가 1개 존재합니다.",
                                "score": 100
                            }
                        ],
                        "total_checks": 3,
                        "passed_checks": 3,
                        "section_score": 100,
                        "ai_suggestions": []
                    },
                    {
                        "section_id": "web_performance_technical",
                        "section_name": "웹 성능 및 기술적 요소",
                        "checks": [
                            {
                                "id": 1,
                                "title": "LCP ≤2.5초",
                                "importance": 5,
                                "criteria": "모바일 기준",
                                "status": "pass",
                                "message": "LCP: 1.8초 (최적)",
                                "score": 100
                            },
                            {
                                "id": 2,
                                "title": "INP ≤200ms",
                                "importance": 5,
                                "criteria": "모바일 기준",
                                "status": "pass",
                                "message": "INP: 150ms (최적)",
                                "score": 100
                            },
                            {
                                "id": 3,
                                "title": "CLS ≤0.1",
                                "importance": 5,
                                "criteria": "모바일 기준",
                                "status": "pass",
                                "message": "CLS: 0.05 (최적)",
                                "score": 100
                            }
                        ],
                        "total_checks": 3,
                        "passed_checks": 3,
                        "section_score": 100,
                        "ai_suggestions": []
                    }
                ]
            }
        ],
        "overall_score": 92,
        "category_scores": {
            "technical_seo": 92
        },
        "total_checks": 10,
        "passed_checks": 10,
        "failed_checks": 0,
        "ai_analysis": {
            "overall_summary": "전반적으로 우수한 SEO 상태입니다. 모든 핵심 요소가 최적화되어 있으며, 검색엔진 최적화가 잘 되어 있습니다.",
            "seo_level": "고급",
            "strengths": ["완벽한 robots.txt 설정", "최적화된 메타 태그", "우수한 웹 성능"],
            "priority_recommendations": [
                {
                    "priority": "낮음",
                    "title": "콘텐츠 품질 향상",
                    "description": "현재 기술적 SEO는 완벽하므로, 콘텐츠 품질과 사용자 경험을 더욱 향상시켜 보세요.",
                    "category": "콘텐츠 최적화",
                    "expected_impact": "사용자 참여도 증가"
                }
            ],
            "technical_insights": ["모든 기술적 요소가 최적화됨"],
            "content_insights": ["콘텐츠 품질 향상 여지 있음"],
            "expected_score_improvement": "+5-8점",
            "implementation_timeline": "1-2주"
        },
        "recommendations": [
            {
                "priority": "낮음",
                "title": "콘텐츠 품질 향상",
                "description": "현재 기술적 SEO는 완벽하므로, 콘텐츠 품질과 사용자 경험을 더욱 향상시켜 보세요.",
                "category": "콘텐츠 최적화",
                "expected_impact": "사용자 참여도 증가"
            }
        ],
        "critical_issues": []
    }
    return SEOResult(**sample_data)

@app.get("/analyze/test/bad", response_model=SEOResult)
async def analyze_test_bad():
    """에러 케이스 테스트 데이터 반환"""
    sample_data = {
        "url": "https://example.com",
        "checklist_version": "2.0",
        "categories": [
            {
                "category_id": "technical_seo",
                "category_name": "기술 SEO 6개 영역",
                "category_score": 35,
                "sections": [
                    {
                        "section_id": "crawling_indexing",
                        "section_name": "크롤링 및 색인 관리",
                        "checks": [
                            {
                                "id": 1,
                                "title": "robots.txt 200 응답",
                                "importance": 5,
                                "criteria": "GET /robots.txt → 200 OK",
                                "status": "fail",
                                "message": "robots.txt를 찾을 수 없습니다 (상태 코드: 404).",
                                "score": 0
                            },
                            {
                                "id": 2,
                                "title": "User-agent=* Disallow 비율",
                                "importance": 5,
                                "criteria": "핵심 디렉터리 차단 0%",
                                "status": "fail",
                                "message": "치명적 오류: 'User-agent: *'에 'Disallow: /'가 설정되어 모든 검색엔진을 차단합니다.",
                                "score": 0
                            },
                            {
                                "id": 3,
                                "title": "Sitemap 지시자 존재",
                                "importance": 4,
                                "criteria": "Sitemap: ≥1 줄",
                                "status": "fail",
                                "message": "robots.txt 파일이 없습니다.",
                                "score": 0
                            },
                            {
                                "id": 7,
                                "title": "<lastmod> 정확도",
                                "importance": 2,
                                "criteria": "페이지 갱신 ±72 h",
                                "status": "fail",
                                "message": "사이트맵에 lastmod 태그가 없습니다.",
                                "score": 20
                            }
                        ],
                        "total_checks": 4,
                        "passed_checks": 0,
                        "section_score": 5,
                        "ai_suggestions": [
                            {
                                "id": 1,
                                "suggestion": "robots.txt 파일을 서버 루트 디렉터리에 생성하고, 검색엔진이 접근할 수 있도록 설정하세요.",
                                "expected_impact": "검색엔진 크롤링 허용 및 색인 개선"
                            },
                            {
                                "id": 2,
                                "suggestion": "User-agent: *에 Disallow: / 규칙을 제거하고, 필요한 페이지만 선택적으로 차단하세요.",
                                "expected_impact": "검색엔진 접근 허용 및 순위 향상"
                            },
                            {
                                "id": 3,
                                "suggestion": "robots.txt에 Sitemap 지시자를 추가하여 사이트맵 위치를 명시하세요.",
                                "expected_impact": "검색엔진 색인 효율성 증대"
                            },
                            {
                                "id": 7,
                                "suggestion": "사이트맵 XML 파일에 lastmod 태그를 추가하여 페이지 수정 시간을 정확히 표시하세요.",
                                "expected_impact": "검색엔진 크롤링 최적화"
                            }
                        ]
                    },
                    {
                        "section_id": "page_optimization",
                        "section_name": "페이지 최적화",
                        "checks": [
                            {
                                "id": 1,
                                "title": "Title 30–60 자",
                                "importance": 5,
                                "criteria": "주요 키워드 포함",
                                "status": "fail",
                                "message": "제목이 너무 짧습니다 (15자)",
                                "score": 30
                            },
                            {
                                "id": 2,
                                "title": "Meta Description 150–160 자",
                                "importance": 4,
                                "criteria": "타겟 키워드 포함",
                                "status": "fail",
                                "message": "메타 디스크립션이 없습니다.",
                                "score": 0
                            },
                            {
                                "id": 3,
                                "title": "H1 태그 1개",
                                "importance": 4,
                                "criteria": "페이지당 1개",
                                "status": "fail",
                                "message": "H1 태그가 없습니다.",
                                "score": 0
                            }
                        ],
                        "total_checks": 3,
                        "passed_checks": 0,
                        "section_score": 10,
                        "ai_suggestions": [
                            {
                                "id": 1,
                                "suggestion": "페이지 제목을 30-60자로 늘리고 주요 키워드를 포함하세요.",
                                "expected_impact": "검색 결과 노출 개선 및 클릭률 증가"
                            },
                            {
                                "id": 2,
                                "suggestion": "메타 디스크립션을 150-160자로 작성하고 타겟 키워드를 포함하세요.",
                                "expected_impact": "검색 결과 클릭률 향상"
                            },
                            {
                                "id": 3,
                                "suggestion": "페이지에 H1 태그를 1개 추가하여 콘텐츠 구조를 명확히 하세요.",
                                "expected_impact": "검색엔진 이해도 향상 및 순위 개선"
                            }
                        ]
                    },
                    {
                        "section_id": "web_performance_technical",
                        "section_name": "웹 성능 및 기술적 요소",
                        "checks": [
                            {
                                "id": 1,
                                "title": "LCP ≤2.5초",
                                "importance": 5,
                                "criteria": "모바일 기준",
                                "status": "fail",
                                "message": "LCP: 4.2초 (느림)",
                                "score": 20
                            },
                            {
                                "id": 2,
                                "title": "INP ≤200ms",
                                "importance": 5,
                                "criteria": "모바일 기준",
                                "status": "fail",
                                "message": "INP: 350ms (느림)",
                                "score": 20
                            },
                            {
                                "id": 3,
                                "title": "CLS ≤0.1",
                                "importance": 5,
                                "criteria": "모바일 기준",
                                "status": "fail",
                                "message": "CLS: 0.25 (나쁨)",
                                "score": 20
                            }
                        ],
                        "total_checks": 3,
                        "passed_checks": 0,
                        "section_score": 20,
                        "ai_suggestions": [
                            {
                                "id": 1,
                                "suggestion": "이미지 최적화, CDN 사용, 서버 응답 시간 개선을 통해 LCP를 2.5초 이하로 단축하세요.",
                                "expected_impact": "사용자 경험 개선 및 이탈률 감소"
                            },
                            {
                                "id": 2,
                                "suggestion": "JavaScript 최적화, 이벤트 핸들러 개선을 통해 INP를 200ms 이하로 단축하세요.",
                                "expected_impact": "사용자 상호작용 반응성 향상"
                            },
                            {
                                "id": 3,
                                "suggestion": "레이아웃 시프트를 방지하기 위해 이미지와 광고에 고정 크기를 설정하세요.",
                                "expected_impact": "시각적 안정성 향상 및 사용자 경험 개선"
                            }
                        ]
                    }
                ]
            }
        ],
        "overall_score": 35,
        "category_scores": {
            "technical_seo": 35
        },
        "total_checks": 10,
        "passed_checks": 0,
        "failed_checks": 10,
        "ai_analysis": {
            "overall_summary": "심각한 SEO 문제가 다수 발견되었습니다. 즉시 개선이 필요한 핵심 요소들이 있습니다.",
            "seo_level": "초급",
            "strengths": [],
            "priority_recommendations": [
                {
                    "priority": "높음",
                    "title": "robots.txt 파일 생성",
                    "description": "검색엔진이 사이트에 접근할 수 있도록 robots.txt 파일을 생성하고 올바르게 설정하세요.",
                    "category": "크롤링 최적화",
                    "expected_impact": "검색엔진 접근 허용 및 색인 개선"
                },
                {
                    "priority": "높음",
                    "title": "메타 태그 최적화",
                    "description": "Title과 Meta Description을 적절한 길이로 작성하고 타겟 키워드를 포함하세요.",
                    "category": "페이지 최적화",
                    "expected_impact": "검색 결과 노출 및 클릭률 향상"
                },
                {
                    "priority": "높음",
                    "title": "웹 성능 개선",
                    "description": "LCP, INP, CLS 등 Core Web Vitals를 개선하여 사용자 경험을 향상시키세요.",
                    "category": "성능 최적화",
                    "expected_impact": "사용자 경험 개선 및 순위 향상"
                }
            ],
            "technical_insights": ["robots.txt 누락", "메타 태그 부족", "성능 문제"],
            "content_insights": ["콘텐츠 구조 개선 필요"],
            "expected_score_improvement": "+40-50점",
            "implementation_timeline": "4-6주"
        },
        "recommendations": [
            {
                "priority": "높음",
                "title": "robots.txt 파일 생성",
                "description": "검색엔진이 사이트에 접근할 수 있도록 robots.txt 파일을 생성하고 올바르게 설정하세요.",
                "category": "크롤링 최적화",
                "expected_impact": "검색엔진 접근 허용 및 색인 개선"
            },
            {
                "priority": "높음",
                "title": "메타 태그 최적화",
                "description": "Title과 Meta Description을 적절한 길이로 작성하고 타겟 키워드를 포함하세요.",
                "category": "페이지 최적화",
                "expected_impact": "검색 결과 노출 및 클릭률 향상"
            },
            {
                "priority": "높음",
                "title": "웹 성능 개선",
                "description": "LCP, INP, CLS 등 Core Web Vitals를 개선하여 사용자 경험을 향상시키세요.",
                "category": "성능 최적화",
                "expected_impact": "사용자 경험 개선 및 순위 향상"
            }
        ],
        "critical_issues": [
            "robots.txt 파일 누락",
            "모든 검색엔진 차단",
            "메타 태그 부족",
            "웹 성능 문제"
        ]
    }
    return SEOResult(**sample_data)


if __name__ == "__main__":
    host = os.getenv("HOST", "0.0.0.0")
    port = int(os.getenv("PORT", "8000"))
    reload = is_development
    
    print(f"🚀 서버 시작: {'개발' if is_development else '배포'} 모드")
    print(f"📍 주소: http://{host}:{port}")
    print(f"🔄 자동 재로드: {reload}")
    
    uvicorn.run("main:app", host=host, port=port, reload=reload) 