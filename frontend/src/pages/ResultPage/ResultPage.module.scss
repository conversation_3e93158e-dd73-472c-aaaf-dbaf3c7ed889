@import "../../styles/mixin";

// Dashboard Layout
.dashboard {
  max-width: 1400px;
  margin: 0 auto;
  padding: 30px;
}

// Top area layout: donut (left) + categories (right)
.score-and-categories {
  display: grid;
  grid-template-columns: 240px 1fr;
  align-items: center;
  gap: 24px;
  margin-bottom: 30px;
}

.categories-panel {
  min-width: 0; // allow grid child to shrink properly
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 2px solid #f8f9fa;
}

.logo {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 24px;
  font-weight: bold;
  color: #4c63b6;
}

.url-info {
  background: #f8f9fa;
  padding: 10px 20px;
  border-radius: 25px;
  color: #666;
  font-size: 14px;
  a {
    color: #666;
  }
}

// Main Score Section
.main-score {
  text-align: center;
}

.score-circle {
  position: relative;
  width: 200px;
  height: 200px;
  margin: 0 auto; 

  @include mobile {
    width: 150px;
    height: 150px;
  }
}

.circle-bg {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.circle-inner {
  width: calc(100% - 40px);
  height: calc(100% - 40px);
  background: white;
  border-radius: 50%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.score-number {
  font-size: 48px;
  font-weight: bold;
  color: #4c63b6;
}

.score-label {
  font-size: 16px;
  color: #666;
  margin-top: 5px;
}

// Categories Grid
.categories-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20px;
  margin-bottom: 0;

  @include tablet {
    grid-template-columns: repeat(2, 1fr);
  }
}

.category-card {
  background: #ffffff;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  padding: 1.5rem;
  transition: all 0.3s ease;
  cursor: pointer;
  text-decoration: none;
  color: inherit;
  display: block;
  position: relative;
  overflow: hidden;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    border-color: #d1d5db;
  }

  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    border-radius: 12px 12px 0 0;
    background: var(--accent-color);
  }

  &.excellent {
    --accent-color: #10b981;
  }

  &.good {
    --accent-color: #3b82f6;
  }

  &.warning {
    --accent-color: #f59e0b;
  }

  &.danger {
    --accent-color: #ef4444;
  }
}

.category-header {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.category-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.category-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: #111827;
  margin: 0;
}

.category-score {
  font-size: 1.2rem;
  font-weight: 700;
  color: var(--accent-color);
}

.progress-bar {
  width: 100%;
  height: 8px;
  background: #f3f4f6;
  border-radius: 4px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: var(--accent-color);
  border-radius: 4px;
  transition: width 0.3s ease;
}

.category-status {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;
  color: #6b7280;
}

.status-icon {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: var(--accent-color);
}

// AI Insights Section
.ai-insights {
  background: white;
  border: 2px solid #e5e7eb;
  border-radius: 16px;
  padding: 30px;
  margin-top: 30px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.06);
}

.insights-header {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-bottom: 25px;
  padding-bottom: 20px;
  border-bottom: 2px solid #f3f4f6;
}

.insights-icon {
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.insights-title {
  font-size: 24px;
  font-weight: bold;
  color: #374151;
}

.ai-overall-summary {
  font-size: 1rem;
  line-height: 1.6;
  color: #4b5563;
  margin-top: 1rem;
  margin-bottom: 2rem;
  padding: 1rem;
  background-color: #f8f9fa;
  border-radius: 8px;
}

.ai-highlights {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.highlight-box {
  background: #f8f9fa;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  padding: 1.5rem;
  text-align: center;

  h4 {
    font-size: 0.9rem;
    font-weight: 600;
    color: #6b7280;
    margin: 0 0 0.5rem 0;
    text-transform: uppercase;
  }

  p {
    font-size: 1.4rem;
    font-weight: 700;
    color: #4c63b6;
    margin: 0;
  }
}

.insights-grid2 {
  display: grid;
  grid-template-columns: 0.5fr 0.5fr;
  gap: 25px;
}

.insight-section {
  background: #f8f9fa;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  padding: 24px;
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    border-color: #d1d5db;
  }

  h4 {
    font-size: 18px;
    margin-bottom: 16px;
    display: flex;
    align-items: center;
    gap: 8px;
    color: #374151;
    font-weight: 600;
  }
}

.insight-list {
  list-style: none;
  padding: 0;
  margin: 0;

  li {
    margin-bottom: 12px;
    padding-left: 20px;
    position: relative;
    font-size: 14px;
    line-height: 1.6;
    color: #4b5563;

    &::before {
      content: "•";
      position: absolute;
      left: 0;
      color: #667eea;
      font-weight: bold;
      font-size: 16px;
    }
  }
}

// Recommendations Section
.recommendations {
  margin-top: 2rem;
  padding: 2rem;
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.06);
  border: 1px solid #e5e7eb;

  h4 {
    font-size: 1.5rem;
    font-weight: 600;
    color: #374151;
    margin-bottom: 1rem;
  }

  p {
    color: #6b7280;
    margin-bottom: 1.5rem;
  }
}

.recommendations-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
}

.recommendation-item {
  background: white;
  padding: 15px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
  border-left: 4px solid;
}

.recommendation-week {
  font-weight: bold;
  display: block;
  margin-bottom: 0.5rem;
}

.recommendation-title {
  color: #6b7280;
}

.detailed-report-section {
  margin-top: 40px;
}

// Priority Classes
.priorityHigh {
  color: #dc2626;
  font-weight: 500;
}

.priorityMedium {
  color: #d97706;
  font-weight: 500;
}

.priorityLow {
  color: #059669;
  font-weight: 500;
}

.save-button {
  padding: 8px 12px;
  border-radius: 6px;
  background: #4c63b6;
  color: #fff;
  border: 0;
  cursor: pointer;
}

// Responsive Design
@media (max-width: 768px) {
  .dashboard {
    padding: 20px;
    margin: 10px;
  }

  .score-and-categories {
    display: block;
  }

  .main-score {
    margin-bottom: 24px;
  }

  .categories-grid {
    grid-template-columns: 1fr; // 모바일 1열 유지
  }

  .insights-grid2 {
    grid-template-columns: 1fr;
  }
}
