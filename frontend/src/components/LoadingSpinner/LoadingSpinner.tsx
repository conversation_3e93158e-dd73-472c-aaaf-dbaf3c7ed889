import React, { useState, useEffect } from "react";
import { Bar<PERSON>hart3, <PERSON>, CheckCircle } from "lucide-react";
import styles from "./LoadingSpinner.module.scss";
import classNames from "classnames/bind";

const cx = classNames.bind(styles);

interface LoadingSpinnerProps {
  onProgress?: (step: number, progress: number) => void;
  isComplete?: boolean; // 분석 완료 여부를 외부에서 제어
  progress?: number; // 외부에서 전달받은 진행률
}

const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({
  onProgress,
  isComplete = false,
  progress: externalProgress,
}) => {
  const [currentStep, setCurrentStep] = useState(0);
  const [progress, setProgress] = useState(0);

  const steps = [
    { id: 1, label: "SEO 요소 분석", duration: 8000 }, // 크롤링 시간 포함 (4000 + 4000)
    { id: 2, label: "AI 분석 진행", duration: 14000 }, // AI 분석 시간 (12000 + 2000)
    { id: 3, label: "결과 생성", duration: 4000 }, // 결과 정리 시간 (2000 + 2000)
  ];

  useEffect(() => {
    let progressInterval: NodeJS.Timeout;

    // 외부에서 전달받은 진행률이 있으면 사용
    if (externalProgress !== undefined) {
      setProgress(externalProgress);
      
      // 진행률에 따라 현재 단계 계산
      if (externalProgress >= 100) {
        setCurrentStep(steps.length - 1);
      } else if (externalProgress >= 70) {
        setCurrentStep(2);
      } else if (externalProgress >= 40) {
        setCurrentStep(1);
      } else {
        setCurrentStep(0);
      }
      return;
    }

    // 분석이 완료되면 즉시 100%로 설정
    if (isComplete) {
      setProgress(100);
      setCurrentStep(steps.length - 1);
      return;
    }

    const startProgress = () => {
      const totalDuration = steps.reduce((sum, step) => sum + step.duration, 0);
      let elapsedTime = 0;

      progressInterval = setInterval(() => {
        elapsedTime += 200; // 200ms마다 업데이트 (더 부드러운 진행)
        const newProgress = Math.min((elapsedTime / totalDuration) * 100, 100);
        setProgress(newProgress);

        // 현재 단계 계산
        let cumulativeDuration = 0;
        let newCurrentStep = 0;

        for (let i = 0; i < steps.length; i++) {
          cumulativeDuration += steps[i].duration;
          if (elapsedTime <= cumulativeDuration) {
            newCurrentStep = i;
            break;
          }
        }

        setCurrentStep(newCurrentStep);

        if (onProgress) {
          onProgress(newCurrentStep + 1, newProgress);
        }

        if (newProgress >= 100) {
          clearInterval(progressInterval);
        }
      }, 200);
    };

    startProgress();

    return () => {
      if (progressInterval) clearInterval(progressInterval);
    };
  }, [onProgress, isComplete, externalProgress]);

  return (
    <div className={cx("container")} data-testid="loading-spinner">
      <div className={cx("content")}>
        <div className={cx("icon-wrapper")}>
          <BarChart3 className={cx("chart-icon")} />
          <Brain className={cx("brain-icon")} />
        </div>

        <h2 className={cx("title")} data-testid="loading-title">
          SEO 분석 중...
        </h2>

        <div className={cx("progress-steps")} data-testid="progress-steps">
          {steps.map((step, index) => (
            <div
              key={step.id}
              className={cx("step", {
                active: index <= currentStep,
                completed: index < currentStep,
              })}
              data-testid={`progress-step-${index}`}
            >
              <div className={cx("step-icon")}>
                {index < currentStep ? <CheckCircle size={20} /> : step.id}
              </div>
              <span data-testid={`progress-step-label-${index}`}>
                {step.label}
              </span>
            </div>
          ))}
        </div>

        <div className={cx("progress-bar")} data-testid="progress-bar">
          <div
            className={cx("progress-fill")}
            style={{ width: `${progress}%` }}
            data-testid="progress-fill"
          ></div>
        </div>

        <div className={cx("progress-text")} data-testid="progress-text">
          {Math.round(progress)}% 완료
        </div>

        <p className={cx("description")} data-testid="loading-description">
          {steps[currentStep]?.label || "분석 완료"}
          <br />
          잠시만 기다려주세요...
        </p>
      </div>

      {/* 분석 완료 상태 표시 */}
      {isComplete && (
        <div data-testid="analysis-complete" style={{ display: "none" }}>
          분석 완료
        </div>
      )}
    </div>
  );
};

export default LoadingSpinner;
