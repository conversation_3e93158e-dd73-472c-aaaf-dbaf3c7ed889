import uuid
from datetime import datetime, timedelta
from sqlalchemy import Column, String, DateTime, Integer, JSON, <PERSON>olean, ForeignKey
from sqlalchemy.orm import relationship

from db.engine import Base


def default_expired_at() -> datetime:
    return datetime.utcnow() + timedelta(days=7)


class SEOAnalysis(Base):
    __tablename__ = "seo_analyses"

    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    url = Column(String(2048), nullable=False, index=True)
    overall_score = Column(Integer, nullable=False, default=0)
    payload = Column(JSON, nullable=False)
    is_public = Column(Boolean, default=True, nullable=False)
    expired_at = Column(DateTime, default=default_expired_at, nullable=True)

    user_id = Column(String(36), ForeignKey("users.id"), nullable=True)
    user = relationship("User", back_populates="analyses")

    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)


