from datetime import datetime
from typing import Optional, List, Dict, Any

from pydantic import BaseModel


class SaveAnalysisRequest(BaseModel):
    result: Dict[str, Any]
    is_public: bool = True
    ttl_days: Optional[int] = 7
    user_email: Optional[str] = None
    user_display_name: Optional[str] = None


class SaveAnalysisResponse(BaseModel):
    id: str
    share_url: str
    created_at: datetime


class LoadAnalysisResponse(BaseModel):
    result: Dict[str, Any]
    created_at: datetime


class AnalysisListItem(BaseModel):
    id: str
    url: str
    overall_score: int
    created_at: datetime


class ResultsResponse(BaseModel):
    items: List[AnalysisListItem]
    total: int
    page: int
    page_size: int


