# Cursor Coding Rules

## 1. 스타일 적용 원칙 (classnames/bind)

- 모든 React 컴포넌트에서 스타일을 적용할 때는 반드시 classnames의 bind 기능을 사용한다.

  - 예시:

    ```tsx
    import classNames from "classnames/bind";
    import styles from "./컴포넌트.module.scss";
    const cx = classNames.bind(styles);

    // 사용 예시
    <div className={cx("클래스명")}>...</div>;
    ```

- 직접 styles.xxx 또는 classNames(styles.xxx) 형태로 사용하지 않는다.
- 조건부 스타일, 다중 클래스도 cx로 처리한다.

## 2. 기타 규칙

- (필요시 추가)
