.pie-chart {
  position: relative;
  display: inline-block;
}

// Size classes
.size-120 {
  width: 120px;
  height: 120px;
}
.size-200 {
  width: 200px;
  height: 200px;
}

.svg {
  width: 100%;
  height: 100%;
}

.progress-circle {
  transition: stroke-dashoffset 0.8s ease;
}

// Score color classes
.color-10b981 {
  stroke: #10b981;
}
.color-3b82f6 {
  stroke: #3b82f6;
}
.color-f59e0b {
  stroke: #f59e0b;
}
.color-ef4444 {
  stroke: #ef4444;
}

// Score-specific styles
.score-0,
.score-10,
.score-20,
.score-30,
.score-40 {
  .progress-circle {
    filter: drop-shadow(0 0 8px #ef444440);
  }
}

.score-50,
.score-60 {
  .progress-circle {
    filter: drop-shadow(0 0 8px #f59e0b40);
  }
}

.score-70,
.score-80 {
  .progress-circle {
    filter: drop-shadow(0 0 8px #3b82f640);
  }
}

.score-90,
.score-100 {
  .progress-circle {
    filter: drop-shadow(0 0 8px #10b98140);
  }
}

.content {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
}

.score {
  font-size: 1.5rem;
  font-weight: bold;
  margin-bottom: 0.25rem;
}

.label {
  font-size: 0.75rem;
  font-weight: 500;
}

// Score color classes for text
.color-10b981.score,
.color-10b981.label {
  color: #10b981;
}

.color-3b82f6.score,
.color-3b82f6.label {
  color: #3b82f6;
}

.color-f59e0b.score,
.color-f59e0b.label {
  color: #f59e0b;
}

.color-ef4444.score,
.color-ef4444.label {
  color: #ef4444;
}
