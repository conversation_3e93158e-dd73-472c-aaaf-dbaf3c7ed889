import requests
from bs4 import BeautifulSoup
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
import time
from typing import Dict, List
import urllib.parse
import json
import os
import re
import xml.etree.ElementTree as ET
import httpx
import asyncio
import datetime
from dotenv import load_dotenv

from ai_analyzer import AIAnalyzer # AIAnalyzer import 추가

# .env 파일 로드
load_dotenv()

PAGESPEED_API_KEY = os.getenv("PAGESPEED_API_KEY")

class SEOAnalyzer:
    def __init__(self, url: str, checklist: Dict):
        self.url = self._add_scheme_if_missing(url)
        self.checklist = checklist
        self.driver = None
        self.soup = None
        self.robots_txt_content = None
        self.robots_txt_status = None
        self.headers = {}
        self.pagespeed_results = {}
        self.ai_analyzer = None

    @classmethod
    async def create(cls, url: str, checklist: Dict):
        """비동기 초기화를 위한 팩토리 메소드"""
        instance = cls(url, checklist)
        instance.ai_analyzer = AIAnalyzer()

        # 무거운 I/O 작업들을 비동기적으로 동시에 실행
        # 1. Selenium 드라이버 초기화 (블록킹 작업이므로 별도 스레드에서 실행)
        init_driver_task = asyncio.to_thread(instance._init_driver_and_soup)
        
        # 2. 네트워크 요청들 (httpx로 비동기 실행)
        async with httpx.AsyncClient(timeout=60, follow_redirects=True, verify=False) as client:
            # 드라이버 초기화를 기다리는 동안 다른 네트워크 요청을 보냄
            robots_task = instance._get_robots_txt(client)
            headers_task = instance._get_headers(client)
            pagespeed_task = instance._get_pagespeed_insights(client)
            
            # 모든 비동기 작업이 완료될 때까지 대기
            await init_driver_task # 드라이버와 soup 객체가 설정될 때까지 대기
            
            results = await asyncio.gather(
                robots_task,
                headers_task,
                pagespeed_task,
                return_exceptions=True
            )

            # 결과 할당
            instance.robots_txt_content, instance.robots_txt_status = results[0] if not isinstance(results[0], Exception) else (None, None)
            instance.headers = results[1] if not isinstance(results[1], Exception) else {}
            instance.pagespeed_results = results[2] if not isinstance(results[2], Exception) else {}

        return instance

    def _init_driver_and_soup(self):
        """Selenium 드라이버와 BeautifulSoup 객체를 초기화하는 동기 함수"""
        try:
            options = Options()
            options.add_argument("--headless")
            options.add_argument("--no-sandbox")
            options.add_argument("--disable-dev-shm-usage")
            options.add_argument("--disable-gpu")  # GPU 비활성화로 속도 향상
            options.add_argument("--disable-extensions")  # 확장 프로그램 비활성화
            options.add_argument("--disable-images")  # 이미지 로딩 비활성화로 속도 향상
            options.add_argument("user-agent=Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/123.0.0.0 Safari/537.36")
            
            self.driver = webdriver.Chrome(options=options)
            self.driver.get(self.url)
            time.sleep(2) # 페이지 로딩 대기 시간 단축 (3초 → 2초)
            self.soup = BeautifulSoup(self.driver.page_source, "html.parser")
        except Exception as e:
            print(f"WebDriver 초기화 또는 페이지 파싱 오류: {e}")
            self.driver = None
            self.soup = None

    def _get_soup(self):
        return BeautifulSoup(self.driver.page_source, "html.parser") if self.driver else None

    async def _get_headers(self, client: httpx.AsyncClient):
        try:
            response = await client.head(self.url)
            return response.headers
        except httpx.RequestError as e:
            print(f"헤더 정보 요청 오류: {e}")
            return {}

    async def _get_robots_txt(self, client: httpx.AsyncClient):
        robots_url = urllib.parse.urljoin(self.url, "/robots.txt")
        try:
            response = await client.get(robots_url)
            return (response.text, response.status_code) if response.status_code == 200 else (None, response.status_code)
        except httpx.RequestError:
            return None, None

    def _add_scheme_if_missing(self, url: str) -> str:
        return "https://" + url if not urllib.parse.urlparse(url).scheme else url

    async def _get_pagespeed_insights(self, client: httpx.AsyncClient) -> Dict:
        """Google PageSpeed Insights API를 비동기적으로 호출하여 성능 데이터를 가져옵니다."""
        if not PAGESPEED_API_KEY:
            print("PageSpeed API 키가 설정되지 않아 기본 성능 분석을 수행합니다.")
            print("API 키를 설정하려면:")
            print("1. Google Cloud Console에서 PageSpeed Insights API를 활성화하세요")
            print("2. API 키를 생성하고 환경변수 PAGESPEED_API_KEY에 설정하세요")
            print("3. 또는 .env 파일에 PAGESPEED_API_KEY=your_api_key_here를 추가하세요")
            
            # 기본 성능 분석 수행
            return await self._get_basic_performance_metrics()
        
        # URL 인코딩 추가
        encoded_url = urllib.parse.quote(self.url, safe='')
        api_url = f"https://www.googleapis.com/pagespeedonline/v5/runPagespeed?url={encoded_url}&key={PAGESPEED_API_KEY}&category=PERFORMANCE&strategy=MOBILE"
        print(f"PageSpeed API 호출 시도: {self.url}")
        print(f"인코딩된 URL: {encoded_url}")
        
        try:
            # 타임아웃을 20초로 늘리고 재시도 로직 추가
            for attempt in range(2):  # 최대 2번 시도
                try:
                    response = await client.get(api_url, timeout=20.0)  # 20초로 늘림
                    response.raise_for_status()
                    print("PageSpeed API 호출 성공!")
                    return response.json()
                except httpx.TimeoutException as e:
                    if attempt == 0:  # 첫 번째 시도에서 실패
                        print(f"PageSpeed API 첫 번째 시도 시간 초과 (20초), 재시도 중...")
                        continue
                    else:  # 두 번째 시도에서도 실패
                        print(f"PageSpeed Insights API 호출 시간 초과 (20초) - 기본 성능 분석을 수행합니다: {e}")
                        return await self._get_basic_performance_metrics()
        except httpx.HTTPStatusError as e:
            print(f"PageSpeed API HTTP 오류 ({e.response.status_code}): {e}")
            print(f"응답 내용: {e.response.text[:200]}...")  # 응답 내용 일부 출력
            if e.response.status_code == 403:
                print(f"PageSpeed API 키 인증 실패 (403) - 기본 성능 분석을 수행합니다: {e}")
            elif e.response.status_code == 429:
                print(f"PageSpeed API 할당량 초과 (429) - 기본 성능 분석을 수행합니다: {e}")
            else:
                print(f"PageSpeed API HTTP 오류 ({e.response.status_code}) - 기본 성능 분석을 수행합니다: {e}")
            return await self._get_basic_performance_metrics()
        except httpx.RequestError as e:
            print(f"PageSpeed Insights API 네트워크 오류 - 기본 성능 분석을 수행합니다: {e}")
            return await self._get_basic_performance_metrics()

    async def _get_basic_performance_metrics(self) -> Dict:
        """Selenium을 사용하여 기본적인 성능 메트릭을 수집합니다."""
        if not self.driver:
            return {}
        
        try:
            print("기본 성능 메트릭 수집 시작...")
            
            # 페이지 로드 시간 측정
            navigation_timing = self.driver.execute_script("""
                var timing = performance.timing;
                return {
                    'domContentLoaded': timing.domContentLoadedEventEnd - timing.navigationStart,
                    'loadComplete': timing.loadEventEnd - timing.navigationStart,
                    'firstPaint': performance.getEntriesByType('paint')[0]?.startTime || 0,
                    'firstContentfulPaint': performance.getEntriesByType('paint')[1]?.startTime || 0
                };
            """)
            
            # 리소스 크기 측정
            resource_sizes = self.driver.execute_script("""
                var resources = performance.getEntriesByType('resource');
                var totalSize = 0;
                var imageSize = 0;
                var scriptSize = 0;
                var cssSize = 0;
                
                resources.forEach(function(resource) {
                    if (resource.transferSize) {
                        totalSize += resource.transferSize;
                        if (resource.name.includes('.jpg') || resource.name.includes('.png') || resource.name.includes('.gif') || resource.name.includes('.webp')) {
                            imageSize += resource.transferSize;
                        } else if (resource.name.includes('.js')) {
                            scriptSize += resource.transferSize;
                        } else if (resource.name.includes('.css')) {
                            cssSize += resource.transferSize;
                        }
                    }
                });
                
                return {
                    'totalSize': totalSize,
                    'imageSize': imageSize,
                    'scriptSize': scriptSize,
                    'cssSize': cssSize
                };
            """)
            
            # DOM 요소 수 측정
            dom_elements = self.driver.execute_script("""
                return {
                    'totalElements': document.getElementsByTagName('*').length,
                    'images': document.images.length,
                    'scripts': document.scripts.length,
                    'stylesheets': document.styleSheets.length
                };
            """)
            
            # 추가 성능 메트릭
            additional_metrics = self.driver.execute_script("""
                return {
                    'titleLength': document.title.length,
                    'metaDescriptionLength': document.querySelector('meta[name="description"]')?.content?.length || 0,
                    'h1Count': document.querySelectorAll('h1').length,
                    'h2Count': document.querySelectorAll('h2').length,
                    'linkCount': document.querySelectorAll('a').length,
                    'formCount': document.querySelectorAll('form').length
                };
            """)
            
            result = {
                'lighthouseResult': {
                    'audits': {
                        'largest-contentful-paint': {
                            'numericValue': navigation_timing.get('firstContentfulPaint', 0) * 1000
                        },
                        'total-blocking-time': {
                            'numericValue': max(0, navigation_timing.get('domContentLoaded', 0) - navigation_timing.get('firstPaint', 0))
                        },
                        'cumulative-layout-shift': {
                            'numericValue': 0.1  # 기본값
                        },
                        'server-response-time': {
                            'numericValue': navigation_timing.get('domContentLoaded', 0) * 1000
                        }
                    }
                },
                'basicMetrics': {
                    'pageLoadTime': navigation_timing.get('loadComplete', 0),
                    'domContentLoaded': navigation_timing.get('domContentLoaded', 0),
                    'totalSizeKB': resource_sizes.get('totalSize', 0) / 1024,
                    'imageSizeKB': resource_sizes.get('imageSize', 0) / 1024,
                    'scriptSizeKB': resource_sizes.get('scriptSize', 0) / 1024,
                    'cssSizeKB': resource_sizes.get('cssSize', 0) / 1024,
                    'totalElements': dom_elements.get('totalElements', 0),
                    'imageCount': dom_elements.get('images', 0),
                    'scriptCount': dom_elements.get('scripts', 0),
                    'stylesheetCount': dom_elements.get('stylesheets', 0),
                    'titleLength': additional_metrics.get('titleLength', 0),
                    'metaDescriptionLength': additional_metrics.get('metaDescriptionLength', 0),
                    'h1Count': additional_metrics.get('h1Count', 0),
                    'h2Count': additional_metrics.get('h2Count', 0),
                    'linkCount': additional_metrics.get('linkCount', 0),
                    'formCount': additional_metrics.get('formCount', 0)
                }
            }
            
            print(f"기본 성능 메트릭 수집 완료:")
            print(f"- 페이지 로드 시간: {result['basicMetrics']['pageLoadTime']}ms")
            print(f"- 총 크기: {result['basicMetrics']['totalSizeKB']:.1f}KB")
            print(f"- 이미지 수: {result['basicMetrics']['imageCount']}개")
            print(f"- 스크립트 수: {result['basicMetrics']['scriptCount']}개")
            
            return result
            
        except Exception as e:
            print(f"기본 성능 메트릭 수집 중 오류: {e}")
            return {}

    async def analyze(self) -> Dict:
        if not self.driver or not self.soup:
            return {"error": "WebDriver 초기화 또는 페이지 파싱에 실패하여 분석을 진행할 수 없습니다."}

        # 1. 모든 카테고리와 섹션의 분석을 동기적으로 먼저 수행합니다.
        categories = self.checklist.get("seo_checklist", {}).get("categories", [])
        categories_results = [self._analyze_category(cat) for cat in categories]
        
        # 2. 분석 결과에서 'fail' 또는 'warning' 상태인 모든 체크 항목을 하나의 리스트로 수집합니다.
        all_failed_checks = []
        for category in categories_results:
            for section in category['sections']:
                failed_checks_in_section = [c for c in section['checks'] if c['status'] in ['fail', 'warning']]
                all_failed_checks.extend(failed_checks_in_section)

        # 3. 수집된 항목이 있을 경우, 단 한 번만 AI에게 개선 제안을 요청합니다.
        all_ai_suggestions = []
        if all_failed_checks:
            try:
                # 실패한 항목이 너무 많으면 상위 10개만 처리 (속도 개선)
                limited_failed_checks = all_failed_checks[:10] if len(all_failed_checks) > 10 else all_failed_checks
                all_ai_suggestions = await self.ai_analyzer.get_suggestions_for_failed_checks(limited_failed_checks)
            except Exception as e:
                print(f"전체 AI 제안 요청 중 오류 발생: {e}")
                # AI 분석 실패 시에도 기본 분석 결과는 제공
        
        # 4. ID를 키로 하는 딕셔너리를 만들어 제안을 쉽게 찾을 수 있도록 합니다.
        suggestions_map = {s['id']: s for s in all_ai_suggestions}

        # 5. 각 섹션을 순회하며 해당하는 AI 제안을 다시 넣어줍니다.
        for category in categories_results:
            for section in category['sections']:
                section_suggestions = []
                for check in section['checks']:
                    if check['id'] in suggestions_map:
                        section_suggestions.append(suggestions_map[check['id']])
                section['ai_suggestions'] = section_suggestions
        
        self.driver.quit()
        return self._compile_final_result(categories_results)

    def _analyze_category(self, category: Dict) -> Dict:
        sections = category.get("sections", [])
        sections_results = [self._analyze_section(sec) for sec in sections]
        return {
            "category_id": category.get("id"),
            "category_name": category.get("name"),
            "sections": sections_results,
            "category_score": self._calculate_average_score([s.get("section_score", 0) for s in sections_results])
        }

    def _analyze_section(self, section: Dict) -> Dict:
        section_id = section.get("id")
        all_checks_from_config = []
        if "sub_sections" in section and section["sub_sections"]:
            for sub in section["sub_sections"]:
                all_checks_from_config.extend(sub.get("items", []))
        else:
            all_checks_from_config.extend(section.get("items", []))
        
        check_results = []
        if section_id == 'crawling_indexing':
            check_results = self._perform_crawling_indexing_checks(all_checks_from_config)
        elif section_id == 'page_optimization': # ✅ '페이지 최적화' 섹션 분석 로직 추가
            check_results = self._perform_page_optimization_checks(all_checks_from_config)
        elif section_id == 'security_trust': # ✅ "보안 및 신뢰성" 섹션 분석 로직 추가
            check_results = self._perform_security_trust_checks(all_checks_from_config)
        elif section_id == 'web_performance_technical':
            check_results = self._perform_web_performance_checks(all_checks_from_config)
        elif section_id == 'url_content_standardization': # ✅ "URL 및 콘텐츠 표준화" 섹션 분석 로직 추가
            check_results = self._perform_url_content_standardization_checks(all_checks_from_config)
        else:
            check_results = [{"id": c.get("id"), "title": c.get("title"), "importance": c.get("importance"), "criteria": c.get("criteria"), "status": "pending", "message": "분석 미지원", "score": 0} for c in all_checks_from_config]

        passed_checks_count = len([c for c in check_results if c["status"] == "pass"])
        section_score = self._calculate_weighted_score(check_results)
        
        # AI 제안 요청 로직을 analyze() 메소드로 이동시켰으므로 여기서는 제거합니다.

        return {
            "section_id": section.get("id"), "section_name": section.get("name"), "checks": check_results,
            "total_checks": len(check_results), "passed_checks": passed_checks_count, "section_score": section_score,
            "ai_suggestions": [] # 우선 빈 리스트로 초기화
        }

    def _perform_crawling_indexing_checks(self, checks_config: List[Dict]) -> List[Dict]:
        results = []
        sitemap_urls = re.findall(r"Sitemap:\s*(.*)", self.robots_txt_content, re.IGNORECASE) if self.robots_txt_content else []

        # --- 사전 분석 ---
        crawl_delay, disallow_all, blocked_resources = None, False, []
        if self.robots_txt_content:
            in_ua_all = False
            disallow_rules = []
            for line in self.robots_txt_content.splitlines():
                line = line.strip().lower()
                if line.startswith('user-agent:'): in_ua_all = '*' in line
                if in_ua_all and line.startswith('disallow:'):
                    rule = line.split(':', 1)[1].strip()
                    if rule: disallow_rules.append(rule)
                    if rule == '/': disallow_all = True
                if in_ua_all and line.startswith('crawl-delay:'):
                    val = re.findall(r'\d+', line)
                    if val: crawl_delay = int(val[0])
            
            resources = [t.get('href') for t in self.soup.find_all('link', rel='stylesheet')] + [t.get('src') for t in self.soup.find_all('script', src=True)]
            for res_path in filter(None, resources):
                for rule in disallow_rules:
                    if res_path.startswith(rule):
                        blocked_resources.append(res_path)
                        break
        
        sitemap_content, sitemap_size, url_count, sitemap_ok, canonical_ok, sitemap_meta = None, 0, 0, True, True, {}
        if sitemap_urls:
            try:
                res = requests.get(sitemap_urls[0], timeout=10)
                if res.status_code == 200:
                    sitemap_content, sitemap_size = res.content, len(res.content)
                    root = ET.fromstring(sitemap_content)
                    ns = {'sm': 'http://www.sitemaps.org/schemas/sitemap/0.9', 'xhtml': 'http://www.w3.org/1999/xhtml'}
                    urls = root.findall('sm:url', ns)
                    url_count = len(urls)
                    sitemap_meta = {'is_index': 'sitemapindex' in root.tag, 'has_image': b'<image:image>' in sitemap_content, 'has_video': b'<video:video>' in sitemap_content, 'has_hreflang': bool(root.find('.//xhtml:link', ns))}

                    for url_node in urls[:5]: # 샘플 5개 검사
                        page_url = url_node.find('sm:loc', ns).text
                        page_res = requests.get(page_url, timeout=5)
                        if page_res.status_code != 200: sitemap_ok = False; break
                        page_soup = BeautifulSoup(page_res.content, 'html.parser')
                        canonical_tag = page_soup.find('link', rel='canonical')
                        if canonical_tag and self._normalize_url(canonical_tag.get('href','')) != self._normalize_url(page_url):
                            canonical_ok = False; break
            except Exception as e:
                print(f"Sitemap parsing error: {e}")
                sitemap_content = None

        for item in checks_config:
            result = item.copy()
            result.update({"status": "fail", "message": "분석 중 오류", "score": 0})
            title = item.get("title", "").lower()
            
            print(f"크롤링/색인 분석 중: {title}")

            try:
                if "robots.txt 200" in title:
                    if self.robots_txt_status == 200: result.update({"status": "pass", "message": "robots.txt가 200 OK를 반환합니다.", "score": 100})
                    else: result.update({"message": f"robots.txt를 찾을 수 없습니다 (상태 코드: {self.robots_txt_status})."})
                elif "user-agent=* disallow" in title:
                    if not self.robots_txt_content: result.update({"message": "robots.txt 파일이 없습니다."})
                    elif not disallow_all: result.update({"status": "pass", "message": "모든 검색엔진의 접근을 막는 'Disallow: /' 규칙이 없습니다.", "score": 100})
                    else: result.update({"message": "치명적 오류: 'User-agent: *'에 'Disallow: /'가 설정되어 모든 검색엔진을 차단합니다."})
                elif "sitemap 지시자" in title or "robots 연동" in title:
                    if sitemap_urls: result.update({"status": "pass", "message": f"robots.txt에 사이트맵 주소 {len(sitemap_urls)}개를 확인했습니다.", "score": 100})
                    else: result.update({"message": "robots.txt에 Sitemap 지시어가 없습니다."})
                elif "crawl-delay" in title:
                    if crawl_delay is None: result.update({"status": "pass", "message": "Crawl-delay가 설정되지 않았습니다 (일반적으로 권장).", "score": 100})
                    elif crawl_delay <= 5: result.update({"status": "pass", "message": f"Crawl-delay가 {crawl_delay}초로 적절하게 설정되었습니다.", "score": 100})
                    else: result.update({"message": f"Crawl-delay가 {crawl_delay}초로 너무 길어 크롤링이 비효율적일 수 있습니다.", "score": 40})
                elif "http→https" in title:
                    # (This check was moved to a different function but left here as fallback idea)
                    http_url = self.url.replace("https://", "http://", 1)
                    res = requests.head(http_url, allow_redirects=False, timeout=5)
                    if 300 <= res.status_code < 400 and res.headers.get("Location", "").startswith("https://"):
                         result.update({"status": "pass", "message": "HTTP에서 HTTPS로 정상 리디렉션됩니다.", "score": 100})
                    else:
                         result.update({"message": f"HTTP->HTTPS 리디렉션이 올바르지 않습니다 (상태코드: {res.status_code})."})
                elif "robots.txt 크기" in title:
                    size_kb = len(self.robots_txt_content.encode('utf-8')) / 1024 if self.robots_txt_content else 0
                    if size_kb <= 500: result.update({"status": "pass", "message": f"robots.txt 파일 크기가 {size_kb:.2f}KB로 적절합니다.", "score": 100})
                    else: result.update({"message": f"robots.txt 파일 크기가 {size_kb:.2f}KB로 너무 큽니다 (500KB 이하 권장).", "score": 40})
                elif "pre-render 블로킹" in title:
                    if not self.robots_txt_content: result.update({"status": "pass", "message": "robots.txt가 없어 리소스 차단 여부를 확인할 수 없습니다.", "score": 100})
                    elif not blocked_resources: result.update({"status": "pass", "message": "robots.txt에서 주요 렌더링 리소스(CSS, JS)를 차단하지 않습니다.", "score": 100})
                    else: result.update({"message": f"robots.txt가 렌더링에 필요한 리소스를 차단합니다: {', '.join(blocked_resources[:2])}...", "score": 20})
                elif "disallow 오탐 테스트" in title:
                    if not self.robots_txt_content: 
                        result.update({"status": "pass", "message": "robots.txt가 없어 Disallow 규칙이 없습니다.", "score": 100})
                    else:
                        # Disallow 규칙이 실제로 중요한 페이지를 차단하는지 테스트
                        disallow_rules = []
                        in_ua_all = False
                        for line in self.robots_txt_content.splitlines():
                            line = line.strip().lower()
                            if line.startswith('user-agent:'): 
                                in_ua_all = '*' in line
                            if in_ua_all and line.startswith('disallow:'):
                                rule = line.split(':', 1)[1].strip()
                                if rule: 
                                    disallow_rules.append(rule)
                        
                        if not disallow_rules:
                            result.update({"status": "pass", "message": "Disallow 규칙이 없어 모든 페이지가 허용됩니다.", "score": 100})
                        else:
                            # 실제 사이트에서 중요한 페이지들을 찾아서 테스트
                            important_urls = []
                            
                            # 1. 메인 페이지 (루트)
                            important_urls.append('/')
                            
                            # 2. 사이트맵에서 실제 존재하는 URL들 (최대 10개)
                            if sitemap_urls:
                                try:
                                    sitemap_res = requests.get(sitemap_urls[0], timeout=10)
                                    if sitemap_res.status_code == 200:
                                        root = ET.fromstring(sitemap_res.content)
                                        ns = {'sm': 'http://www.sitemaps.org/schemas/sitemap/0.9'}
                                        urls = root.findall('sm:url', ns)
                                        
                                        for url_node in urls[:10]:  # 최대 10개
                                            url = url_node.find('sm:loc', ns).text
                                            # 현재 도메인의 URL만 사용
                                            if self.url in url:
                                                path = urllib.parse.urlparse(url).path
                                                if path and path not in important_urls:
                                                    important_urls.append(path)
                                except Exception as e:
                                    print(f"사이트맵 파싱 오류: {e}")
                            
                            # 3. 현재 페이지에서 발견된 내부 링크들 (중요도 기반)
                            if self.soup:
                                # H1 태그가 있는 페이지는 중요
                                h1_tags = self.soup.find_all('h1')
                                for h1 in h1_tags:
                                    parent_link = h1.find_parent('a')
                                    if parent_link and parent_link.get('href'):
                                        href = parent_link['href']
                                        if href.startswith('/') and href not in important_urls:
                                            important_urls.append(href)
                                
                                # 네비게이션 메뉴 링크들
                                nav_links = self.soup.find_all('a', href=True)
                                for link in nav_links[:20]:  # 최대 20개
                                    href = link['href']
                                    if href.startswith('/') and href not in important_urls and len(important_urls) < 15:
                                        # 메뉴 관련 키워드가 포함된 링크 우선
                                        link_text = link.get_text(strip=True).lower()
                                        if any(keyword in link_text for keyword in ['about', 'contact', 'product', 'service', 'blog', 'news', 'help', 'support']):
                                            important_urls.append(href)
                            
                            # 4. 기본적인 중요 페이지들 (사이트맵이나 내부 링크에서 찾지 못한 경우)
                            if len(important_urls) < 5:
                                default_important = ['/about', '/contact', '/products', '/services', '/blog']
                                for url in default_important:
                                    if url not in important_urls:
                                        important_urls.append(url)
                            
                            # 실제 테스트 수행
                            blocked_count = 0
                            blocked_urls = []
                            for url in important_urls:
                                for rule in disallow_rules:
                                    if url.startswith(rule):
                                        blocked_count += 1
                                        blocked_urls.append(url)
                                        break
                            
                            if blocked_count == 0:
                                result.update({"status": "pass", "message": f"Disallow 규칙이 중요 페이지 {len(important_urls)}개를 차단하지 않습니다.", "score": 100})
                            elif blocked_count <= len(important_urls) * 0.2:  # 20% 이하 차단
                                result.update({"status": "warning", "message": f"Disallow 규칙이 중요 페이지 {len(important_urls)}개 중 {blocked_count}개를 차단합니다. 차단된 URL: {', '.join(blocked_urls[:3])}", "score": 70})
                            else:
                                result.update({"status": "fail", "message": f"Disallow 규칙이 중요 페이지 {len(important_urls)}개 중 {blocked_count}개를 차단합니다. 많은 중요 페이지가 차단되고 있습니다.", "score": 30})
                elif "xml 유효성" in title:
                    if not sitemap_content: result.update({"message": "robots.txt에 사이트맵이 없어 검사 불가합니다."})
                    else: result.update({"status": "pass", "message": "사이트맵이 유효한 XML 형식입니다.", "score": 100})
                elif "utf-8 인코딩" in title:
                    if not sitemap_content: result.update({"message": "사이트맵을 찾을 수 없습니다."})
                    elif b'encoding="UTF-8"' in sitemap_content[:100]: result.update({"status": "pass", "message": "사이트맵이 UTF-8로 올바르게 인코딩되었습니다.", "score": 100})
                    else: result.update({"message": "사이트맵 XML 선언에 UTF-8 인코딩이 명시되지 않았습니다."})
                elif "<url> ≤50,000" in title:
                    if not sitemap_content: result.update({"message": "사이트맵을 찾을 수 없습니다."})
                    elif url_count <= 50000: result.update({"status": "pass", "message": f"사이트맵에 포함된 URL이 {url_count}개로 권장사항을 준수합니다.", "score": 100})
                    else: result.update({"message": f"사이트맵의 URL 개수가 {url_count}개로 너무 많습니다 (50,000개 이하 권장).", "score": 40})
                elif "파일 크기 ≤50 mb" in title:
                    size_mb = sitemap_size / (1024 * 1024)
                    if not sitemap_content: result.update({"message": "사이트맵을 찾을 수 없습니다."})
                    elif size_mb <= 50: result.update({"status": "pass", "message": f"사이트맵 파일 크기가 {size_mb:.2f}MB로 권장사항을 준수합니다.", "score": 100})
                    else: result.update({"message": f"사이트맵 파일 크기가 {size_mb:.2f}MB로 너무 큽니다 (50MB 이하 권장).", "score": 40})
                elif any(k in title for k in ["허용 url 2xx", "5xx 오류율", "http 200 상태"]):
                    if not sitemap_content: result.update({"message": "사이트맵을 찾을 수 없습니다."})
                    elif sitemap_ok: result.update({"status": "pass", "message": "사이트맵의 샘플 URL들이 모두 정상적으로 응답합니다(2xx).", "score": 100})
                    else: result.update({"message": "사이트맵의 일부 URL이 200 OK 상태가 아닙니다. 깨진 링크를 확인하세요."})
                elif "canonical url만 포함" in title:
                    if not sitemap_content: result.update({"message": "사이트맵을 찾을 수 없습니다."})
                    elif canonical_ok: result.update({"status": "pass", "message": "사이트맵의 샘플 URL들이 모두 Canonical URL로 확인됩니다.", "score": 100})
                    else: result.update({"message": "사이트맵에 Canonical URL이 아닌 페이지가 포함된 것으로 보입니다."})
                elif "이미지/비디오 맵" in title:
                    if not sitemap_content: result.update({"message": "사이트맵을 찾을 수 없습니다."})
                    elif sitemap_meta.get('has_image') or sitemap_meta.get('has_video'): result.update({"status": "pass", "message": "사이트맵에 이미지 또는 비디오 정보가 포함되어 있습니다.", "score": 100})
                    else: result.update({"status": "warning", "message": "사이트맵에 이미지/비디오 정보가 없어, 관련 콘텐츠의 색인 기회를 놓칠 수 있습니다.", "score": 70})
                elif "hreflang" in title:
                    if not sitemap_content: result.update({"message": "사이트맵을 찾을 수 없습니다."})
                    elif sitemap_meta.get('has_hreflang'): result.update({"status": "pass", "message": "사이트맵에 hreflang 정보가 포함되어 있습니다.", "score": 100})
                    else: result.update({"status": "warning", "message": "다국어 사이트의 경우, hreflang 정보 추가를 고려하세요.", "score": 70})
                elif "인덱스 sitemap 체계" in title:
                    if not sitemap_content: result.update({"message": "사이트맵을 찾을 수 없습니다."})
                    elif sitemap_meta.get('is_index'): result.update({"status": "pass", "message": "사이트맵 인덱스 파일을 사용하여 여러 사이트맵을 효율적으로 관리합니다.", "score": 100})
                    else: result.update({"status": "warning", "message": "URL이 많은 경우 사이트맵 인덱스 사용을 고려하세요.", "score": 70})
                elif "lastmod 정확도" in title or "<lastmod> 정확도" in title:
                    if not sitemap_content: 
                        result.update({"message": "사이트맵을 찾을 수 없습니다."})
                    else:
                        try:
                            print("lastmod 정확도 분석 시작...")
                            # 사이트맵에서 lastmod 정보를 추출하고 실제 페이지 수정 시간과 비교
                            root = ET.fromstring(sitemap_content)
                            ns = {'sm': 'http://www.sitemaps.org/schemas/sitemap/0.9'}
                            urls = root.findall('sm:url', ns)
                            
                            print(f"사이트맵에서 {len(urls)}개의 URL 발견")
                            
                            if not urls:
                                result.update({"message": "사이트맵에 URL이 없습니다."})
                            else:
                                # 샘플 URL들의 lastmod 정확도 검사
                                sample_count = min(5, len(urls))
                                accurate_count = 0
                                total_checked = 0
                                lastmod_found = 0
                                
                                for i, url_node in enumerate(urls[:sample_count]):
                                    lastmod_tag = url_node.find('sm:lastmod', ns)
                                    if lastmod_tag is not None:
                                        lastmod_found += 1
                                        lastmod_str = lastmod_tag.text
                                        print(f"URL {i+1}: lastmod = {lastmod_str}")
                                        try:
                                            # lastmod 시간을 파싱
                                            lastmod_time = datetime.datetime.fromisoformat(lastmod_str.replace('Z', '+00:00'))
                                            
                                            # 실제 페이지의 Last-Modified 헤더 확인
                                            page_url = url_node.find('sm:loc', ns).text
                                            print(f"페이지 확인: {page_url}")
                                            page_res = requests.head(page_url, timeout=5)
                                            
                                            if 'Last-Modified' in page_res.headers:
                                                header_time_str = page_res.headers['Last-Modified']
                                                header_time = datetime.datetime.strptime(header_time_str, '%a, %d %b %Y %H:%M:%S %Z')
                                                
                                                # 시간 차이 계산 (72시간 = 3일)
                                                time_diff = abs((lastmod_time - header_time).total_seconds() / 3600)
                                                print(f"시간 차이: {time_diff:.1f}시간")
                                                
                                                if time_diff <= 72:  # 72시간 이내
                                                    accurate_count += 1
                                                total_checked += 1
                                                
                                        except (ValueError, TypeError) as e:
                                            print(f"lastmod 파싱 실패: {e}")
                                            continue
                                    else:
                                        print(f"URL {i+1}: lastmod 태그 없음")
                                
                                print(f"lastmod 태그 발견: {lastmod_found}개, 검사 완료: {total_checked}개")
                                
                                if total_checked == 0:
                                    if lastmod_found == 0:
                                        result.update({"status": "fail", "message": "사이트맵에 lastmod 태그가 없습니다.", "score": 20})
                                    else:
                                        result.update({"status": "fail", "message": "lastmod 정보 파싱에 실패했습니다.", "score": 20})
                                elif accurate_count == total_checked:
                                    result.update({"status": "pass", "message": f"lastmod 정보가 샘플 {total_checked}개 모두 정확합니다 (±72시간).", "score": 100})
                                elif accurate_count >= total_checked * 0.8:
                                    result.update({"status": "warning", "message": f"lastmod 정보가 샘플 {total_checked}개 중 {accurate_count}개만 정확합니다.", "score": 70})
                                else:
                                    result.update({"status": "fail", "message": f"lastmod 정보가 샘플 {total_checked}개 중 {accurate_count}개만 정확합니다. 정확도가 낮습니다.", "score": 30})
                                    
                        except Exception as e:
                            print(f"lastmod 정확도 분석 중 예외 발생: {e}")
                            result.update({"status": "fail", "message": f"lastmod 정확도 분석 중 오류: {e}", "score": 20})
                else:
                    result.update({"status": "pending", "message": "분석 미지원"})
            except Exception as e:
                result.update({"message": f"분석 중 오류 발생: {e}"})
            results.append(result)
        return results

    def _perform_page_optimization_checks(self, checks_config: List[Dict]) -> List[Dict]:
        """'페이지 최적화' 섹션의 메타 태그, H1 등을 분석합니다."""
        results = []
        for item in checks_config:
            result = item.copy()
            result.update({"status": "fail", "message": "분석 중 오류", "score": 0})
            check_title = item.get("title", "").lower()

            try:
                # Title 태그 검사
                if "title 30–60" in check_title:
                    title_tag = self.soup.find('title')
                    if title_tag:
                        title_text = title_tag.get_text(strip=True)
                        title_len = len(title_text)
                        if 30 <= title_len <= 60:
                            result.update({"status": "pass", "message": f"제목 길이가 최적화되었습니다 ({title_len}자).", "score": 100})
                        else:
                            result.update({"message": f"제목 길이가 권장 범위(30~60자)를 벗어납니다 ({title_len}자).", "score": 40})
                    else:
                        result.update({"message": "페이지에 <title> 태그가 존재하지 않습니다."})
                
                # Meta Description 검사
                elif "meta description" in check_title:
                    desc_tag = self.soup.find('meta', attrs={'name': 'description'})
                    if desc_tag and desc_tag.get('content'):
                        desc_text = desc_tag['content'].strip()
                        desc_len = len(desc_text)
                        if 70 <= desc_len <= 155:
                            result.update({"status": "pass", "message": f"메타 디스크립션 길이가 적절합니다 ({desc_len}자).", "score": 100})
                        else:
                            result.update({"message": f"메타 디스크립션 길이가 권장 범위(70~155자)를 벗어납니다 ({desc_len}자).", "score": 40})
                    else:
                        result.update({"message": "메타 디스크립션이 없거나 내용이 비어있습니다."})
                        
                # H1 태그 검사
                elif "h1 단일 존재" in check_title:
                    h1_tags = self.soup.find_all('h1')
                    h1_count = len(h1_tags)
                    if h1_count == 1:
                        result.update({"status": "pass", "message": "H1 태그가 1개 존재하여 명확한 주제를 전달합니다.", "score": 100})
                    elif h1_count == 0:
                        result.update({"message": "페이지의 핵심 주제를 나타내는 H1 태그가 없습니다."})
                    else:
                        result.update({"message": f"H1 태그가 {h1_count}개 존재합니다. 페이지당 하나만 사용하는 것을 권장합니다.", "score": 20})
                
                else:
                    result.update({"status": "pending", "message": "분석 미지원"})

            except Exception as e:
                result.update({"message": f"알 수 없는 오류: {e}"})
            
            results.append(result)
        return results

    def _perform_security_trust_checks(self, checks_config: List[Dict]) -> List[Dict]:
        """'보안 및 신뢰성' 섹션의 보안 헤더 등을 분석합니다."""
        results = []
        for item in checks_config:
            result = item.copy()
            result.update({"status": "fail", "message": "분석 중 오류", "score": 0})
            check_title = item.get("title", "").lower()

            try:
                if "hsts" in check_title:
                    hsts_header = self.headers.get('Strict-Transport-Security')
                    if hsts_header:
                        max_age_match = re.search(r'max-age=(\d+)', hsts_header)
                        if max_age_match and int(max_age_match.group(1)) >= 31536000:
                            result.update({"status": "pass", "message": "HSTS 헤더가 1년 이상으로 강력하게 설정되었습니다.", "score": 100})
                        else:
                            result.update({"message": "HSTS 헤더가 존재하지만 max-age가 1년 미만입니다.", "score": 50})
                    else:
                        result.update({"message": "HSTS(Strict-Transport-Security) 헤더가 없습니다."})

                elif "content-security-policy" in check_title:
                    if 'Content-Security-Policy' in self.headers:
                        result.update({"status": "pass", "message": "CSP 헤더가 존재하여 XSS 공격 방어에 도움이 됩니다.", "score": 100})
                    else:
                        result.update({"message": "CSP(Content-Security-Policy) 헤더가 없어 보안이 취약할 수 있습니다."})

                elif "x-frame-options" in check_title:
                    header_val = self.headers.get('X-Frame-Options', '').upper()
                    if header_val in ['DENY', 'SAMEORIGIN']:
                        result.update({"status": "pass", "message": f"X-Frame-Options 헤더가 '{header_val}'로 설정되어 클릭재킹을 방어합니다.", "score": 100})
                    else:
                        result.update({"message": "X-Frame-Options 헤더가 없어 클릭재킹 공격에 취약할 수 있습니다."})
                
                elif "x-content-type-options" in check_title:
                    if self.headers.get('X-Content-Type-Options', '').lower() == 'nosniff':
                        result.update({"status": "pass", "message": "X-Content-Type-Options 헤더가 'nosniff'로 설정되어 MIME 스니핑을 방지합니다.", "score": 100})
                    else:
                        result.update({"message": "X-Content-Type-Options 헤더가 설정되지 않았습니다."})
                
                else:
                    result.update({"status": "pending", "message": "분석 미지원"})
            except Exception as e:
                result.update({"message": f"알 수 없는 오류: {e}"})
            
            results.append(result)
        return results

    def _perform_web_performance_checks(self, checks_config: List[Dict]) -> List[Dict]:
        """'웹 성능 및 기술적 최적화' 섹션을 PageSpeed Insights 결과 또는 기본 메트릭으로 분석합니다."""
        results = []
        # PageSpeed Insights 결과 또는 기본 메트릭에서 데이터를 가져옵니다.
        lighthouse_result = self.pagespeed_results.get('lighthouseResult', {})
        metrics = lighthouse_result.get('audits', {})
        basic_metrics = self.pagespeed_results.get('basicMetrics', {})
        
        for item in checks_config:
            result = item.copy()
            result.update({"status": "fail", "message": "성능 데이터를 가져올 수 없습니다.", "score": 0})
            check_title = item.get("title", "").lower()

            try:
                if "lcp" in check_title:
                    lcp_metric = metrics.get('largest-contentful-paint', {})
                    lcp_value = lcp_metric.get('numericValue', 0) / 1000
                    
                    # 기본 메트릭이 있는 경우 페이지 로드 시간을 대체로 사용
                    if lcp_value == 0 and basic_metrics:
                        page_load_time = basic_metrics.get('pageLoadTime', 0) / 1000
                        if page_load_time > 0:
                            lcp_value = page_load_time * 0.6  # 일반적으로 LCP는 전체 로드 시간의 60% 정도
                    
                    if lcp_value > 0 and lcp_value <= 2.5:
                        result.update({"status": "pass", "message": f"LCP가 {lcp_value:.2f}초로 우수합니다.", "score": 100})
                    else:
                        result.update({"message": f"LCP가 {lcp_value:.2f}초로 개선이 필요합니다 (권장: 2.5초 이하).", "score": 30 if lcp_value > 4 else 60})

                elif "inp" in check_title:
                    # PageSpeed API v5는 INP를 직접 제공하지 않음. TBT로 대체하여 유사하게 판단.
                    tbt_metric = metrics.get('total-blocking-time', {})
                    tbt_value = tbt_metric.get('numericValue', 0)
                    
                    # 기본 메트릭이 있는 경우 DOM 로드 시간을 대체로 사용
                    if tbt_value == 0 and basic_metrics:
                        dom_load_time = basic_metrics.get('domContentLoaded', 0)
                        if dom_load_time > 0:
                            tbt_value = dom_load_time * 0.3  # 일반적으로 TBT는 DOM 로드 시간의 30% 정도
                    
                    if tbt_value <= 200:
                         result.update({"status": "pass", "message": f"TBT가 {tbt_value:.0f}ms로 우수하여 INP도 양호할 것으로 예상됩니다.", "score": 100})
                    else:
                         result.update({"message": f"TBT가 {tbt_value:.0f}ms로 높아 INP 개선이 필요할 수 있습니다 (권장: 200ms 이하).", "score": 30 if tbt_value > 600 else 60})

                elif "cls" in check_title:
                    cls_metric = metrics.get('cumulative-layout-shift', {})
                    cls_value = cls_metric.get('numericValue', 0)
                    
                    # 기본 메트릭이 있는 경우 이미지 수를 기반으로 추정
                    if cls_value == 0 and basic_metrics:
                        image_count = basic_metrics.get('imageCount', 0)
                        if image_count > 10:
                            cls_value = 0.15  # 이미지가 많으면 CLS가 높을 가능성
                        elif image_count > 5:
                            cls_value = 0.1   # 중간 정도
                        else:
                            cls_value = 0.05  # 이미지가 적으면 CLS가 낮을 가능성
                    
                    if cls_value < 0.1:
                        result.update({"status": "pass", "message": f"CLS가 {cls_value:.3f}로 매우 안정적입니다.", "score": 100})
                    else:
                        result.update({"message": f"CLS가 {cls_value:.3f}로 레이아웃 불안정성이 높습니다 (권장: 0.1 미만).", "score": 30 if cls_value > 0.25 else 60})

                elif "ttfb" in check_title:
                    ttfb_metric = metrics.get('server-response-time', {})
                    ttfb_value = ttfb_metric.get('numericValue', 0) / 1000
                    
                    # 기본 메트릭이 있는 경우 DOM 로드 시간을 대체로 사용
                    if ttfb_value == 0 and basic_metrics:
                        dom_load_time = basic_metrics.get('domContentLoaded', 0) / 1000
                        if dom_load_time > 0:
                            ttfb_value = dom_load_time * 0.4  # 일반적으로 TTFB는 DOM 로드 시간의 40% 정도
                    
                    if ttfb_value > 0 and ttfb_value <= 0.8:
                        result.update({"status": "pass", "message": f"TTFB가 {ttfb_value:.3f}초로 빠릅니다.", "score": 100})
                    else:
                        result.update({"message": f"TTFB가 {ttfb_value:.3f}초로 느립니다 (권장: 0.8초 이하).", "score": 40})
                
                # ✅ 모바일 친화성 검사 로직 추가
                elif "모바일 친화성" in check_title or "viewport" in check_title:
                    # soup에서 직접 viewport 태그를 확인
                    viewport_tag = self.soup.find('meta', attrs={'name': 'viewport'})
                    if viewport_tag and 'width=device-width' in viewport_tag.get('content', ''):
                         result.update({"status": "pass", "message": "Viewport 메타 태그가 올바르게 설정되었습니다.", "score": 100})
                    else:
                         result.update({"message": "페이지가 모바일 친화적이지 않거나 Viewport 설정이 올바르지 않습니다.", "score": 20})
                
                # ✅ 추가 성능 메트릭들
                elif "페이지 크기" in check_title:
                    if basic_metrics:
                        total_size_kb = basic_metrics.get('totalSizeKB', 0)
                        if total_size_kb <= 500:  # 500KB 이하
                            result.update({"status": "pass", "message": f"페이지 크기가 {total_size_kb:.1f}KB로 적절합니다.", "score": 100})
                        elif total_size_kb <= 1000:  # 1MB 이하
                            result.update({"status": "warning", "message": f"페이지 크기가 {total_size_kb:.1f}KB로 다소 큽니다.", "score": 70})
                        else:
                            result.update({"message": f"페이지 크기가 {total_size_kb:.1f}KB로 너무 큽니다 (500KB 이하 권장).", "score": 30})
                    else:
                        result.update({"message": "페이지 크기 정보를 가져올 수 없습니다."})
                
                elif "이미지 최적화" in check_title:
                    if basic_metrics:
                        image_size_kb = basic_metrics.get('imageSizeKB', 0)
                        total_size_kb = basic_metrics.get('totalSizeKB', 1)
                        image_ratio = (image_size_kb / total_size_kb) * 100 if total_size_kb > 0 else 0
                        
                        if image_ratio <= 50:  # 이미지가 전체의 50% 이하
                            result.update({"status": "pass", "message": f"이미지 크기가 전체의 {image_ratio:.1f}%로 적절합니다.", "score": 100})
                        elif image_ratio <= 70:
                            result.update({"status": "warning", "message": f"이미지 크기가 전체의 {image_ratio:.1f}%로 다소 큽니다.", "score": 70})
                        else:
                            result.update({"message": f"이미지 크기가 전체의 {image_ratio:.1f}%로 너무 큽니다 (50% 이하 권장).", "score": 30})
                    else:
                        result.update({"message": "이미지 크기 정보를 가져올 수 없습니다."})
                
                else:
                    result.update({"status": "pending", "message": "분석 미지원"})
            except Exception as e:
                result.update({"message": f"알 수 없는 오류: {e}"})
            
            results.append(result)
        return results

    def _perform_url_content_standardization_checks(self, checks_config: List[Dict]) -> List[Dict]:
        """'URL 및 콘텐츠 표준화' 섹션의 Canonical URL 등을 분석합니다."""
        results = []
        for item in checks_config:
            result = item.copy()
            result.update({"status": "fail", "message": "분석 중 오류", "score": 0})
            check_title = item.get("title", "").lower()

            try:
                if "self-canonical" in check_title:
                    canonical_tag = self.soup.find('link', rel='canonical')
                    if canonical_tag and canonical_tag.get('href'):
                        canonical_url = urllib.parse.urljoin(self.url, canonical_tag['href'])
                        if self._normalize_url(canonical_url) == self._normalize_url(self.url):
                            result.update({"status": "pass", "message": "자기 자신을 가리키는 Canonical 태그가 올바르게 설정되었습니다.", "score": 100})
                        else:
                            result.update({"message": f"Canonical URL({canonical_url})이 현재 페이지 URL과 다릅니다.", "score": 30})
                    else:
                        result.update({"message": "페이지에 Canonical 태그가 없습니다."})
                
                # 'Canonical URL만 포함'은 사이트맵 검사 시 이미 간접적으로 확인되므로, 여기서는 Self-canonical에 집중
                # 다른 항목들은 AI 분석이나 심층 크롤링이 필요
                else:
                    result.update({"status": "pending", "message": "분석 미지원"})
            except Exception as e:
                result.update({"message": f"알 수 없는 오류: {e}"})

            results.append(result)
        return results

    def _normalize_url(self, url: str) -> str:
        """URL을 정규화하여 비교하기 쉽게 만듭니다 (www 제거, / 제거 등)."""
        parsed = urllib.parse.urlparse(url.lower())
        netloc = parsed.netloc.replace('www.', '')
        path = parsed.path.rstrip('/')
        return f"{parsed.scheme}://{netloc}{path}"

    def _calculate_average_score(self, scores: List[int]) -> int:
        if not scores:
            return 100 # 또는 0, 정책에 따라
        return int(round(sum(scores) / len(scores)))

    def _calculate_weighted_score(self, checks: List[Dict]) -> int:
        relevant_checks = [c for c in checks if c.get("status") != "pending"]
        if not relevant_checks: return 100
        total_importance = sum(c.get('importance', 1) for c in relevant_checks)
        weighted_score_sum = sum(c.get('score', 0) * c.get('importance', 1) for c in relevant_checks)
        return int(round(weighted_score_sum / total_importance)) if total_importance > 0 else 100
        
    def _compile_final_result(self, categories_results: List[Dict]) -> Dict:
        all_checks = [check for cat in categories_results for sec in cat["sections"] for check in sec["checks"]]
        total_checks = len(all_checks)
        passed_checks = len([c for c in all_checks if c["status"] == "pass"])
        
        category_scores = {cat["category_id"]: cat["category_score"] for cat in categories_results}
        
        # 가중 평균으로 전체 점수 계산
        weights = {"technical_seo": 0.8, "ai_content_quality": 0.2}
        weighted_sum = sum(category_scores.get(cid, 0) * w for cid, w in weights.items())
        total_weight = sum(w for cid, w in weights.items() if cid in category_scores)
        overall_score = int(round(weighted_sum / total_weight)) if total_weight > 0 else 0

        failed_checks = [c for c in all_checks if c["status"] == 'fail']
        sorted_failed = sorted(failed_checks, key=lambda x: x.get('importance', 0), reverse=True)

        return {
            "url": self.url,
            "checklist_version": self.checklist.get("seo_checklist", {}).get("metadata", {}).get("version", "1.0"),
            "categories": categories_results,
            "overall_score": overall_score,
            "category_scores": category_scores,
            "total_checks": total_checks,
            "passed_checks": passed_checks,
            "failed_checks": total_checks - passed_checks,
            "critical_issues": [f"{c['title']}: {c['message']}" for c in sorted_failed if c.get('importance', 0) >= 4][:5],
            "recommendations": [{
                "priority": "높음" if c.get('importance', 0) >= 4 else "중간",
                "title": c['title'], "description": c['message'], "category": "N/A", "expected_impact": "개선 필요"
            } for c in sorted_failed]
        } 