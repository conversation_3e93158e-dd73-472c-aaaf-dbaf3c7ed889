#!/usr/bin/env node

// Playwright 프로세스 모니터링 및 정리 스크립트

const { execSync, exec } = require('child_process');

class PlaywrightProcessMonitor {
  constructor() {
    this.isMonitoring = false;
    this.cleanupInterval = null;
  }

  // 현재 실행 중인 브라우저 프로세스 확인
  checkRunningProcesses() {
    try {
      const result = execSync('ps aux | grep -E "(chromium|chrome|playwright)" | grep -v grep', 
        { encoding: 'utf8', stdio: 'pipe' });
      
      if (result.trim()) {
        console.log('🔍 실행 중인 브라우저 프로세스:');
        console.log(result);
        return result.split('\n').filter(line => line.trim()).length;
      }
      return 0;
    } catch (error) {
      return 0; // 프로세스가 없으면 0 반환
    }
  }

  // 좀비 프로세스 정리
  cleanupZombieProcesses() {
    console.log('🧹 좀비 프로세스 정리 시작...');
    
    const commands = [
      // 1. 일반적인 종료 시도
      'pkill -TERM -f "chromium|chrome|playwright"',
      // 2. 강제 종료
      'pkill -KILL -f "chromium|chrome|playwright"',
      // 3. 좀비 프로세스 정리
      'ps aux | grep -E "(chromium|chrome|playwright)" | grep -v grep | awk \'{print $2}\' | xargs -r kill -9',
      // 4. 임시 파일 정리
      'find /tmp -name "*chromium*" -type f -mtime +1 -delete 2>/dev/null || true',
      'find /tmp -name "*chrome*" -type f -mtime +1 -delete 2>/dev/null || true'
    ];

    commands.forEach((cmd, index) => {
      try {
        console.log(`${index + 1}. ${cmd}`);
        execSync(cmd, { stdio: 'ignore', timeout: 5000 });
        // 각 명령어 사이에 잠시 대기
        if (index < commands.length - 1) {
          require('child_process').execSync('sleep 1');
        }
      } catch (error) {
        // 에러 무시 (프로세스가 없을 수도 있음)
      }
    });

    // 정리 후 상태 확인
    const remainingProcesses = this.checkRunningProcesses();
    if (remainingProcesses === 0) {
      console.log('✅ 모든 브라우저 프로세스가 정리되었습니다.');
    } else {
      console.log(`⚠️  ${remainingProcesses}개의 프로세스가 여전히 실행 중입니다.`);
    }
  }

  // 주기적 모니터링 시작
  startMonitoring(intervalMinutes = 5) {
    if (this.isMonitoring) {
      console.log('이미 모니터링이 실행 중입니다.');
      return;
    }

    console.log(`🔄 ${intervalMinutes}분마다 프로세스 모니터링을 시작합니다.`);
    this.isMonitoring = true;

    this.cleanupInterval = setInterval(() => {
      const processCount = this.checkRunningProcesses();
      if (processCount > 10) { // 임계값 설정
        console.log(`⚠️  브라우저 프로세스가 ${processCount}개 실행 중입니다. 정리를 시작합니다.`);
        this.cleanupZombieProcesses();
      }
    }, intervalMinutes * 60 * 1000);
  }

  // 모니터링 중단
  stopMonitoring() {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
      this.cleanupInterval = null;
      this.isMonitoring = false;
      console.log('🛑 프로세스 모니터링을 중단했습니다.');
    }
  }

  // 즉시 정리 실행
  forceCleanup() {
    console.log('🚨 강제 프로세스 정리를 실행합니다.');
    this.cleanupZombieProcesses();
  }
}

// CLI 사용법
if (require.main === module) {
  const monitor = new PlaywrightProcessMonitor();
  const command = process.argv[2];

  switch (command) {
    case 'check':
      monitor.checkRunningProcesses();
      break;
    case 'cleanup':
      monitor.forceCleanup();
      break;
    case 'monitor':
      const interval = parseInt(process.argv[3]) || 5;
      monitor.startMonitoring(interval);
      // Ctrl+C로 종료할 수 있도록 설정
      process.on('SIGINT', () => {
        monitor.stopMonitoring();
        process.exit(0);
      });
      break;
    default:
      console.log('사용법:');
      console.log('  node process-monitor.js check    - 현재 프로세스 확인');
      console.log('  node process-monitor.js cleanup  - 즉시 정리 실행');
      console.log('  node process-monitor.js monitor [분] - 주기적 모니터링 (기본 5분)');
  }
}

module.exports = PlaywrightProcessMonitor;
