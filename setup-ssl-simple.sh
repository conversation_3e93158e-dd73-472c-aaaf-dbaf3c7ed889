#!/bin/bash

# 간단한 SSL 인증서 설정 스크립트 (standalone 모드 사용)
# 사용법: ./setup-ssl-simple.sh seoapi.pxd.co.kr

DOMAIN=$1

if [ -z "$DOMAIN" ]; then
    echo "사용법: $0 <도메인명>"
    echo "예시: $0 seoapi.pxd.co.kr"
    exit 1
fi

echo "=== 간단한 SSL 인증서 설정을 시작합니다 ==="
echo "도메인: $DOMAIN"

# 1. Docker 컨테이너 중단 (포트 80, 443 해제)
echo "1. Docker 컨테이너 중단..."
docker-compose down

# 2. 필요한 디렉토리 생성
echo "2. 필요한 디렉토리 생성..."
sudo mkdir -p /var/www/certbot

# 3. Certbot standalone 모드로 SSL 인증서 발급
echo "3. SSL 인증서 발급 중 (standalone 모드)..."
sudo certbot certonly \
  --standalone \
  --preferred-challenges http \
  --email admin@$DOMAIN \
  --agree-tos \
  --no-eff-email \
  -d $DOMAIN

# 4. 인증서 발급 확인
if [ -f "/etc/letsencrypt/live/$DOMAIN/fullchain.pem" ]; then
    echo "✅ SSL 인증서가 성공적으로 발급되었습니다!"
    
    # 5. Docker Compose로 서비스 재시작
    echo "4. Docker Compose 서비스 재시작..."
    docker-compose up -d
    
    echo "✅ HTTPS 설정이 완료되었습니다!"
    echo "🌐 https://$DOMAIN 으로 접속해보세요."
    
    # 6. 자동 갱신 설정
    echo "5. SSL 인증서 자동 갱신 설정..."
    
    # 기존 cron job 제거 후 새로 추가
    (crontab -l 2>/dev/null | grep -v "certbot renew") | crontab -
    (crontab -l 2>/dev/null; echo "0 12 * * * /usr/bin/certbot renew --quiet --pre-hook 'docker-compose -f $(pwd)/docker-compose.yml down' --post-hook 'docker-compose -f $(pwd)/docker-compose.yml up -d'") | crontab -
    
    echo "✅ SSL 인증서 자동 갱신이 설정되었습니다."
    
    # 7. 설정 확인
    echo "6. 설정 확인..."
    echo "- 인증서 위치: /etc/letsencrypt/live/$DOMAIN/"
    echo "- 만료일: $(sudo openssl x509 -enddate -noout -in /etc/letsencrypt/live/$DOMAIN/cert.pem)"
    
else
    echo "❌ SSL 인증서 발급에 실패했습니다."
    echo "다음 사항을 확인해주세요:"
    echo "1. 도메인 $DOMAIN 이 이 서버 IP를 올바르게 가리키는지 확인"
    echo "2. 포트 80이 방화벽에서 열려있는지 확인"
    echo "3. 다른 웹서버가 포트 80을 사용하고 있지 않은지 확인"
    
    # Docker 서비스 재시작 (HTTP로라도 서비스 유지)
    echo "HTTP 서비스를 재시작합니다..."
    docker-compose up -d
    exit 1
fi

echo "=== SSL 설정이 완료되었습니다! ==="
