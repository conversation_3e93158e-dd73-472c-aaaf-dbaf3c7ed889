import React from "react";
import styles from "./PieChart.module.scss";
import classNames from "classnames/bind";

const cx = classNames.bind(styles);

interface PieChartProps {
  score: number;
  size?: number;
}

const PieChart: React.FC<PieChartProps> = ({ score, size = 120 }) => {
  const radius = size / 2 - 10;
  const circumference = 2 * Math.PI * radius;
  const strokeDasharray = circumference;
  const strokeDashoffset = circumference - (score / 100) * circumference;

  const getScoreColor = (score: number) => {
    if (score >= 80) return "#10b981"; // 우수 - 초록색
    if (score >= 60) return "#3b82f6"; // 양호 - 파란색
    if (score >= 40) return "#f59e0b"; // 보통 - 주황색
    return "#ef4444"; // 개선필요 - 빨간색
  };

  const getScoreLabel = (score: number) => {
    if (score >= 80) return "우수";
    if (score >= 60) return "양호";
    if (score >= 40) return "보통";
    return "개선필요";
  };

  const scoreColor = getScoreColor(score);

  return (
    <div className={cx("pie-chart", `size-${size}`)}>
      <svg width={size} height={size} className={cx("svg")}>
        {/* 배경 원 */}
        <circle
          cx={size / 2}
          cy={size / 2}
          r={radius}
          fill="none"
          stroke="rgba(255, 255, 255, 0.1)"
          strokeWidth="8"
        />
        {/* 점수 원 */}
        <circle
          cx={size / 2}
          cy={size / 2}
          r={radius}
          fill="none"
          stroke={scoreColor}
          strokeWidth="8"
          strokeLinecap="round"
          strokeDasharray={strokeDasharray}
          strokeDashoffset={strokeDashoffset}
          className={cx(
            "progress-circle",
            `score-${score}`,
            `color-${getScoreColor(score).replace("#", "")}`
          )}
        />
      </svg>
      <div className={cx("content")}>
        <div
          className={cx(
            "score",
            `score-${score}`,
            `color-${getScoreColor(score).replace("#", "")}`
          )}
        >
          {score}
        </div>
        <div
          className={cx(
            "label",
            `score-${score}`,
            `color-${getScoreColor(score).replace("#", "")}`
          )}
        >
          {getScoreLabel(score)}
        </div>
      </div>
    </div>
  );
};

export default PieChart;
