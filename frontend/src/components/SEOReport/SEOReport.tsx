import React, { useState, use<PERSON>emo } from "react";
import {
  CheckCircle,
  AlertCircle,
  XCircle,
  Lightbulb,
  ChevronDown,
  ChevronUp,
  HelpCircle,
  MinusCircle,
} from "lucide-react";
import { SEOAnalysisResult, SectionResult, CheckInfo } from "../../types";
import styles from "./SEOReport.module.scss";
import classNames from "classnames/bind";

const cx = classNames.bind(styles);

interface SEOReportProps {
  result: SEOAnalysisResult;
}

const getScoreClass = (score: number) => {
  if (score >= 90) return "excellent";
  if (score >= 70) return "good";
  if (score >= 40) return "warning";
  return "danger";
};

const getStatusIcon = (
  status: "pass" | "fail" | "warning" | "pending" | "optional_skip"
) => {
  switch (status) {
    case "pass":
      return <CheckCircle className={cx("status-pass")} />;
    case "fail":
      return <XCircle className={cx("status-fail")} />;
    case "warning":
      return <AlertCircle className={cx("status-warning")} />;
    case "optional_skip":
      return <MinusCircle className={cx("status-optional-skip")} />;
    case "pending":
      return <HelpCircle className={cx("status-pending")} />;
    default:
      return <HelpCircle className={cx("status-pending")} />;
  }
};

const getImportanceClass = (importance: number) => {
  if (importance >= 4) return "importance-high";
  if (importance >= 3) return "importance-medium";
  return "importance-low";
};

const getImportanceText = (importance: number) => {
  if (importance >= 4) return "높음";
  if (importance >= 3) return "중간";
  return "낮음";
};

const AiSuggestions: React.FC<{
  suggestions: SectionResult["ai_suggestions"];
  checks: CheckInfo[];
  sectionId: string;
}> = ({ suggestions, checks, sectionId }) => {
  const checkIdToTitleMap = useMemo(
    () =>
      checks.reduce((acc, check) => {
        acc[check.id] = check.title;
        return acc;
      }, {} as Record<number, string>),
    [checks]
  );

  const checkIdToImportanceMap = useMemo(
    () =>
      checks.reduce((acc, check) => {
        acc[check.id] = check.importance;
        return acc;
      }, {} as Record<number, number>),
    [checks]
  );

  if (!suggestions || suggestions.length === 0) {
    return null;
  }

  // 중복 제거: 같은 ID의 제안은 첫 번째 것만 유지
  const uniqueSuggestions =
    suggestions?.reduce((acc, suggestion) => {
      const existingIndex = acc.findIndex((s) => s.id === suggestion.id);
      if (existingIndex === -1) {
        acc.push(suggestion);
      }
      return acc;
    }, [] as typeof suggestions) || [];

  return (
    <div className={cx("ai-section-suggestions")}>
      <h5>
        <Lightbulb size={16} /> 항목별 개선 제안
      </h5>
      {/* 수동 제안 입력/표시는 사용하지 않음 */}

      {/* 기존 AI 제안 */}
      {uniqueSuggestions.length > 0 && (
        <ul>
          {uniqueSuggestions.map((suggestion, index) => {
            const importance = checkIdToImportanceMap[suggestion.id] || 1;
            return (
              <li
                key={`${sectionId}-suggestion-${suggestion.id}-${index}`}
                className={cx(
                  "suggestion-item",
                  getImportanceClass(importance)
                )}
              >
                <div className={cx("suggestion-header")}>
                  <strong>
                    {suggestion.title ||
                      checkIdToTitleMap[suggestion.id] ||
                      "알 수 없는 항목"}
                    :
                  </strong>
                  <div className={cx("suggestion-meta")}>
                    <span className={cx("importance-badge")}>
                      중요도: {getImportanceText(importance)}
                    </span>
                  </div>
                </div>
                <div className={cx("suggestion-content")}>
                  {suggestion.suggestion}
                </div>
                {suggestion.expected_impact && (
                  <div className={cx("expected-impact")}>
                    <span>[기대효과] </span>
                    <span>{suggestion.expected_impact}</span>
                  </div>
                )}
              </li>
            );
          })}
        </ul>
      )}
    </div>
  );
};

const Section: React.FC<{
  section: SectionResult;
  analysisMethod?: string;
}> = ({ section, analysisMethod }) => {
  const [isOpen, setIsOpen] = useState(true);

  return (
    <div className={cx("section-container")} id={section.section_id}>
      <div className={cx("section-header")} onClick={() => setIsOpen(!isOpen)}>
        <h4 className={cx("section-title")}>{section.section_name}</h4>
        <div className={cx("section-header-info")}>
          <span
            className={cx(
              "section-score",
              getScoreClass(section.section_score)
            )}
          >
            {section.section_score} / 100
          </span>
          {analysisMethod && (
            <span className={cx("analysis-method")}>{analysisMethod}</span>
          )}
          <span className={cx("section-toggle")}>
            {isOpen ? <ChevronUp /> : <ChevronDown />}
          </span>
        </div>
      </div>
      {isOpen && (
        <div className={cx("checklist-table-wrapper")}>
          <table className={cx("checklist-table")}>
            <thead>
              <tr>
                <th>상태</th>
                <th>항목</th>
                <th>결과</th>
                <th>기준</th>
                <th>중요도</th>
              </tr>
            </thead>
            <tbody>
              {section.checks.map((check, index) => (
                <tr key={`${section.section_id}-check-${check.id}-${index}`}>
                  <td>{getStatusIcon(check.status)}</td>
                  <td>{check.title}</td>
                  <td>{check.message}</td>
                  <td>{check.criteria}</td>
                  <td>{"★".repeat(check.importance)}</td>
                </tr>
              ))}
            </tbody>
          </table>
          <AiSuggestions
            suggestions={section.ai_suggestions}
            checks={section.checks}
            sectionId={section.section_id}
          />
        </div>
      )}
    </div>
  );
};

const SEOReport: React.FC<SEOReportProps> = ({ result }) => {
  return (
    <>
      <div className={cx("category-scores")}>
        {result.categories.map((category) => (
          <div key={category.category_id} className={cx("category-item")}>
            <div className={cx("category-detail")}>
              {category.sections.map((section) => (
                <Section key={section.section_id} section={section} />
              ))}
            </div>
          </div>
        ))}
      </div>
    </>
  );
};

export default SEOReport;
