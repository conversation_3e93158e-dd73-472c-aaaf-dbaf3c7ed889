import React, { useEffect, useState, useId } from "react";
import { useLocation, Navigate } from "react-router-dom";
import SEOReport from "../../components/SEOReport/SEOReport";
import { SEOAnalysisResult } from "../../types";
import styles from "./ResultPage.module.scss";
import classNames from "classnames/bind";

const cx = classNames.bind(styles);

interface CategoryInfo {
  id: string;
  name: string;
  score: number;
}

const getCategoryList = (result: SEOAnalysisResult): CategoryInfo[] => {
  if (!result || !result.categories) {
    return [];
  }
  return result.categories.flatMap((category) =>
    category.sections.map((section) => ({
      id: section.section_id,
      name: section.section_name,
      score: section.section_score,
    }))
  );
};

const getScoreClass = (score: number): string => {
  if (score >= 90) return "excellent";
  if (score >= 70) return "good";
  if (score >= 40) return "warning";
  return "danger";
};

const getStatusText = (score: number): string => {
  if (score >= 90) return "우수 상태";
  if (score >= 70) return "양호";
  if (score >= 40) return "개선 권장";
  return "긴급 개선 필요";
};

const getPriorityClass = (priority: string): string => {
  switch (priority) {
    case "높음":
      return "priorityHigh";
    case "중간":
      return "priorityMedium";
    case "낮음":
      return "priorityLow";
    default:
      return "";
  }
};

const ResultPage: React.FC = () => {
  const location = useLocation();
  const result = location.state?.result as SEOAnalysisResult;
  const [animatedScore, setAnimatedScore] = useState(0);
  const uniqueId = useId();
  useEffect(() => {
    if (result) {
      const targetScore = result.overall_score;
      if (targetScore === 0) {
        setAnimatedScore(0);
        return;
      }

      let current = 0;
      const increment = targetScore / 50;
      const timer = setInterval(() => {
        current += increment;
        if (current >= targetScore) {
          current = targetScore;
          clearInterval(timer);
        }
        setAnimatedScore(Math.floor(current));
      }, 30);

      return () => clearInterval(timer);
    }
  }, [result]);

  if (!result) {
    return <Navigate to="/" replace />;
  }

  const categoryList = getCategoryList(result);
  const conicGradient = `conic-gradient(#4c63b6 0deg ${
    result.overall_score * 3.6
  }deg, #e9ecef ${result.overall_score * 3.6}deg 360deg)`;

  const { ai_analysis, recommendations } = result;

  return (
    <div className={cx("dashboard")}>
      <div className={cx("main-score")}>
        <div className={cx("score-circle")}>
          <div
            className={cx("circle-bg")}
            style={{ background: conicGradient }}
          >
            <div className={cx("circle-inner")}>
              <div className={cx("score-number")}>{animatedScore}</div>
              <div className={cx("score-label")}>종합 점수</div>
            </div>
          </div>
        </div>
      </div>

      <div className={cx("categories-grid")}>
        {categoryList.map((category) => (
          <button
            type="button"
            key={`${category.id}-${uniqueId}`}
            className={cx("category-card", getScoreClass(category.score))}
            onClick={(e) => {
              e.preventDefault();
              const targetSection = document.getElementById(category.id);
              if (targetSection) {
                const targetPosition = targetSection.offsetTop - 100;
                window.scrollTo({
                  top: targetPosition,
                  behavior: "smooth",
                });
              }
            }}
          >
            <div className={cx("category-header")}>
              <div className={cx("category-info")}>
                <h3 className={cx("category-title")}>{category.name}</h3>
                <div className={cx("category-score")}>{category.score}/100</div>
              </div>
              <div className={cx("progress-bar")}>
                <div
                  className={cx("progress-fill")}
                  style={{ width: `${category.score}%` }}
                ></div>
              </div>
              <div className={cx("category-status")}>
                <div className={cx("status-icon")}></div>
                <span>{getStatusText(category.score)}</span>
              </div>
            </div>
          </button>
        ))}
      </div>

      <div className={cx("ai-insights")}>
        <div className={cx("insights-header")}>
          <div className={cx("insights-icon")}>🤖</div>
          <div className={cx("insights-title")}>AI 종합 분석 요약</div>
        </div>

        <p className={cx("ai-overall-summary")}>
          {ai_analysis.overall_summary}
        </p>

        <div className={cx("ai-highlights")}>
          <div className={cx("highlight-box")}>
            <h4>SEO 레벨</h4>
            <p>{ai_analysis.seo_level}</p>
          </div>
          <div className={cx("highlight-box")}>
            <h4>예상 개선 점수</h4>
            <p>+{ai_analysis.expected_score_improvement}</p>
          </div>
          <div className={cx("highlight-box")}>
            <h4>예상 소요 기간</h4>
            <p>{ai_analysis.implementation_timeline}</p>
          </div>
        </div>

        <div className={cx("insights-grid2")}>
          <div className={cx("insight-section")}>
            <h4>👍 강점</h4>
            <ul className={cx("insight-list")}>
              {ai_analysis.strengths.map((item, index) => (
                <li key={`strength-${index}`}>{item}</li>
              ))}
            </ul>
          </div>

          <div className={cx("insight-section")}>
            <h4>⚠️ 긴급 개선사항</h4>
            <ul className={cx("insight-list")}>
              {ai_analysis.technical_insights.map((item, index) => (
                <li key={`tech-insight-${index}`}>{item}</li>
              ))}
            </ul>
          </div>

          <div className={cx("insight-section")}>
            <h4>💡 주요 개선사항</h4>
            <ul className={cx("insight-list")}>
              {ai_analysis.priority_recommendations
                .filter((item) => item.priority === "높음")
                .map((item, index) => (
                  <li
                    key={`urgent-${index}`}
                    className={cx(getPriorityClass(item.priority))}
                  >
                    {item.title}
                  </li>
                ))}
            </ul>
          </div>

          <div className={cx("insight-section")}>
            <h4>✍️ 콘텐츠 개선사항</h4>
            <ul className={cx("insight-list")}>
              {ai_analysis.content_insights.map((item, index) => (
                <li key={`content-insight-${index}`}>{item}</li>
              ))}
            </ul>
          </div>
        </div>
      </div>

      <div className={cx("recommendations")}>
        <h4>📋 다음 단계 권장사항</h4>
        <p>현재 SEO 점수 향상을 위한 우선순위별 개선 계획:</p>
        <div className={cx("recommendations-grid")}>
          {recommendations.slice(0, 3).map((rec, index) => {
            const priorityColor = ["#dc2626", "#ff9a26", "#059669"];
            return (
              <div
                key={`rec-plan-${index}`}
                className={cx("recommendation-item")}
                style={{ borderLeftColor: priorityColor[index] }}
              >
                <strong
                  className={cx("recommendation-week")}
                  style={{ color: priorityColor[index] }}
                >
                  {index + 1}주차
                </strong>
                <br />
                <small className={cx("recommendation-title")}>
                  {rec.title}
                </small>
              </div>
            );
          })}
        </div>
      </div>

      {/* Detailed report is still available below the new dashboard */}
      <div className={cx("detailed-report-section")}>
        <SEOReport result={result} />
      </div>
    </div>
  );
};

export default ResultPage;
