import { defineConfig, devices } from "@playwright/test";

// 환경 변수 설정
const isCI = !!process.env.CI;
const isDevelopment = process.env.NODE_ENV === "development";
const isHeadless = isCI || process.env.HEADLESS === "true" || false; // 기본값을 false로 변경

// 프로세스 정리 함수
const setupProcessCleanup = () => {
  const cleanup = () => {
    console.log("🧹 Playwright 프로세스 정리 중...");
    // 강제로 모든 브라우저 프로세스 종료
    try {
      require("child_process").execSync(
        'pkill -f "chromium|chrome|playwright" || true',
        { stdio: "ignore" }
      );
    } catch (error) {
      // 에러 무시 (프로세스가 없을 수도 있음)
    }
  };

  // 다양한 종료 시그널에 대해 정리 함수 등록
  process.on("exit", cleanup);
  process.on("SIGINT", cleanup);
  process.on("SIGTERM", cleanup);
  process.on("SIGUSR1", cleanup);
  process.on("SIGUSR2", cleanup);
  process.on("uncaughtException", cleanup);
  process.on("unhandledRejection", cleanup);
};

// 프로세스 정리 설정 실행
setupProcessCleanup();

// SEO 분석 테스트를 위한 최적화된 설정
export default defineConfig({
  testDir: "./tests",

  // 병렬 실행 설정
  fullyParallel: true,
  forbidOnly: isCI,

  // 재시도 설정 (CI에서는 더 많은 재시도)
  retries: isCI ? 3 : 1,

  // 워커 수 설정 (CI에서는 1개, 로컬에서는 CPU 코어 수에 따라)
  workers: isCI
    ? 1
    : process.env.WORKERS
    ? parseInt(process.env.WORKERS)
    : undefined,

  // 타임아웃 설정 (SEO 분석 시간을 고려하여 충분한 시간 할당)
  timeout: 300000, // 5분으로 증가
  expect: {
    timeout: 60000, // 60초로 증가
  },

  // 리포터 설정
  reporter: [
    ["html", { open: "never" }],
    ["json", { outputFile: "test-results/results.json" }],
    ["junit", { outputFile: "test-results/results.xml" }],
    ["list"], // 콘솔 출력
  ],

  // 글로벌 사용 설정
  use: {
    // 기본 URL 설정
    baseURL: "http://localhost:4000",

    // 브라우저 설정
    headless: isHeadless,
    viewport: { width: 1280, height: 720 },
    ignoreHTTPSErrors: true,

    // 타임아웃 설정
    actionTimeout: 60000, // 액션 타임아웃 (60초로 증가)
    navigationTimeout: 120000, // 네비게이션 타임아웃 (2분으로 증가)

    // 스크린샷 및 비디오 설정 - 수동 스크린샷 허용
    screenshot: "only-on-failure", // 실패 시에만 자동 스크린샷, 수동 스크린샷은 항상 허용
    video: "retain-on-failure",
    trace: "on-first-retry",

    // 추가 설정
    acceptDownloads: true,
    bypassCSP: true, // Content Security Policy 우회 (개발 환경)

    // 사용자 에이전트 설정
    userAgent:
      "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
  },

  // 브라우저 프로젝트 설정
  projects: [
    // Chromium (기본 브라우저)
    {
      name: "chromium",
      use: {
        ...devices["Desktop Chrome"],
        // Chromium 특화 설정
        launchOptions: {
          args: [
            "--disable-web-security",
            "--disable-features=VizDisplayCompositor",
            "--disable-dev-shm-usage",
            "--no-sandbox",
            "--disable-setuid-sandbox",
            "--disable-background-timer-throttling",
            "--disable-backgrounding-occluded-windows",
            "--disable-renderer-backgrounding",
            "--force-color-profile=srgb",
            "--disable-ipc-flooding-protection",
          ],
          // 브라우저 프로세스가 제대로 종료되도록 설정
          handleSIGINT: true,
          handleSIGTERM: true,
          handleSIGHUP: true,
        },
      },
    },
  ],

  // 웹 서버 설정
  webServer: {
    command: "cd frontend && npm run dev",
    url: "http://localhost:4000",
    reuseExistingServer: !isCI,
    timeout: 300000, // 5분으로 증가 (SEO 분석 시간 고려)
    stdout: "pipe",
    stderr: "pipe",
  },

  // 출력 디렉토리 설정
  outputDir: "test-results/",

  // 환경별 설정
  ...(isCI && {
    // CI 환경 특화 설정
    use: {
      ...devices["Desktop Chrome"],
      headless: true,
      viewport: { width: 1280, height: 720 },
      screenshot: "only-on-failure",
      video: "retain-on-failure",
      trace: "on-first-retry",
    },
    workers: 1,
    retries: 3,
    timeout: 300000, // 5분 (CI에서는 더 긴 타임아웃)
  }),

  // 개발 환경 특화 설정
  ...(isDevelopment && {
    use: {
      headless: false, // 개발 환경에서만 헤드리스 해제
      screenshot: "on",
      video: "on",
      trace: "on",
    },
    workers: 1,
    retries: 0,
  }),
});
