import React from "react";
import { Link, useLocation } from "react-router-dom";
import { BarChart3, ExternalLink } from "lucide-react";
import styles from "./Header.module.scss";
import classNames from "classnames/bind";

const cx = classNames.bind(styles);

const Header: React.FC = () => {
  const location = useLocation();
  const isResultPage = location.pathname === "/result";

  // ResultPage에서 전달받은 state에서 URL 정보 추출
  const resultData = location.state?.result;

  return (
    <header className={cx("header")}>
      <div className={cx("container")}>
        <Link to="/" className={cx("logo")}>
          <BarChart3 size={24} />
          <h1>SEO Analytics Pro</h1>
        </Link>

        {isResultPage && resultData && (
          <div className={cx("url-info")}>
            <strong>분석 URL:</strong>{" "}
            <a
              href={resultData.url}
              target="_blank"
              rel="noopener noreferrer"
              className={cx("url-link")}
            >
              {resultData.url}
              <ExternalLink size={14} />
            </a>
          </div>
        )}
      </div>
    </header>
  );
};

export default Header;
