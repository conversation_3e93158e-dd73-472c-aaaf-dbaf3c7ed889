import React from "react";
import { Link, useLocation } from "react-router-dom";
import { BarChart3, ExternalLink } from "lucide-react";
import styles from "./Header.module.scss";
import classNames from "classnames/bind";
import { getApiUrl } from "../../config/api";

const cx = classNames.bind(styles);

const Header: React.FC = () => {
  const location = useLocation();
  const isResultPage = location.pathname.startsWith("/result");

  // ResultPage에서 전달받은 state 우선 사용
  const resultData = (location.state as any)?.result;
  const [analysisUrl, setAnalysisUrl] = React.useState<string | null>(
    resultData?.url || null
  );

  React.useEffect(() => {
    // state에 없고 /result/:id 형태인 경우 백엔드에서 URL만 로드
    if (!analysisUrl && isResultPage) {
      const match = location.pathname.match(/^\/result\/([^\/]+)$/);
      const analysisId = match?.[1];
      if (analysisId) {
        (async () => {
          try {
            const res = await fetch(getApiUrl(`/result/${analysisId}`));
            if (!res.ok) return;
            const data = await res.json();
            if (data?.url) setAnalysisUrl(data.url);
          } catch {
            // 무시 (헤더 표시는 필수 아님)
          }
        })();
      }
    }
  }, [location.pathname, isResultPage, analysisUrl]);

  return (
    <header className={cx("header")}>
      <div className={cx("container")}>
        <Link to="/" className={cx("logo")}>
          <BarChart3 size={24} />
          <h1>SEO Analytics Pro</h1>
        </Link>

        {isResultPage && (resultData?.url || analysisUrl) && (
          <div className={cx("url-info")}>
            <strong>분석 URL:</strong>{" "}
            <a
              href={resultData?.url || analysisUrl!}
              target="_blank"
              rel="noopener noreferrer"
              className={cx("url-link")}
            >
              {resultData?.url || analysisUrl}
              <ExternalLink size={14} />
            </a>
          </div>
        )}
      </div>
    </header>
  );
};

export default Header;
