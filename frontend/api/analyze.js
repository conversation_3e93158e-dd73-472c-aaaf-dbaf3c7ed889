// Vercel 서버리스 함수 - SEO 분석 전용 프록시
export default async function handler(req, res) {
  // CORS 헤더 설정
  res.setHeader("Access-Control-Allow-Origin", "*");
  res.setHeader(
    "Access-Control-Allow-Methods",
    "GET, POST, PUT, DELETE, OPTIONS"
  );
  res.setHeader("Access-Control-Allow-Headers", "Content-Type, Authorization");

  // OPTIONS 요청 처리
  if (req.method === "OPTIONS") {
    res.status(200).end();
    return;
  }

  // POST 요청만 허용
  if (req.method !== "POST") {
    res.status(405).json({ error: "Method not allowed" });
    return;
  }

  try {
    const backendUrl = "http://15.164.114.169/analyze";

    console.log("Analyze request:", {
      method: req.method,
      body: req.body,
      url: backendUrl,
    });

    // 백엔드로 요청 전달
    const response = await fetch(backendUrl, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(req.body),
    });

    if (!response.ok) {
      throw new Error(`Back<PERSON> responded with status: ${response.status}`);
    }

    const data = await response.json();

    // 백엔드 응답 전달
    res.status(200).json(data);
  } catch (error) {
    console.error("Proxy error:", error);
    res.status(500).json({
      error: "Proxy server error",
      details: error.message,
      timestamp: new Date().toISOString(),
    });
  }
}
