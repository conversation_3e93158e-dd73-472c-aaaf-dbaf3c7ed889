import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import URLForm from "../../components/URLForm/URLForm";
import LoadingSpinner from "../../components/LoadingSpinner/LoadingSpinner";
import { analyzeSEO } from "../../services/api";
import { AnalysisRequest, SEOAnalysisResult } from "../../types";
import {
  Search,
  Lightbulb,
  LayoutDashboard,
  TrendingUp,
  ShieldCheck,
  FileText,
} from "lucide-react";
import styles from "./HomePage.module.scss";
import classNames from "classnames/bind";
import axios from "axios";

const cx = classNames.bind(styles);

const HomePage: React.FC = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [isComplete, setIsComplete] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const navigate = useNavigate();

  const handleAnalysis = async (request: AnalysisRequest) => {
    setIsLoading(true);
    setIsComplete(false);
    setError(null);

    try {
      const result: SEOAnalysisResult = await analyzeSEO(request);

      // 분석 완료 상태를 잠시 보여줌
      setIsComplete(true);
      setTimeout(() => {
        // 결과를 state로 전달하며 결과 페이지로 이동
        navigate("/result", { state: { result } });
      }, 1000); // 1초 후 결과 페이지로 이동 (1.5초에서 단축)
    } catch (err) {
      setError(
        err instanceof Error ? err.message : "분석 중 오류가 발생했습니다."
      );
      setIsLoading(false);
    }
  };

  // 테스트 버튼 클릭 핸들러
  const handleTest = async () => {
    setIsLoading(true);
    setIsComplete(false);
    setError(null);
    try {
      const res = await axios.get("/api/analyze/test");

      // 분석 완료 상태를 잠시 보여줌
      setIsComplete(true);
      setTimeout(() => {
        navigate("/result", { state: { result: res.data } });
      }, 1000);
    } catch (err) {
      setError("테스트 데이터 불러오기 실패");
      setIsLoading(false);
    }
  };

  // 문제 많은 케이스 버튼 클릭 핸들러
  const handleTestBad = async () => {
    setIsLoading(true);
    setIsComplete(false);
    setError(null);
    try {
      const res = await axios.get("/api/analyze/test/bad");

      // 분석 완료 상태를 잠시 보여줌
      setIsComplete(true);
      setTimeout(() => {
        navigate("/result", { state: { result: res.data } });
      }, 1000);
    } catch (err) {
      setError("문제 많은 케이스 데이터 불러오기 실패");
      setIsLoading(false);
    }
  };

  if (isLoading || isComplete) {
    return <LoadingSpinner isComplete={isComplete} />;
  }

  return (
    <div className={cx("home-page")}>
      <div className={cx("hero")}>
        <div className={cx("hero-content")}>
          <h1 className={cx("title")}>SEO Analytics Pro</h1>
          <p className={cx("subtitle")}>
            pxd만의 사용자 경험(UX) 데이터와 성공 노하우를 AI에 결합하여
            <br />
            가장 효과적인 개선 전략과 즉시 실행 가능한 가이드를 제공합니다.
          </p>

          <URLForm
            onSubmit={handleAnalysis}
            isLoading={isLoading}
            onTestSubmit={handleTest}
            onTestBadSubmit={handleTestBad}
          />

          {error && (
            <div className={cx("error")}>
              <p>{error}</p>
            </div>
          )}
        </div>
      </div>

      <div className={cx("features")}>
        <div className={cx("features-container")}>
          <h2 className={cx("features-title")}>주요 기능 or 주요 키워드</h2>
          <div className={cx("features-grid")}>
            <div className={cx("feature-card")}>
              <Search className={cx("feature-icon")} />
              <h3>쉽고 빠른 SEO 진단</h3>
              <p>
                복잡한 절차 없이, 웹사이트 주소만 입력하면 자동으로 SEO 상태를
                분석합니다.
              </p>
            </div>

            <div className={cx("feature-card")}>
              <Lightbulb className={cx("feature-icon")} />
              <h3>맞춤형 개선 가이드 제공</h3>
              <p>
                분석 결과를 바탕으로, 사이트에 꼭 필요한 개선 사항과 실질적인
                실행 방법을 단계별로 안내합니다.
              </p>
            </div>

            <div className={cx("feature-card")}>
              <LayoutDashboard className={cx("feature-icon")} />
              <h3>사용자 친화적 인터페이스</h3>
              <p>
                쉽게 이해할 수 있는 직관적인 대시보드의 시각화로, 결과를
                명확하게 파악하고 우선순위별로 정리되어 있어 효율적인 의사결정이
                가능합니다.
              </p>
            </div>

            <div className={cx("feature-card")}>
              <TrendingUp className={cx("feature-icon")} />
              <h3>성능 및 사용자 경험 분석</h3>
              <p>
                페이지 속도와 사용자 환경을 평가하여, 방문자 이탈을 막고
                검색엔진에 최적화된 사이트로 개선할 수 있도록 지원합니다.
              </p>
            </div>

            <div className={cx("feature-card")}>
              <ShieldCheck className={cx("feature-icon")} />
              <h3>기술적 요소 점검</h3>
              <p>
                SSL, 모바일 최적화, 구조화 데이터 등 기술적 SEO 요소까지
                빠짐없이 진단하고, 개선 방향을 구체적으로 제시합니다.
              </p>
            </div>

            <div className={cx("feature-card")}>
              <FileText className={cx("feature-icon")} />
              <h3>종합 진단 리포트</h3>
              <p>
                복잡한 분석 결과를 한눈에 파악할 수 있도록 핵심 인사이트를 담은
                보고서를 제공합니다. PDF 저장과 공유 기능으로 언제 어디서든
                손쉽게 내용을 전달하고 협업할 수 있습니다.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default HomePage;
