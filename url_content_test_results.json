[{"status": "warning", "message": "URL 형식이 부분적으로 개선이 필요합니다.", "score": 75}, {"status": "pass", "message": "중복 파라미터가 없습니다.", "score": 100}, {"status": "fail", "message": "Canonical 태그가 없습니다.", "score": 0}, {"status": "pass", "message": "Canonical 체인이 없습니다.", "score": 100}, {"status": "warning", "message": "교차 도메인 canonical이 설정되지 않았습니다.", "score": 50}, {"status": "pass", "message": "HTTPS 페이지가 올바르게 설정되었습니다.", "score": 100}, {"status": "pass", "message": "URL 파라미터가 적절합니다.", "score": 100}, {"status": "warning", "message": "언어/지역 세그먼트가 설정되지 않았습니다.", "score": 50}, {"status": "warning", "message": "리다이렉트 홉 확인이 필요합니다.", "score": 50}, {"status": "pass", "message": "콘텐츠 중복률이 0.0%로 적절합니다.", "score": 100}, {"status": "pass", "message": "제목 중복률이 0.0%로 적절합니다.", "score": 100}, {"status": "warning", "message": "메타 설명 수집이 필요합니다.", "score": 50}, {"status": "warning", "message": "콘텐츠가 충분하지 않습니다.", "score": 50}, {"status": "fail", "message": "얇은 콘텐츠 비율이 100.0%로 높습니다.", "score": 0}, {"status": "warning", "message": "Facet 파라미터 noindex 설정을 확인하세요.", "score": 50}, {"status": "pass", "message": "페이지네이션 페이지가 아닙니다.", "score": 100}, {"status": "warning", "message": "Syndication canonical 설정을 확인하세요.", "score": 50}, {"status": "pass", "message": "Session ID가 URL에 없습니다.", "score": 100}]