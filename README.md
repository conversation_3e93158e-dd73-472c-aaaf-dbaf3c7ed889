# 🔍 AI 기반 SEO 분석 도구 (xe-seo)

웹사이트의 SEO 상태를 종합적으로 분석하고 Google Gemini AI가 제안하는 맞춤형 개선 방안을 제공하는 도구입니다.

## ✨ 주요 기능

### 📊 종합 SEO 분석

- **기본 SEO 요소**: 메타태그, 제목, 설명, 헤딩 구조 분석
- **이미지 최적화**: Alt 텍스트, 접근성 분석
- **링크 구조**: 내부/외부 링크 분석
- **성능 측정**: 페이지 로딩 속도 및 Core Web Vitals
- **기술적 SEO**: SSL, 스키마 마크업, 모바일 최적화
- **콘텐츠 분석**: 키워드 밀도, 읽기 시간, 콘텐츠 품질
- **구조화 데이터**: Article, Product, FAQPage, VideoObject, ImageObject 등 Schema.org 마크업 분석
- **URL 및 콘텐츠 표준화**: Canonical URL, 중복 콘텐츠, URL 구조 최적화

### 🤖 AI 기반 개선 제안

- **Google Gemini 2.0 Flash**: 고도화된 SEO 분석 및 개선 제안
- **카테고리별 점수**: 콘텐츠 품질, 기술적 SEO, 사용자 경험, 모바일 최적화, 성능
- **우선순위 기반 개선안**: 높음/중간/낮음 우선순위별 실행 가능한 개선 방안
- **키워드 제안**: 타겟 키워드 및 콘텐츠 개선 방안
- **개별 항목별 제안**: 각 SEO 항목에 대한 맞춤형 개선 제안
- **예상 효과 제시**: 각 개선사항의 SEO 영향도 예측

### 📱 추가 분석 기능

- **모바일 최적화**: 반응형 디자인, 터치 요소 분석
- **접근성 분석**: ARIA 라벨, 폼 요소, 접근성 점수
- **보안 분석**: HTTPS, 혼합 콘텐츠, CSP 헤더
- **국제화 분석**: 다국어 지원, hreflang 태그

## 🛠 기술 스택

### 백엔드

- **Python 3.9+**
- **FastAPI**: 고성능 비동기 웹 API 프레임워크
- **BeautifulSoup4**: HTML 파싱 및 DOM 분석
- **Selenium**: 동적 콘텐츠 크롤링 및 성능 측정
- **Google Generative AI**: Gemini 2.0 Flash 모델 기반 AI 분석
- **Pydantic**: 데이터 검증 및 시리얼라이제이션
- **Uvicorn**: ASGI 서버
- **Scikit-learn**: TF-IDF 기반 콘텐츠 중복 분석
- **URL 콘텐츠 표준화 분석기**: 전용 URL 및 콘텐츠 표준화 분석 모듈

### 프론트엔드

- **React 19**: 최신 React 기반 사용자 인터페이스
- **TypeScript**: 타입 안전성 및 개발자 경험 향상
- **SCSS Modules**: 모듈화된 스타일링
- **React Router**: 클라이언트 사이드 라우팅
- **Axios**: HTTP 클라이언트
- **Lucide React**: 아이콘 라이브러리
- **Recharts**: 데이터 시각화

## 🔧 새로운 기능: URL 및 콘텐츠 표준화 분석

### 📋 분석 항목

#### URL 및 표준화 관리 (9개 항목)

1. **짧은 영문-kebab URL**: URL 길이 ≤115자, 소문자, kebab-case 형식
2. **중복 파라미터 제거**: ID와 콘텐츠 1:1 매핑
3. **Self-canonical 100%**: rel=canonical 본인 지정
4. **Canonical 체인 0건**: A→B 단일 호출 구조
5. **교차 도메인 정합**: cross-domain canonical 적용
6. **HTTP vs HTTPS 혼재**: Canonical HTTPS 우선
7. **매개변수 정책**: GSC URL 파라미터 설정
8. **언어/지역 세그먼트**: /en-us/ 패턴 정규화
9. **Redirect Hop ≤1**: 301 연속 1회로 종료

#### 중복 및 얇은 콘텐츠 (8개 항목)

10. **해시 중복 ≤10%**: SHA-256 유사도 분석
11. **Title 중복 ≤5%**: 문자열 매칭
12. **Meta Description 중복 ≤5%**: 문자열 매칭
13. **Near-duplicate TF-IDF**: Cosine ≤0.9
14. **Thin 단어수 하위 20%**: 전체 ≤15%
15. **Facet 파라미터 noindex**: 규칙 100%
16. **Syndication cross-domain rel=canonical**: 원본 지정
17. **SessionID 제거**: 쿠키 우선 사용

### 🤖 AI 분석 통합

- **기술적 분석**: URL 구조, Canonical 태그, HTTP/HTTPS 혼재 등
- **AI 보완 분석**: 콘텐츠 중복, TF-IDF 유사도, 개선 제안
- **결합 분석**: 기술적 분석 결과에 AI 인사이트 보완

### 📁 파일 구조

```
xe-seo/
├── url_content_analyzer.py     # URL 콘텐츠 표준화 전용 분석기
├── ai_analyzer.py             # AI 분석기 (URL 콘텐츠 분석 기능 추가)
├── seo_analyzer.py            # 메인 SEO 분석기 (통합)
├── test_url_content.py        # URL 콘텐츠 분석기 테스트
└── requirements.txt           # 의존성 (scikit-learn, numpy 추가)
```

## 🚀 빠른 시작 (Docker, 현재 기준)

아래 절차는 로컬에서 DB(PostgreSQL)와 백엔드를 Docker로 실행하고, 프론트엔드를 4000 포트에서 띄우는 현재 프로젝트 기준 가이드입니다.

### 0) 사전 준비

- macOS에서 Docker Desktop 실행 (도커 데몬이 떠 있어야 합니다)
- 루트(`xe-seo/`)에 `.env` 파일 생성 후 필요한 키를 입력

```env
# 필수: PageSpeed Insights API 키
PAGESPEED_API_KEY=YOUR_PSI_API_KEY

# 선택: Gemini API 키 (AI 인사이트/제안에 필요)
GOOGLE_API_KEY=YOUR_GEMINI_API_KEY

# 개발 모드(CORS 완화)
ENVIRONMENT=development

# 선택: 공유 링크에 사용할 프론트 도메인(표시용)
# 예) PUBLIC_BASE_URL=http://localhost:4000
# PUBLIC_BASE_URL=

# 선택: DB 접속 URL (기본값은 docker-compose의 db 컨테이너)
# DATABASE_URL=postgresql+asyncpg://xe_seo:xe_seo@db:5432/xe_seo
```

### 1) DB + 백엔드 실행 (Docker)

```bash
cd xe-seo
docker compose up -d db seo-backend

# 상태 확인
docker compose ps | cat
curl http://localhost:8000/
```

- 기본 포트: 백엔드 8000, DB 5432
- FastAPI 기동 시 테이블은 자동 생성됩니다 (`startup` 훅에서 `Base.metadata.create_all`).

### 2) 프론트엔드 실행

```bash
cd frontend
npm install

# API URL 설정(권장)
echo 'REACT_APP_API_URL=http://localhost:8000' > .env.local

# 4000 포트에서 실행
npm run dev
```

- 접속: http://localhost:4000
- 라우트: `홈(/) → 저장 후 자동 이동(/result/:id)`
  - 저장 실패 시 `/error` 페이지로 이동합니다.
- 결과 페이지 상단 버튼: 결과를 DB에 저장하고 현재 페이지 URL(`/result/:id`)을 클립보드에 복사합니다.

### 3) 동작 점검

- 백엔드 헬스: `curl http://localhost:8000/`
- 테스트 데이터: `http://localhost:8000/analyze/test`, `.../analyze/test/bad`
- 실제 분석: 홈에서 URL 입력 → 진행 바 → 저장 → `/result/:id`

### 4) 자주 하는 질문(요약)

- 왜 `/result` 라우트가 없나요? 현재는 단일 결과 페이지 컴포넌트(`ResultPage`)가 `/result/:id`만 사용합니다.
- 공유는 어떻게 하나요? 결과 페이지 상단 버튼을 눌러 `/result/:id` 링크를 복사하면 됩니다.

### TablePlus 연결

1. TablePlus 실행
2. "Create a new connection" 클릭
3. PostgreSQL 선택
4. 연결 정보 입력:
   Name: XE-SEO Database
   Host: localhost
   Port: 5432
   User: xe_seo
   Password: xe_seo
   Database: xe_seo
5. "Connect" 클릭

## 📝 API 키 발급 방법

### Google Gemini API 키

1. [Google AI Studio](https://aistudio.google.com/) 접속
2. Google 계정으로 로그인
3. "Get API Key" 클릭하여 새 프로젝트 생성
4. API 키 생성 후 `.env` 파일에 추가

## 🔧 사용 방법

1. 백엔드 서버 실행: `python main.py`
2. 프론트엔드 서버 실행: `npm start` (frontend 디렉토리에서)
3. 웹 브라우저에서 `http://localhost:4000` 접속
4. 분석하고 싶은 웹사이트 URL 입력
5. "SEO 분석 시작" 버튼 클릭
6. AI 분석 완료 후 상세한 결과 리포트 확인

## 📊 API 엔드포인트

### GET /

- **설명**: API 상태 확인
- **응답**: 서버 상태 및 버전 정보

### POST /analyze

- **설명**: SEO 분석 수행
- **요청 본문**:
  ```json
  {
    "url": "https://example.com",
    "ai_provider": "gemini"
  }
  ```
- **응답**: SEO 분석 결과 (점수, 카테고리별 분석, AI 제안사항)

## 🏗 프로젝트 구조

```
xe-seo/
├── main.py                    # FastAPI 메인 애플리케이션
├── seo_analyzer.py            # SEO 분석 모듈
├── ai_analyzer.py             # AI 분석 모듈 (Gemini)
├── requirements.txt           # Python 의존성
├── .env                       # 환경 변수 (gitignore됨)
├── Dockerfile                 # Docker 컨테이너 설정
├── docker-compose.yml         # Docker Compose 설정
├── deploy-aws.sh             # AWS 배포 스크립트
├── nginx-seo.conf            # Nginx 설정
└── frontend/                  # React 프론트엔드
    ├── src/
    │   ├── components/        # React 컴포넌트
    │   │   ├── Header/        # 헤더 컴포넌트
    │   │   ├── LoadingSpinner/ # 로딩 스피너
    │   │   ├── SEOReport/     # SEO 리포트 컴포넌트
    │   │   └── URLForm/       # URL 입력 폼
    │   ├── pages/             # 페이지 컴포넌트
    │   │   ├── HomePage/      # 홈 페이지
    │   │   └── ResultPage/    # 결과 페이지
    │   ├── services/          # API 서비스
    │   │   └── api.ts         # API 클라이언트
    │   └── types/             # TypeScript 타입 정의
    │       └── index.ts       # 타입 정의
    ├── package.json           # Node.js 의존성
    └── .env.local             # 프론트엔드 환경 변수
```

## 🚀 배포

### Docker를 사용한 배포

```bash
# Docker 이미지 빌드
docker build -t xe-seo .

# Docker Compose로 실행
docker-compose up -d
```

### AWS EC2 배포

```bash
# AWS 배포 스크립트 실행
chmod +x deploy-aws.sh
./deploy-aws.sh
```

자세한 배포 가이드는 [DEPLOYMENT.md](DEPLOYMENT.md)를 참조하세요.

## 📊 분석 항목

### 기본 SEO 분석

#### 🔴 필수 요소

- 페이지 제목 (30-60자, 존재 여부, 최적화)
- 메타 설명 (120-160자, 존재 여부, 최적화)
- H1 태그 (유일성, 적절성)
- Viewport 설정 (모바일 최적화)
- Charset 설정 (문자 인코딩)
- Meta Robots (크롤링 허용)
- Open Graph (소셜 미디어 최적화)
- Breadcrumb Schema (네비게이션 구조)

#### 🟡 선택적 요소 (해당 시에만 검사)

- **Article Schema**: 뉴스/블로그 사이트용 구조화 데이터
- **Product Schema**: 전자상거래 사이트용 제품 정보
- **FAQPage Schema**: FAQ 섹션이 있는 페이지
- **VideoObject Schema**: 동영상 콘텐츠
- **ImageObject Schema**: 중요 이미지 최적화
- **HowTo Schema**: 단계별 가이드 콘텐츠
- **JobPosting Schema**: 채용 정보 페이지
- **Event Schema**: 이벤트/행사 정보

#### 기타 분석 요소

- 링크 구조 (내부/외부 링크)
- 페이지 성능 (로딩 시간, DOM 로드 시간)
- 기술적 SEO (SSL, Canonical URL)

### AI 분석 (Gemini 2.0 Flash)

- **콘텐츠 품질**: 제목, 메타 설명, 헤딩 구조 분석
- **기술적 SEO**: SSL, 스키마 마크업, 메타 태그 최적화
- **사용자 경험**: 페이지 구조, 네비게이션, 시맨틱 HTML
- **모바일 최적화**: 반응형 디자인, 뷰포트 설정
- **성능**: 로딩 속도, 이미지 최적화

### 점수 체계

- **종합 점수**: 0-100점 (카테고리별 가중평균)
- **카테고리별 점수**: 각 영역별 세부 점수
- **개선 우선순위**: 높음/중간/낮음 3단계
- **예상 효과**: 각 개선사항별 SEO 효과 예측

### 분석 결과 상태

#### 상태 아이콘 시스템

- **✅ 통과 (Pass)**: 기준을 만족하는 항목
- **❌ 실패 (Fail)**: 개선이 필요한 중요 항목
- **⚠️ 경고 (Warning)**: 부분적 개선이 필요한 항목
- **➖ 선택적 건너뜀 (Optional Skip)**: 해당 사이트에 적용되지 않는 선택적 항목
- **❓ 대기 중 (Pending)**: 분석 진행 중인 항목

#### 항목별 분류

- **필수 항목**: 모든 웹사이트에 적용되는 기본 SEO 요소
- **선택적 항목**: 사이트 유형에 따라 선택적으로 적용되는 구조화 데이터

## 🔧 문제 해결

### 일반적인 문제

1. **Chrome 드라이버 오류**

   ```bash
   # Chrome 설치 스크립트 재실행
   ./install-chrome.sh
   ```

2. **포트 충돌**

   ```bash
   # 포트 사용 중인 프로세스 확인
   lsof -i :8000  # 백엔드
   lsof -i :4000  # 프론트엔드
   ```

3. **API 키 오류**

   ```bash
   # 환경 변수 확인
   echo $GOOGLE_API_KEY
   ```

4. **CORS 오류**
   - 백엔드 CORS 설정 확인
   - 프론트엔드 API URL 확인

### 로그 확인

```bash
# 백엔드 로그
python main.py  # 개발 모드

# 프론트엔드 로그
npm start  # 개발 모드
```

## 🤝 기여하기

1. 이 저장소를 포크합니다
2. 기능 브랜치를 생성합니다 (`git checkout -b feature/AmazingFeature`)
3. 변경 사항을 커밋합니다 (`git commit -m 'Add some AmazingFeature'`)
4. 브랜치에 푸시합니다 (`git push origin feature/AmazingFeature`)
5. Pull Request를 생성합니다

## 📄 라이선스

이 프로젝트는 MIT 라이선스 하에 배포됩니다.

## 🧪 E2E 테스트 (Playwright)

### 테스트 실행

```bash
# Playwright 브라우저 설치
npm run test:e2e:install

# 기본 SEO 분석 테스트
npm run test:e2e:basic

# 모든 E2E 테스트 실행
npm run test:e2e

# 브라우저 화면을 보면서 테스트 (헤드리스 모드 해제)
npm run test:e2e:headed

# 스트레스 테스트 (동시 요청)
npm run test:e2e:stress:concurrent

# 테스트 결과 리포트 보기
npm run test:e2e:report
```

### 프로세스 관리

Playwright 테스트 후 좀비 프로세스가 남지 않도록 자동 정리 기능이 포함되어 있습니다:

```bash
# 현재 실행 중인 Playwright 관련 프로세스 확인
npm run test:e2e:check-processes

# 응급상황용 수동 프로세스 정리 (자동 정리 실패 시에만 사용)
npm run test:e2e:emergency-cleanup
```

### 테스트 기능

- **기본 SEO 분석 테스트**: 실제 웹사이트 SEO 분석 기능 검증
- **에러 처리 테스트**: 잘못된 URL 입력 시 처리 검증
- **스트레스 테스트**: 동시 요청 처리 능력 검증 (3~20개 동시 요청)
- **자동 스크린샷**: 테스트 결과를 이미지로 자동 캡처
- **완전 자동 정리**: 백엔드 자동 정리 시스템으로 수동 개입 불필요

## 🔄 자동 프로세스 정리 시스템

### 백엔드 자동 정리

- **SEO 분석 완료 후**: Chrome/Selenium 프로세스 자동 정리
- **에러 발생 시**: 예외 상황에서도 리소스 자동 해제
- **임시 파일 정리**: Chrome 프로필 및 임시 디렉토리 정리

### 프론트엔드 자동 정리

- **분석 성공 시**: 백엔드 정리 API 자동 호출
- **분석 실패 시**: 에러 발생 시에도 정리 프로세스 실행
- **백그라운드 실행**: 사용자 경험에 영향 없이 정리

### 응급상황용 정리 API

```bash
# 응급상황용 프로세스 정리 (자동 정리 실패 시에만 사용)
curl -X POST http://localhost:8000/emergency-cleanup
```

## ⚠️ 주의사항

- **Chrome 브라우저와 ChromeDriver**: 웹 크롤링을 위해 필수 설치
- **Google Gemini API 키**: AI 분석을 위해 필수 설정
- **크롤링 제한**: 일부 웹사이트는 크롤링을 제한할 수 있습니다
- **분석 시간**: 웹사이트 복잡도에 따라 1-3분 소요
- **API 사용량**: Google Gemini API 사용량에 따른 과금 가능
- **프로세스 관리**: 분석 완료 후 자동으로 정리되므로 좀비 프로세스 걱정 없음

## 📞 지원

문제가 발생하면 다음을 확인하세요:

1. [Issues](https://github.com/your-username/xe-seo/issues)에서 유사한 문제 검색
2. 환경 변수 설정 확인
3. 의존성 설치 상태 확인
4. Chrome 및 ChromeDriver 설치 확인
