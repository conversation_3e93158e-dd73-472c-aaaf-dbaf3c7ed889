import axios from "axios";
import { SEOAnalysisResult, AnalysisRequest } from "../types";
import API_CONFIG from "../config/api";

const api = axios.create({
  baseURL: API_CONFIG.BASE_URL,
  timeout: 120000, // 2분 타임아웃 (SEO 분석은 시간이 걸릴 수 있음)
  headers: API_CONFIG.HEADERS,
});

export const analyzeSEO = async (
  request: AnalysisRequest
): Promise<SEOAnalysisResult> => {
  try {
    const response = await api.post<SEOAnalysisResult>(
      API_CONFIG.ENDPOINTS.ANALYZE,
      request
    );
    return response.data;
  } catch (error: any) {
    if (error.response) {
      const message = error.response?.data?.detail || error.message;
      throw new Error(`SEO 분석 실패: ${message}`);
    }
    throw error;
  }
};

export const healthCheck = async (): Promise<boolean> => {
  try {
    const response = await api.get(API_CONFIG.ENDPOINTS.HEALTH);
    return response.status === 200;
  } catch (error) {
    return false;
  }
};
