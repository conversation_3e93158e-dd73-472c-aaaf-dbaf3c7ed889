#!/bin/bash

# SSL 인증서 갱신 스크립트 (Standalone 모드)
# 사용법: ./renew-ssl.sh

# 로그 파일 설정
LOG_FILE="/var/log/ssl-renewal.log"
DOMAIN="seoapi.pxd.co.kr"

# 로그 함수
log() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1" | sudo tee -a $LOG_FILE
}

log "=== SSL 인증서 갱신을 시작합니다 ==="

# 1. 현재 인증서 만료일 확인
log "1. 현재 인증서 상태 확인..."
CERT_FILE="/etc/letsencrypt/live/$DOMAIN/cert.pem"

if [ -f "$CERT_FILE" ]; then
    EXPIRY_DATE=$(sudo openssl x509 -enddate -noout -in "$CERT_FILE" | cut -d= -f2)
    log "현재 인증서 만료일: $EXPIRY_DATE"

    # 만료까지 30일 이상 남았는지 확인
    EXPIRY_TIMESTAMP=$(date -d "$EXPIRY_DATE" +%s)
    CURRENT_TIMESTAMP=$(date +%s)
    DAYS_LEFT=$(( ($EXPIRY_TIMESTAMP - $CURRENT_TIMESTAMP) / 86400 ))

    log "인증서 만료까지 $DAYS_LEFT 일 남음"

    if [ $DAYS_LEFT -gt 30 ]; then
        log "인증서 갱신이 필요하지 않습니다. (30일 이상 남음)"
        exit 0
    fi
else
    log "인증서 파일을 찾을 수 없습니다: $CERT_FILE"
    exit 1
fi

# 2. Docker Compose 서비스 중단 (포트 80, 443 해제)
log "2. Docker 서비스 중단 중..."
cd /home/<USER>/xe-seo  # 실제 프로젝트 경로로 변경하세요
docker-compose down

if [ $? -eq 0 ]; then
    log "✅ Docker 서비스가 성공적으로 중단되었습니다."
else
    log "❌ Docker 서비스 중단에 실패했습니다."
    exit 1
fi

# 3. SSL 인증서 갱신 (standalone 모드)
log "3. SSL 인증서 갱신 실행..."
sudo certbot renew --standalone --preferred-challenges http

RENEWAL_RESULT=$?

# 4. Docker Compose 서비스 재시작
log "4. Docker 서비스 재시작 중..."
docker-compose up -d

if [ $? -eq 0 ]; then
    log "✅ Docker 서비스가 성공적으로 재시작되었습니다."
else
    log "❌ Docker 서비스 재시작에 실패했습니다."
    exit 1
fi

# 5. 갱신 결과 확인
if [ $RENEWAL_RESULT -eq 0 ]; then
    log "✅ SSL 인증서 갱신이 완료되었습니다!"

    # 갱신된 인증서 정보 확인
    NEW_EXPIRY_DATE=$(sudo openssl x509 -enddate -noout -in "$CERT_FILE" | cut -d= -f2)
    log "새 인증서 만료일: $NEW_EXPIRY_DATE"

    # 서비스 상태 확인
    sleep 10
    if curl -s -o /dev/null -w "%{http_code}" https://$DOMAIN | grep -q "200\|301\|302"; then
        log "✅ HTTPS 서비스가 정상적으로 작동하고 있습니다."
    else
        log "⚠️  HTTPS 서비스 상태를 확인해주세요."
    fi

else
    log "❌ SSL 인증서 갱신에 실패했습니다."
    log "로그를 확인하세요: /var/log/letsencrypt/letsencrypt.log"
    exit 1
fi

log "=== SSL 인증서 갱신 작업이 완료되었습니다 ==="
