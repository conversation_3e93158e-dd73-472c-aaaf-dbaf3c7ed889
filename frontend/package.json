{"name": "frontend", "version": "0.1.0", "private": true, "dependencies": {"@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^13.5.0", "@types/axios": "^0.9.36", "@types/jest": "^27.5.2", "@types/node": "^16.18.126", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@types/react-router-dom": "^5.3.3", "axios": "^1.10.0", "classnames": "^2.5.1", "lucide-react": "^0.516.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-router-dom": "^7.6.2", "react-scripts": "5.0.1", "recharts": "^2.15.3", "sass": "^1.89.2", "typescript": "^4.9.5", "web-vitals": "^2.1.4"}, "scripts": {"dev": "PORT=4000 react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"http-proxy-middleware": "^3.0.5"}}