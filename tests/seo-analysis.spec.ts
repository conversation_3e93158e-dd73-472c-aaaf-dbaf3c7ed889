import { test, expect } from "@playwright/test";

test.describe("SEO 분석 E2E 테스트", () => {
  test("기본 SEO 분석 테스트", async ({ page }) => {
    // 1. 홈페이지 접속
    await page.goto("http://localhost:4000");

    // 2. URL 입력 및 분석 요청
    const urlInput = page.locator('[data-testid="url-input"]');
    await urlInput.fill("pxd.co.kr"); // Changed URL

    const analyzeButton = page.locator('[data-testid="analyze-button"]');
    await analyzeButton.click();

    // 3. 로딩 상태 확인
    const loadingSpinner = page.locator('[data-testid="loading-spinner"]');
    await expect(loadingSpinner).toBeVisible();

    // 4. 분석 완료 대기 (로딩 스피너가 사라질 때까지)
    await expect(loadingSpinner).toBeHidden({ timeout: 120000 }); // 2분 타임아웃

    // 5. 결과 페이지 확인
    await expect(page).toHaveURL(/\/result/);

    // 페이지가 완전히 로드될 때까지 대기
    await page.waitForLoadState("networkidle");

    // 페이지를 맨 위로 스크롤
    await page.evaluate(() => window.scrollTo(0, 0));

    // 스크롤바 숨기기 (CSS 주입)
    await page.addStyleTag({
      content: `
        ::-webkit-scrollbar {
          display: none !important;
        }
        * {
          scrollbar-width: none !important;
          -ms-overflow-style: none !important;
        }
      `,
    });

    // 6. 결과 화면 스크린샷 저장 (전체 페이지) - 레포트에 연결
    // 파일로 먼저 저장
    await page.screenshot({
      path: "test-results/analysis-result-fullpage.png",
      fullPage: true, // 스크롤 가능한 모든 영역 포함
    });

    // 스크린샷을 테스트 결과에 첨부 (파일로 저장 후 연결)
    await test.info().attach("analysis-result-fullpage", {
      path: "test-results/analysis-result-fullpage.png",
      contentType: "image/png",
    });

    // 7. 테스트 완료
    console.log("기본 SEO 분석 테스트 완료!");
  });

  test("잘못된 URL 입력 테스트", async ({ page }) => {
    await page.goto("http://localhost:4000");

    const urlInput = page.locator('[data-testid="url-input"]');
    await urlInput.fill("invalid-url");

    const analyzeButton = page.locator('[data-testid="analyze-button"]');
    await analyzeButton.click();

    // 에러 상태 확인 (로딩 스피너가 나타나지 않거나 에러 메시지가 나타남)
    const loadingSpinner = page.locator('[data-testid="loading-spinner"]');
    await expect(loadingSpinner).toBeHidden({ timeout: 10000 });

    // 에러 화면 스크린샷 저장 (전체 페이지) - 레포트에 연결
    // 파일로 먼저 저장
    await page.screenshot({
      path: "test-results/error-result-fullpage.png",
      fullPage: true, // 스크롤 가능한 모든 영역 포함
    });

    // 스크린샷을 테스트 결과에 첨부 (파일로 저장 후 연결)
    await test.info().attach("error-result-fullpage", {
      path: "test-results/error-result-fullpage.png",
      contentType: "image/png",
    });

    // 테스트 완료
    console.log("잘못된 URL 입력 테스트 완료!");
  });

  test("스트레스 테스트 - 동시 요청", async ({ browser }) => {
    // 환경 변수나 커맨드 라인 인자로 동시 요청 개수 설정
    const concurrentCount = parseInt(process.env.CONCURRENT_COUNT || "3");
    console.log(`동시 요청 테스트 시작: ${concurrentCount}개 동시 요청`);

    // 여러 브라우저 컨텍스트를 동시에 생성
    const contexts: any[] = [];
    const pages: any[] = [];

    try {
      // 설정된 개수만큼 동시 요청 생성
      for (let i = 0; i < concurrentCount; i++) {
        const context = await browser.newContext();
        const page = await context.newPage();
        contexts.push(context);
        pages.push(page);
      }

      // 모든 페이지에서 동시에 분석 시작
      const promises = pages.map(async (page, index) => {
        console.log(`동시 요청 ${index + 1}/${concurrentCount} 시작`);

        await page.goto("http://localhost:4000");
        const urlInput = page.locator('[data-testid="url-input"]');
        await urlInput.fill("pxd.co.kr"); // Changed URL

        const analyzeButton = page.locator('[data-testid="analyze-button"]');
        await analyzeButton.click();

        const loadingSpinner = page.locator('[data-testid="loading-spinner"]');
        await expect(loadingSpinner).toBeVisible();

        return { page, index };
      });

      // 모든 요청이 시작될 때까지 대기
      await Promise.all(promises);

      // 모든 분석이 완료될 때까지 대기
      const completionPromises = pages.map(async (page, index) => {
        const loadingSpinner = page.locator('[data-testid="loading-spinner"]');
        await expect(loadingSpinner).toBeHidden({ timeout: 90000 }); // 1.5분 타임아웃
        await expect(page).toHaveURL(/\/result/);

        // 페이지가 완전히 로드될 때까지 대기
        await page.waitForLoadState("networkidle");

        // 페이지를 맨 위로 스크롤
        await page.evaluate(() => window.scrollTo(0, 0));

        // 스크롤바 숨기기 (CSS 주입)
        await page.addStyleTag({
          content: `
            ::-webkit-scrollbar {
              display: none !important;
            }
            * {
              scrollbar-width: none !important;
              -ms-overflow-style: none !important;
            }
          `,
        });

        // 각 페이지의 결과 스크린샷 저장 (전체 페이지) - 레포트에 연결
        // 파일로 먼저 저장
        await page.screenshot({
          path: `test-results/stress-result-${index + 1}-fullpage.png`,
          fullPage: true, // 스크롤 가능한 모든 영역 포함
        });

        // 스크린샷을 테스트 결과에 첨부 (파일로 저장 후 연결)
        await test.info().attach(`stress-result-${index + 1}-fullpage`, {
          path: `test-results/stress-result-${index + 1}-fullpage.png`,
          contentType: "image/png",
        });

        console.log(`동시 요청 ${index + 1}/${concurrentCount} 완료`);
      });

      await Promise.all(completionPromises);
      console.log(`모든 동시 요청 완료! (총 ${concurrentCount}개)`);

      // 테스트 완료
      console.log("스트레스 테스트 완료!");
    } catch (error) {
      console.error("스트레스 테스트 중 오류 발생:", error);
    } finally {
      // 모든 컨텍스트와 페이지 정리
      for (const context of contexts) {
        try {
          await context.close();
        } catch (error) {
          console.log("컨텍스트 정리 중 오류 (무시됨):", error.message);
        }
      }
    }
  });
});
