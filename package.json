{"name": "xe-seo-e2e-tests", "version": "1.0.0", "description": "XE SEO 프로젝트 E2E 테스트 스위트", "scripts": {"test:e2e": "playwright test", "test:e2e:headed": "playwright test --headed", "test:e2e:basic": "playwright test --grep='기본 SEO 분석'", "test:e2e:error": "playwright test --grep='잘못된 URL'", "test:e2e:stress": "playwright test --grep='스트레스'", "test:e2e:stress:concurrent": "playwright test --grep='동시 요청'", "test:e2e:stress:concurrent:5": "CONCURRENT_COUNT=5 playwright test --grep='동시 요청'", "test:e2e:stress:concurrent:10": "CONCURRENT_COUNT=10 playwright test --grep='동시 요청'", "test:e2e:stress:concurrent:20": "CONCURRENT_COUNT=20 playwright test --grep='동시 요청'", "test:e2e:with-report": "playwright test && (playwright show-report --port 9324 & sleep 10 && pkill -f 'playwright show-report')", "test:e2e:basic:with-report": "playwright test --grep='기본 SEO 분석' && (playwright show-report --port 9324 & sleep 10 && pkill -f 'playwright show-report')", "test:e2e:install": "playwright install", "test:e2e:clean": "rm -rf test-results/ playwright-report/", "test:e2e:report": "playwright show-report --port 9324", "test:e2e:emergency-cleanup": "pkill -f 'chromium|chrome|playwright' || true && lsof -ti:4000 | xargs kill -9 || true && npm run test:e2e:clean", "test:e2e:check-processes": "./scripts/check-processes.sh"}, "devDependencies": {"@playwright/test": "^1.40.0", "@types/node": "^20.0.0", "typescript": "^5.0.0"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "keywords": ["playwright", "e2e", "testing", "seo", "automation"], "author": "XE SEO Team", "license": "MIT"}