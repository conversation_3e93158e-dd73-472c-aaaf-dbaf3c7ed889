import os
from typing import AsyncGenerator

from sqlalchemy.ext.asyncio import AsyncEngine, create_async_engine, AsyncSession
from sqlalchemy.orm import sessionmaker, declarative_base


# 환경별 데이터베이스 URL 설정
def get_database_url() -> str:
    """환경에 따른 데이터베이스 URL 반환"""
    env_db_url = os.getenv("DATABASE_URL")
    if env_db_url:
        return env_db_url
    
    environment = os.getenv("ENVIRONMENT", "development")
    
    if environment == "production":
        # 운영환경: AWS RDS 사용 (환경변수 필수)
        raise ValueError("운영환경에서는 DATABASE_URL 환경변수가 필요합니다")
    else:
        # 개발환경: 로컬 PostgreSQL 기본값
        return "postgresql+asyncpg://xe_seo:xe_seo@localhost:5432/xe_seo"


DATABASE_URL = get_database_url()


Base = declarative_base()


def get_engine() -> AsyncEngine:
    echo = os.getenv("SQLALCHEMY_ECHO", "false").lower() == "true"
    return create_async_engine(DATABASE_URL, future=True, echo=echo, pool_pre_ping=True)


async_engine: AsyncEngine = get_engine()


async_session_factory = sessionmaker(
    bind=async_engine,
    class_=AsyncSession,
    expire_on_commit=False,
    autoflush=False,
    autocommit=False,
)


async def get_session() -> AsyncGenerator[AsyncSession, None]:
    async with async_session_factory() as session:
        yield session


