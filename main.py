from fastapi import FastAP<PERSON>, HTTPException, Request, Depends
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import Response
from pydantic import BaseModel, HttpUrl, Field
import uvicorn
from typing import Optional, Dict, List, Any
import os
from dotenv import load_dotenv
import json

from seo_analyzer import SEOAnalyzer
from ai_analyzer import AIAnalyzer
from sqlalchemy import select
from sqlalchemy.exc import NoResultFound
from sqlalchemy.ext.asyncio import AsyncSession
from db.engine import async_engine, Base, get_session
from db.models import SEOAnalysis, User
from api.schemas import SaveAnalysisRequest, SaveAnalysisResponse, LoadAnalysisResponse, ResultsResponse, AnalysisListItem
import subprocess
import asyncio
import datetime

load_dotenv()

# ✅ PageSpeed API 키를 환경변수에서 로드
PAGESPEED_API_KEY = os.getenv("PAGESPEED_API_KEY")

# 프로세스 정리 함수
async def cleanup_analysis_processes():
    """SEO 분석 완료 후 관련 프로세스들을 정리합니다."""
    try:
        print("🧹 SEO 분석 완료 - 프로세스 정리 시작...")
        
        # 1. Chrome/Chromium 프로세스 정리
        await asyncio.to_thread(
            subprocess.run,
            ['pkill', '-f', 'chrome|chromium|chromedriver'],
            capture_output=True,
            text=True
        )
        
        # 2. Selenium 관련 프로세스 정리
        await asyncio.to_thread(
            subprocess.run,
            ['pkill', '-f', 'selenium|webdriver'],
            capture_output=True,
            text=True
        )
        
        # 3. 임시 파일 정리 (Chrome 프로필 등)
        await asyncio.to_thread(
            subprocess.run,
            ['find', '/tmp', '-name', '*chrome*', '-type', 'd', '-mtime', '+1', '-exec', 'rm', '-rf', '{}', '+'],
            capture_output=True,
            text=True
        )
        
        print("✅ 프로세스 정리 완료")
        
    except Exception as e:
        print(f"⚠️ 프로세스 정리 중 오류 (무시됨): {e}")

app = FastAPI(
    title="SEO 분석 도구",
    description="웹사이트 SEO 분석 및 AI 기반 개선 제안",
    version="1.0.0"
)

# 환경에 따른 CORS 설정
is_development = os.getenv("ENVIRONMENT", "development") != "production"
production_domain = os.getenv("PRODUCTION_DOMAIN", "seoapi.pxd.co.kr")

# CORS 설정: 개발 모드에서는 모든 오리진 허용 (프록시/내부망 포함)
if is_development:
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],
        allow_credentials=False,
        allow_methods=["*"],
        allow_headers=["*"],
        expose_headers=["*"]
    )
else:
    origins = [
        "https://xe-seo.vercel.app",
        f"https://{production_domain}",
        "https://*.vercel.app",
    ]
    app.add_middleware(
        CORSMiddleware,
        allow_origins=origins,
        allow_credentials=True,
        allow_methods=["GET", "POST", "OPTIONS"],
        allow_headers=["*"],
        expose_headers=["*"]
    )



class URLRequest(BaseModel):
    url: HttpUrl
    ai_provider: Optional[str] = "gemini"  # 현재는 "gemini"만 지원

class CheckInfo(BaseModel):
    id: int
    title: str
    importance: int
    criteria: str
    status: str
    message: str
    score: int

class SectionResult(BaseModel):
    section_id: str
    section_name: str
    checks: List[CheckInfo]
    total_checks: int
    passed_checks: int
    section_score: int
    ai_suggestions: Optional[List[Dict[str, Any]]] = None # AI 제안을 담을 필드 추가

class CategoryResult(BaseModel):
    category_id: str
    category_name: str
    sections: List[SectionResult]
    category_score: int

class Recommendation(BaseModel):
    priority: str = Field(..., description="추천의 우선순위 (예: 높음, 중간, 낮음)")
    title: str = Field(..., description="개선 항목의 제목")
    description: str = Field(..., description="개선 항목에 대한 상세 설명")
    category: str = Field(..., description="관련 SEO 카테고리 (예: 메타 태그, 성능)")
    expected_impact: str = Field(..., description="예상되는 SEO 점수 영향 또는 효과")


class AIInsights(BaseModel):
    overall_summary: str
    seo_level: str
    strengths: List[str]
    priority_recommendations: List[Recommendation]
    technical_insights: List[str]
    content_insights: List[str]
    expected_score_improvement: str
    implementation_timeline: str

class SEOResult(BaseModel):
    url: str
    checklist_version: str
    categories: List[CategoryResult]
    overall_score: int
    category_scores: Dict[str, int]
    total_checks: int
    passed_checks: int
    failed_checks: int
    ai_analysis: AIInsights
    recommendations: List[Recommendation] = Field(description="상위 추천 목록")
    critical_issues: List[str]

 

def _generate_expected_impact(title: str = "", section_id: str = "", importance: int = 3, message: str = "") -> str:
    t = (title or "").lower()
    m = (message or "").lower()
    # 메타 정보/콘텐츠
    if ("title" in t and ("30-60" in t or "30–60" in t or "길이" in t)) or ("title tag" in t):
        return "제목이 검색 결과에 온전히 노출되어 CTR 상승(예상 +20~30%) 및 주요 키워드 일치도 강화"
    if "meta description" in t or "메타 설명" in t or "description" in t:
        return "요약 품질 향상으로 SERP 클릭률 증가(예상 +15~25%) 및 사용자 기대치 정합성 개선"
    if "h1" in t:
        return "페이지 주제 명확화로 색인·랭킹 신호 강화, 내부 헤딩 구조 개선으로 본문 스캔 효율 향상"
    if "heading" in t or "헤딩" in t:
        return "계층적 헤딩 구조 확립으로 정보 탐색성 개선 및 키워드 맥락 강화"
    # 모바일/표시
    if "viewport" in t:
        return "모바일 뷰포트 최적화로 가독성·터치 타깃 품질 향상, 이탈률 감소(예상 -10~20%)"
    if "charset" in t or "문자 인코딩" in t:
        return "문자 깨짐·색인 오류 방지, 다국어 페이지의 검색 노출 안정성 확보"
    # 소셜/내비게이션
    if "og:" in t or "open graph" in t or "twitter:" in t:
        return "소셜 미리보기 품질 개선으로 공유 시 클릭률 상승(예상 +20~40%) 및 브랜드 일관성 강화"
    if "breadcrumb" in t:
        return "Breadcrumb 리치 리절트 노출 가능성 증가 및 탐색 경로 명확화로 UX 향상"
    # 크롤링/색인
    if ("robots" in t and "meta" in t) or "meta robots" in t:
        return "색인 정책 정상화로 색인 누락/중복 방지, 크롤 효율 개선"
    if "robots.txt" in t:
        return "크롤 예산 낭비 축소 및 중요 경로 접근 보장으로 신규 페이지 발견 속도 향상"
    if "sitemap" in t:
        return "사이트맵 신호 강화로 신규/변경 페이지의 색인 속도 개선 및 커버리지 안정화"
    if "canonical" in t or "정규화" in t:
        return "중복 URL 통합로 랭킹 신호 집중, 불필요한 경쟁 해소"
    if "hreflang" in t:
        return "지역/언어 타게팅 정확도 향상으로 잘못된 지역 노출 감소 및 현지 SERP 성과 개선"
    # 이미지/미디어
    if ("alt" in t and ("image" in t or "이미지" in t)) or "대체 텍스트" in t:
        return "이미지 접근성 개선 및 이미지 검색 유입 증가(예상 +5~15%)"
    if ("image" in t or "이미지" in t) and ("size" in t or "용량" in t or "압축" in t or "포맷" in t or "webp" in t):
        return "이미지 전송량 절감으로 LCP 단축, 초기 이탈률 감소(예상 -5~15%)"
    if "lazy" in t and ("image" in m or "이미지" in m):
        return "폴드 이하 이미지 지연 로딩으로 초기 렌더링 가벼워져 LCP 개선"
    # 성능/웹 바이탈
    if "lcp" in t or "largest contentful paint" in t:
        return "주요 콘텐츠 표시 시간 단축(LCP ≤ 2.5s 목표)으로 체감 속도 개선 및 이탈률 감소"
    if "inp" in t or "fid" in t or "interaction" in t:
        return "상호작용 지연(INP ≤ 200ms 목표) 감소로 UX 및 전환율 개선"
    if "cls" in t or "layout shift" in t:
        return "예상치 못한 레이아웃 이동 감소(CLS ≤ 0.1 목표)로 신뢰도·집중도 향상"
    if "ttfb" in t:
        return "서버 응답 지연 단축으로 전체 로딩 체감 속도 개선 및 크롤 효율 증가"
    if "preload" in t or "prefetch" in t or "defer" in t:
        return "리소스 로딩 우선순위 최적화로 렌더링 경로 단축 및 초기 가시성 개선"
    if "gzip" in t or "brotli" in t or "압축" in t:
        return "전송량 감소로 네트워크 지연 완화, LCP/TTFB 동시 개선"
    if "cache" in t or "캐시" in t:
        return "재방문 로딩 시간 단축으로 페이지 뷰·세션당 페이지수 상승"
    # 보안/오류
    # 보안/프로토콜/헤더
    if "https" in t or "mixed content" in t or "보안" in t:
        return "HTTPS 일관화로 브라우저 경고 제거 및 신뢰도 향상, 검색 신호 안정화"
    if "hsts" in t or "strict-transport-security" in t or "max-age" in t or "hsts" in m:
        return "HSTS 적용으로 다운그레이드/중간자 공격 방지, 첫 방문 이후 강제 HTTPS로 보안 경고 제거 및 크롤러 신뢰 신호 강화"
    if "tls" in t or "ssl" in t or "1.2" in t or "1.3" in t:
        return "TLS ≥1.2 적용으로 취약 프로토콜 차단, 호환성·성능·보안 균형 개선 및 사용자/크롤러 신뢰도 향상"
    if "content-security-policy" in t or "csp" in t or "content security policy" in t:
        return "CSP로 스크립트/리소스 출처를 제한하여 XSS·인젝션 위험 감소, 보안 사고 예방으로 색인 안정성 및 UX 향상"
    if "x-content-type-options" in t:
        return "MIME sniffing 방지로 잘못된 콘텐츠 해석 차단, 스니핑 기반 공격 억제"
    if "x-frame-options" in t or "frame-ancestors" in t:
        return "클릭재킹 방지로 페이지 임베드 오남용 차단, 사용자 신뢰도 향상"
    if "referrer-policy" in t:
        return "민감 Referrer 데이터 누출 최소화로 개인정보 보호·보안 준수 강화"
    if "permissions-policy" in t or "feature-policy" in t:
        return "센서/권한 사용을 최소한으로 제한해 개인정보 보호 및 공격면 축소"
    if "404" in t or "5xx" in t or "오류" in t:
        return "링크 무결성 회복으로 크롤 오류 감소, 색인 안정화 및 사용자 신뢰 회복"
    if "redirect" in t or "리다이렉트" in t:
        return "리다이렉트 체인 제거로 지연·크롤 낭비 감소, 링크 equity 손실 최소화"
    # 구조화 데이터(리치 리절트)
    if "invalid field" in t or "structured data" in t or "schema" in t:
        return "구조화 데이터 정합성 회복으로 리치 리절트 노출 가능성 증가"
    if "breadcrumblist" in t or "breadcrumb" in t:
        return "Breadcrumb 리치 리절트 노출 및 사이트 구조 가시성 향상"
    if "article" in t:
        return "기사형 콘텐츠의 리치 리절트 노출 개선(제목/작성자/발행일)로 클릭 유입 증대"
    if "product" in t:
        return "상품 정보(가격/재고 등) 노출로 상업적 의도 쿼리 전환 가능성 상승"
    if "faqpage" in t or "faq" in t:
        return "FAQ 리치 리절트로 SERP 내 정보 제공 강화, 클릭 없이도 브랜드 신뢰 제고"
    if "videoobject" in t or "video" in t:
        return "동영상 리치 리절트 노출 가능성 향상으로 미디어 유입 증대"
    if "imageobject" in t:
        return "이미지 메타/HTTPS 정비로 이미지 검색 노출 및 품질 신호 개선"
    if "howto" in t:
        return "절차형 가이드의 리치 리절트 노출 가능성 향상으로 튜토리얼 유입 증가"
    if "jobposting" in t:
        return "채용 리치 리절트 노출로 구직자 트래픽 품질 및 매칭 효율 증가"
    if "event" in t:
        return "이벤트 리치 리절트 노출로 일정 가시성 및 참여율 향상"
    # 기본값
    return f"'{title}' 개선으로 관련 지표(CTR/이탈률/색인/코어 웹 바이탈) 전반의 체감 성과 향상"


@app.get("/")
async def read_root():
    """API 상태 확인"""
    return {"message": "SEO 분석 API 서버가 실행 중입니다!", "version": "1.0.0"}


@app.on_event("startup")
async def on_startup() -> None:
    # Create tables if they do not exist
    async with async_engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)

@app.get("/api-status")
async def get_api_status():
    """API 키 상태를 확인하는 엔드포인트"""
    try:
        # AIAnalyzer 인스턴스 생성하여 상태 확인
        ai_analyzer = AIAnalyzer()
        api_status = ai_analyzer.get_api_key_status()
        
        # 민감한 정보 제거
        safe_status = {
            'current_key_index': api_status['current_key_index'],
            'total_keys': api_status['total_keys'],
            'usage_stats': {}
        }
        
        # API 키는 마스킹하여 표시
        for i, (key, stats) in enumerate(api_status['usage_stats'].items()):
            masked_key = f"***{key[-4:]}" if len(key) > 4 else "***"
            safe_status['usage_stats'][masked_key] = {
                'success': stats['success'],
                'failure': stats['failure'],
                'last_used': stats['last_used']
            }
        
        return {
            "status": "success",
            "api_key_status": safe_status,
            "timestamp": "2023-12-21T10:30:45.123456"
        }
    except Exception as e:
        return {
            "status": "error",
            "message": str(e),
            "timestamp": "2023-12-21T10:30:45.123456"
        }

@app.post("/emergency-cleanup")
async def emergency_cleanup_endpoint():
    """응급상황용 프로세스 정리 엔드포인트 (일반적으로는 자동 정리가 작동함)"""
    try:
        await cleanup_analysis_processes()
        return {
            "status": "success",
            "message": "응급 프로세스 정리가 완료되었습니다. (일반적으로는 자동 정리가 작동합니다)",
            "timestamp": datetime.datetime.now().isoformat()
        }
    except Exception as e:
        return {
            "status": "error",
            "message": f"응급 프로세스 정리 중 오류 발생: {str(e)}",
            "timestamp": datetime.datetime.now().isoformat()
        }



@app.post("/analyze", response_model=SEOResult)
async def analyze_seo(request: URLRequest, response: Response, session: AsyncSession = Depends(get_session)):
    """SEO 분석 수행"""
    # ✅ PageSpeed API 키가 없으면 오류 발생
    if not PAGESPEED_API_KEY:
        raise HTTPException(status_code=500, detail="PageSpeed Insights API 키가 설정되지 않았습니다.")

    try:
        checklist_path = os.path.join(os.path.dirname(__file__), "seo-checklist.json")
        with open(checklist_path, "r", encoding="utf-8") as f:
            checklist_data = json.load(f)

        # AI 분석기 초기화
        ai_analyzer = AIAnalyzer()
        
        # (삭제됨) E-E-A-T 관련 분석은 더 이상 수행하지 않습니다.

        # AI 콘텐츠 품질 분석 수행
        content_quality_result = None
        temp_analyzer = None
        try:
            print(f"🔍 AI 콘텐츠 품질 분석 시작: {request.url}")
            # SEOAnalyzer 생성 전에 콘텐츠 데이터 수집
            temp_analyzer = await SEOAnalyzer.create(str(request.url), checklist_data)
            soup = getattr(temp_analyzer, 'soup', None)
            def safe_find_title():
                if soup:
                    t = soup.find('title')
                    return t.get_text(strip=True) if t else ""
                return ""
            def safe_find_meta_desc():
                if soup:
                    tag = soup.find('meta', attrs={'name': 'description'})
                    return tag.get('content', '') if tag else ""
                return ""
            website_data = {
                "title": safe_find_title(),
                "meta_description": safe_find_meta_desc(),
                "content": soup.get_text()[:5000] if soup else "",  # 첫 5000자만
                "headings": ([{"level": int(h.name[1]), "text": h.get_text(strip=True)} for h in soup.find_all(['h1', 'h2', 'h3', 'h4', 'h5', 'h6'])] if soup else []),
                "images": ([{"src": img.get('src', ''), "alt": img.get('alt', ''), "title": img.get('title', '')} for img in soup.find_all('img')] if soup else []),
                "links": ([{"href": a.get('href', ''), "text": a.get_text(strip=True)} for a in soup.find_all('a')] if soup else []),
                "structured_data": getattr(temp_analyzer, 'structured_data', [])
            }
            
            content_quality_result = await ai_analyzer.analyze_content_quality(str(request.url), website_data)
            print(f"✅ AI 콘텐츠 품질 분석 완료")
            
        except Exception as e:
            print(f"❌ AI 콘텐츠 품질 분석 중 오류: {e}")
            import traceback
            traceback.print_exc()
            content_quality_result = {
                "error": f"AI 콘텐츠 품질 분석 중 오류 발생: {str(e)}"
            }
        finally:
            # temp_analyzer 리소스 정리 (중요!)
            if temp_analyzer:
                try:
                    temp_analyzer.cleanup_resources()
                    print("🧹 temp_analyzer 정리 완료")
                except Exception as cleanup_error:
                    print(f"⚠️ temp_analyzer 정리 중 오류 (무시됨): {cleanup_error}")

        # ✅ SEOAnalyzer를 새로운 비동기 팩토리 메소드로 생성
        seo_analyzer = await SEOAnalyzer.create(str(request.url), checklist_data)
        
        try:
            analysis_result = await seo_analyzer.analyze()
        finally:
            # SEOAnalyzer 사용 완료 후 리소스 정리
            try:
                seo_analyzer.cleanup_resources()
            except Exception as cleanup_error:
                print(f"⚠️ SEOAnalyzer 정리 중 오류 (무시됨): {cleanup_error}")

        # ✅ 분석 결과에 에러가 있는지 확인하고, 있다면 즉시 예외 발생
        if "error" in analysis_result:
            raise HTTPException(status_code=500, detail=f"SEO 분석 실패: {analysis_result['error']}")
        
        # 기존 SEO 분석과 AI 인사이트 결합
        combined_result_dict = await ai_analyzer.combine_analysis_with_ai_insights(analysis_result)

        # 페이지 최적화 섹션에 AI 콘텐츠 품질 분석 결과 통합
        for category in combined_result_dict.get("categories", []):
            for section in category.get("sections", []):
                if section["section_id"] == "page_optimization" and content_quality_result and "error" not in content_quality_result:
                        # 기존 AI 제안을 모두 제거하고 새로 생성 (다른 분석 결과와 섞이지 않도록)
                        section["ai_suggestions"] = []
                        
                        # 실제 분석 결과 기반 구체적 개선 제안 생성
                        ai_suggestion_id = 1
                        
                        # 실패한 항목들과 경고 항목들 분류 (optional_skip 제외)
                        failed_checks = [check for check in section.get("checks", []) 
                                       if check.get("status") == "fail"]
                        warning_checks = [check for check in section.get("checks", []) 
                                        if check.get("status") == "warning"]
                        
                        # 선택적 항목들 정의 (없어도 되는 구조화 데이터)
                        optional_structured_data = ["article", "product", "faqpage", "videoobject", 
                                                  "imageobject", "howto", "jobposting", "event"]
                        
                        # 선택적 항목들 중 optional_skip 상태인 것들만 제안에서 제외
                        # (구조화 데이터가 있지만 잘못 구성된 경우는 제안에 포함)
                        def should_exclude_optional_item(check):
                            status = check.get("status", "")
                            
                            # optional_skip 상태인 항목들만 제외 (없어서 건너뛴 것)
                            # fail/warning 상태인 구조화 데이터는 AI 제안에 포함되어야 함
                            if status == "optional_skip":
                                return True
                            
                            return False
                        
                        # 제외할 항목들 필터링
                        failed_checks = [check for check in failed_checks 
                                       if not should_exclude_optional_item(check)]
                        warning_checks = [check for check in warning_checks 
                                        if not should_exclude_optional_item(check)]
                        
                        # 실패한 필수 항목들에 대한 구체적 개선 제안
                        for check in failed_checks:
                            title = check.get("title", "").lower()
                            message = check.get("message", "")
                            importance = check.get("importance", 3)
                            check_id = check.get("id", ai_suggestion_id)  # 실제 체크 항목의 ID 사용
                            
                            # 유니코드 en dash(–) 표기와 하이픈(-) 표기 모두 인식
                            if ("title" in title and ("30-60" in title or "30–60" in title)):
                                section["ai_suggestions"].append({
                                    "id": check_id,
                                    "title": check.get('title', ''),  # 정확한 제목 포함
                                    "suggestion": f"제목 길이 최적화: {message} → 타겟 키워드를 포함하여 30-60자 범위로 조정하세요",
                                    "expected_impact": "검색 결과 완전 표시로 클릭률 20-30% 향상"
                                })
                            elif "meta description" in title:
                                section["ai_suggestions"].append({
                                    "id": check_id,
                                    "title": check.get('title', ''),
                                    "suggestion": f"메타 설명 최적화: {message} → CTA와 핵심 키워드를 포함한 70-155자 설명 작성",
                                    "expected_impact": "검색 결과 클릭률 15-25% 향상"
                                })
                            elif "h1" in title:
                                section["ai_suggestions"].append({
                                    "id": check_id,
                                    "title": check.get('title', ''),
                                    "suggestion": f"H1 태그 구조화: {message} → 페이지 주제를 명확히 표현하는 단일 H1 태그 설정",
                                    "expected_impact": "검색엔진 페이지 이해도 향상으로 순위 개선"
                                })
                            elif "viewport" in title:
                                section["ai_suggestions"].append({
                                    "id": check_id,
                                    "title": check.get('title', ''),
                                    "suggestion": f"모바일 반응형 설정: {message} → <meta name='viewport' content='width=device-width, initial-scale=1'> 추가",
                                    "expected_impact": "모바일 검색 순위 향상 및 사용자 경험 개선"
                                })
                            elif "charset" in title:
                                section["ai_suggestions"].append({
                                    "id": check_id,
                                    "title": check.get('title', ''),
                                    "suggestion": f"문자 인코딩 설정: {message} → HTML 헤드에 <meta charset='utf-8'> 추가",
                                    "expected_impact": "다국어 콘텐츠 호환성 및 검색엔진 인덱싱 개선"
                                })
                            elif "og:" in title or "open graph" in title:
                                section["ai_suggestions"].append({
                                    "id": check_id,
                                    "title": check.get('title', ''),
                                    "suggestion": f"소셜 미디어 최적화: {message} → og:title, og:description, og:image, og:url 태그 완성",
                                    "expected_impact": "소셜 공유 시 클릭률 40-60% 향상"
                                })
                            elif "breadcrumb" in title:
                                section["ai_suggestions"].append({
                                    "id": check_id,
                                    "title": check.get('title', ''),
                                    "suggestion": f"브레드크럼 네비게이션: {message} → JSON-LD BreadcrumbList 구조화 데이터 구현",
                                    "expected_impact": "사이트 구조 이해도 향상 및 검색 결과 리치 스니펫 표시"
                                })
                            elif "robots" in title and "meta" in title:
                                section["ai_suggestions"].append({
                                    "id": check_id,
                                    "title": check.get('title', ''),
                                    "suggestion": f"Meta Robots 태그: {message} → <meta name='robots' content='index,follow'> 설정 확인",
                                    "expected_impact": "검색엔진 크롤링 최적화"
                                })
                            elif "invalid field" in title:
                                section["ai_suggestions"].append({
                                    "id": check_id,
                                    "title": check.get('title', ''),
                                    "suggestion": f"구조화 데이터 오류 수정: {message} → Google Search Console에서 구조화 데이터 오류 확인 및 수정",
                                    "expected_impact": "리치 스니펫 표시 가능성 향상"
                                })
                            elif "breadcrumblist" in title:
                                section["ai_suggestions"].append({
                                    "id": check_id,
                                    "title": check.get('title', ''),
                                    "suggestion": f"BreadcrumbList 구조화 데이터: {message} → position 속성이 포함된 올바른 구조로 수정",
                                    "expected_impact": "브레드크럼 리치 스니펫 표시"
                                })
                            elif "article" in title:
                                section["ai_suggestions"].append({
                                    "id": check_id,
                                    "title": check.get('title', ''),
                                    "suggestion": f"Article 구조화 데이터 수정: {message} → headline, author, datePublished 속성 추가",
                                    "expected_impact": "뉴스/기사 리치 스니펫 표시 및 검색 노출 향상"
                                })
                            elif "product" in title:
                                section["ai_suggestions"].append({
                                    "id": check_id,
                                    "title": check.get('title', ''),
                                    "suggestion": f"Product 구조화 데이터 수정: {message} → name, price, availability 속성 추가",
                                    "expected_impact": "상품 리치 스니펫 표시 및 쇼핑 검색 노출 향상"
                                })
                            elif "faqpage" in title:
                                section["ai_suggestions"].append({
                                    "id": check_id,
                                    "title": check.get('title', ''),
                                    "suggestion": f"FAQPage 구조화 데이터 수정: {message} → Q&A 쌍의 mainEntity 구조 개선",
                                    "expected_impact": "FAQ 리치 스니펫 표시"
                                })
                            elif "videoobject" in title:
                                section["ai_suggestions"].append({
                                    "id": check_id,
                                    "title": check.get('title', ''),
                                    "suggestion": f"VideoObject 구조화 데이터 수정: {message} → duration ISO8601 형식으로 수정",
                                    "expected_impact": "동영상 리치 스니펫 표시"
                                })
                            elif "imageobject" in title:
                                section["ai_suggestions"].append({
                                    "id": check_id,
                                    "title": check.get('title', ''),
                                    "suggestion": f"ImageObject 구조화 데이터 수정: {message} → HTTPS URL로 수정",
                                    "expected_impact": "이미지 검색 최적화"
                                })
                            elif "howto" in title:
                                section["ai_suggestions"].append({
                                    "id": check_id,
                                    "title": check.get('title', ''),
                                    "suggestion": f"HowTo 구조화 데이터 수정: {message} → 3개 이상의 단계로 구성",
                                    "expected_impact": "가이드 리치 스니펫 표시"
                                })
                            elif "jobposting" in title:
                                section["ai_suggestions"].append({
                                    "id": check_id,
                                    "title": check.get('title', ''),
                                    "suggestion": f"JobPosting 구조화 데이터 수정: {message} → validThrough 만료일 추가",
                                    "expected_impact": "채용 정보 리치 스니펫 표시"
                                })
                            elif "event" in title:
                                section["ai_suggestions"].append({
                                    "id": check_id,
                                    "title": check.get('title', ''),
                                    "suggestion": f"Event 구조화 데이터 수정: {message} → startDate에 시간대 포함",
                                    "expected_impact": "이벤트 리치 스니펫 표시"
                                })
                            else:
                                # 기타 실패 항목들 - 제목과 메시지를 정확히 매칭
                                original_title = check.get('title', '')
                                section["ai_suggestions"].append({
                                    "id": check_id,
                                    "title": check.get('title', ''),
                                    "suggestion": f"{original_title}: {message}",
                                    "expected_impact": _generate_expected_impact(original_title, section.get("section_id", ""), importance)
                                })
                            
                            ai_suggestion_id += 1  # 다음 제안을 위한 증가
                        
                        # 경고 상태 항목들에 대한 개선 제안
                        for check in warning_checks:
                            title = check.get("title", "")
                            message = check.get("message", "")
                            importance = check.get("importance", 3)
                            check_id = check.get("id", ai_suggestion_id)  # 실제 체크 항목의 ID 사용
                            
                            section["ai_suggestions"].append({
                                "id": check_id,
                                "title": check.get('title', ''),
                                "suggestion": f"{title} 최적화: {message}",
                                "expected_impact": _generate_expected_impact(title, section.get("section_id", ""), importance)
                            })
                            ai_suggestion_id += 1

        # ✅ 모든 섹션에서 실패/경고 체크에 최소 1개의 제안 보장 (이미 존재하는 제안은 유지)
        for category in combined_result_dict.get("categories", []):
            for section in category.get("sections", []):
                if "ai_suggestions" not in section or section["ai_suggestions"] is None:
                    section["ai_suggestions"] = []

                existing_ids = {sug.get("id") for sug in (section.get("ai_suggestions") or [])}
                ai_suggestion_id = max([s.get("id", 0) for s in section.get("ai_suggestions") or []] + [0]) + 1

                failed_checks = [c for c in section.get("checks", []) if c.get("status") == "fail"]
                warning_checks = [c for c in section.get("checks", []) if c.get("status") == "warning"]
                # optional_skip 제외
                failed_checks = [c for c in failed_checks if c.get("status") != "optional_skip"]
                warning_checks = [c for c in warning_checks if c.get("status") != "optional_skip"]

                def add_generic_suggestion(check_obj):
                    nonlocal ai_suggestion_id
                    title = check_obj.get("title", "")
                    message = check_obj.get("message", "")
                    importance = check_obj.get("importance", 3)
                    check_id = check_obj.get("id", ai_suggestion_id)
                    section["ai_suggestions"].append({
                        "id": check_id,
                        "title": title,
                        "suggestion": f"{title}: {message}",
                        "expected_impact": _generate_expected_impact(title, section.get("section_id", ""), importance, message)
                    })
                    ai_suggestion_id += 1

                for check in failed_checks:
                    if check.get("id") not in existing_ids:
                        add_generic_suggestion(check)

                for check in warning_checks:
                    if check.get("id") not in existing_ids:
                        add_generic_suggestion(check)
        
        # ✅ SEO 분석 완료 후 자동 프로세스 정리
        try:
            await cleanup_analysis_processes()
            print("🧹 SEO 분석 완료 후 프로세스 정리 완료")
        except Exception as cleanup_error:
            print(f"⚠️ 프로세스 정리 중 오류 (무시됨): {cleanup_error}")

        # 성능 섹션에 대해 LCP/INP/FCP 명시적 제안 보강
        for category in combined_result_dict.get("categories", []):
            for section in category.get("sections", []):
                if (section.get("section_id") == "web_performance_technical"):
                    # 빠른 조회를 위해 현재 제안된 ids 갱신
                    existing_ids = {sug.get("id") for sug in (section.get("ai_suggestions") or [])}
                    def add_perf_suggestion(check_obj, suggestion_text):
                        nonlocal ai_suggestion_id
                        cid = check_obj.get("id", ai_suggestion_id)
                        if cid in existing_ids:
                            return
                        section["ai_suggestions"].append({
                            "id": cid,
                            "title": check_obj.get("title", ""),
                            "suggestion": suggestion_text,
                            "expected_impact": _generate_expected_impact(check_obj.get("title", ""), section.get("section_id", ""), check_obj.get("importance", 3), check_obj.get("message", ""))
                        })
                        ai_suggestion_id += 1
                        existing_ids.add(cid)

                    for check in (failed_checks + warning_checks):
                        title_l = (check.get("title") or "").lower()
                        msg = check.get("message", "")
                        if "lcp" in title_l:
                            add_perf_suggestion(check, f"LCP 단축: 이미지 용량 최적화(WebP/AVIF), 크리티컬 CSS 인라인/프리로드, 폰트 디스플레이 swap 설정, 렌더 블로킹 리소스 최소화. 현재 상태: {msg}")
                        elif "inp" in title_l or "tbt" in title_l:
                            add_perf_suggestion(check, f"INP 개선: 긴 작업(Long Tasks) 분할, 비필수 JS 지연/지연 로드, 이벤트 리스너 경량화, 서드파티 스크립트 지연. 현재 상태: {msg}")
                        elif "fcp" in title_l:
                            add_perf_suggestion(check, f"FCP 개선: 프리커넥트/프리로드 사용(폰트/핵심 리소스), 초기 렌더 경로 최소화, 서버 캐시/압축(Brotli) 활성화. 현재 상태: {msg}")

        # ✅ 기대효과 일괄 후처리: 모든 제안에 대해 항목별 상세 기대효과로 통일 생성
        for category in combined_result_dict.get("categories", []):
            for section in category.get("sections", []):
                checks = section.get("checks", [])
                id_to_check = {c.get("id"): c for c in checks}
                for s in section.get("ai_suggestions", []) or []:
                    s_title = s.get("title")
                    check = id_to_check.get(s.get("id"))
                    effective_title = s_title or (check.get("title") if check else "")
                    importance = check.get("importance") if check else 3
                    message = check.get("message") if check else ""
                    s["expected_impact"] = _generate_expected_impact(effective_title, section.get("section_id", ""), importance, message)

        # ✅ AI가 생성한 추천 목록을 최종 결과에 포함 (ai_analysis가 있는 경우에만)
        if "ai_analysis" in combined_result_dict and combined_result_dict["ai_analysis"]:
            combined_result_dict["recommendations"] = combined_result_dict["ai_analysis"].get("priority_recommendations", [])
        else:
            # ai_analysis가 없는 경우 기본 추천 목록 사용
            combined_result_dict["recommendations"] = []
        
        # critical_issues 필드가 없는 경우 추가
        if "critical_issues" not in combined_result_dict:
            critical_issues = []
            # 실패한 체크 항목들을 critical_issues로 추가
            for category in combined_result_dict.get("categories", []):
                for section in category.get("sections", []):
                    for check in section.get("checks", []):
                        if check.get("status") == "fail" and check.get("importance", 0) >= 3:
                            critical_issues.append(f"{check.get('title', 'Unknown')}: {check.get('message', '')}")
            
            combined_result_dict["critical_issues"] = critical_issues[:5]  # 상위 5개만
        
        # 디버깅: 결과 구조 확인
        print(f"🔍 최종 결과 구조 확인:")
        print(f"  - url: {combined_result_dict.get('url')}")
        print(f"  - overall_score: {combined_result_dict.get('overall_score')}")
        print(f"  - categories: {len(combined_result_dict.get('categories', []))}개")
        print(f"  - ai_analysis: {type(combined_result_dict.get('ai_analysis'))}")
        print(f"  - recommendations: {len(combined_result_dict.get('recommendations', []))}개")
        print(f"  - critical_issues: {len(combined_result_dict.get('critical_issues', []))}개")
        
        try:
            # 결과를 DB에 즉시 저장 (자동 저장)
            try:
                analysis = SEOAnalysis(
                    url=str(combined_result_dict.get("url", str(request.url))),
                    overall_score=int(combined_result_dict.get("overall_score", 0) or 0),
                    payload=combined_result_dict,
                    is_public=True,
                )
                session.add(analysis)
                await session.commit()
                await session.refresh(analysis)
                # 생성된 ID를 헤더로 반환
                response.headers["X-Analysis-Id"] = str(analysis.id)
            except Exception as save_e:
                # 저장 실패는 분석 응답 자체를 막지 않음
                await session.rollback()
                print(f"결과 자동 저장 실패: {save_e}")

            return SEOResult(**combined_result_dict)
        except Exception as validation_error:
            print(f"❌ ValidationError 상세 정보:")
            print(f"  - 오류 타입: {type(validation_error)}")
            print(f"  - 오류 메시지: {str(validation_error)}")
            print(f"  - 누락된 필드들:")
            for field in SEOResult.__fields__:
                if field not in combined_result_dict:
                    print(f"    - {field}: 누락됨")
                else:
                    field_value = combined_result_dict[field]
                    print(f"    - {field}: {type(field_value)} = {field_value}")
            print(f"  - 전체 결과 구조:")
            for key, value in combined_result_dict.items():
                print(f"    - {key}: {type(value)} = {value}")
            raise validation_error
    
    except Exception as e:
        # 서버 로그에 전체 오류 기록
        print(f"Unhandled error during SEO analysis for URL {request.url}: {e}")
        # Traceback을 로깅하여 디버깅 정보 확보
        import traceback
        traceback.print_exc()
        
        # 사용자에게 반환될 오류 응답 생성
        error_response = {
            "url": request.url,
            "overall_score": 0,
            "categories": [],
            "ai_analysis": {
                "overall_summary": f"'{request.url}'을(를) 분석하는 중 서버 오류가 발생했습니다. 잠시 후 다시 시도해 주세요. 오류: {str(e)}",
                "seo_level": "분석 실패",
                "strengths": [],
                "priority_recommendations": [],
                "technical_insights": [],
                "content_insights": [],
                "expected_score_improvement": "N/A",
                "implementation_timeline": "N/A"
            },
            "recommendations": []
        }
        # FastAPI가 자동으로 500 상태 코드를 반환하도록 예외 발생
        raise HTTPException(
            status_code=500,
            detail=f"분석 중 오류 발생: '{e.__class__.__name__}'"
        ) from e

 

@app.get("/analyze/test", response_model=SEOResult)
async def analyze_test():
    """풀 케이스 테스트 데이터 반환"""
    sample_data = {
        "url": "https://example.com",
        "checklist_version": "2.0",
        "categories": [
            {
                "category_id": "technical_seo",
                "category_name": "기술 SEO 6개 영역",
                "category_score": 92,
                "sections": [
                    {
                        "section_id": "crawling_indexing",
                        "section_name": "크롤링 및 색인 관리",
                        "checks": [
                            {
                                "id": 1,
                                "title": "robots.txt 200 응답",
                                "importance": 5,
                                "criteria": "GET /robots.txt → 200 OK",
                                "status": "pass",
                                "message": "robots.txt가 200 OK를 반환합니다.",
                                "score": 100
                            },
                            {
                                "id": 2,
                                "title": "User-agent=* Disallow 비율",
                                "importance": 5,
                                "criteria": "핵심 디렉터리 차단 0%",
                                "status": "pass",
                                "message": "모든 검색엔진의 접근을 막는 'Disallow: /' 규칙이 없습니다.",
                                "score": 100
                            },
                            {
                                "id": 3,
                                "title": "Sitemap 지시자 존재",
                                "importance": 4,
                                "criteria": "Sitemap: ≥1 줄",
                                "status": "pass",
                                "message": "robots.txt에 사이트맵 주소 1개를 확인했습니다.",
                                "score": 100
                            },
                            {
                                "id": 7,
                                "title": "<lastmod> 정확도",
                                "importance": 2,
                                "criteria": "페이지 갱신 ±72 h",
                                "status": "pass",
                                "message": "lastmod 정보가 샘플 5개 모두 정확합니다 (±72시간).",
                                "score": 100
                            }
                        ],
                        "total_checks": 4,
                        "passed_checks": 4,
                        "section_score": 100,
                        "ai_suggestions": []
                    },
                    {
                        "section_id": "page_optimization",
                        "section_name": "페이지 최적화",
                        "checks": [
                            {
                                "id": 1,
                                "title": "Title 30–60 자",
                                "importance": 5,
                                "criteria": "주요 키워드 포함",
                                "status": "pass",
                                "message": "제목 길이 최적화됨 (45자)",
                                "score": 100
                            },
                            {
                                "id": 2,
                                "title": "Meta Description 150–160 자",
                                "importance": 4,
                                "criteria": "타겟 키워드 포함",
                                "status": "pass",
                                "message": "메타 디스크립션 최적화됨 (155자)",
                                "score": 100
                            },
                            {
                                "id": 3,
                                "title": "H1 태그 1개",
                                "importance": 4,
                                "criteria": "페이지당 1개",
                                "status": "pass",
                                "message": "H1 태그가 1개 존재합니다.",
                                "score": 100
                            }
                        ],
                        "total_checks": 3,
                        "passed_checks": 3,
                        "section_score": 100,
                        "ai_suggestions": []
                    },
                    {
                        "section_id": "web_performance_technical",
                        "section_name": "웹 성능 및 기술적 요소",
                        "checks": [
                            {
                                "id": 1,
                                "title": "LCP ≤2.5초",
                                "importance": 5,
                                "criteria": "모바일 기준",
                                "status": "pass",
                                "message": "LCP: 1.8초 (최적)",
                                "score": 100
                            },
                            {
                                "id": 2,
                                "title": "INP ≤200ms",
                                "importance": 5,
                                "criteria": "모바일 기준",
                                "status": "pass",
                                "message": "INP: 150ms (최적)",
                                "score": 100
                            },
                            {
                                "id": 3,
                                "title": "CLS ≤0.1",
                                "importance": 5,
                                "criteria": "모바일 기준",
                                "status": "pass",
                                "message": "CLS: 0.05 (최적)",
                                "score": 100
                            }
                        ],
                        "total_checks": 3,
                        "passed_checks": 3,
                        "section_score": 100,
                        "ai_suggestions": []
                    }
                ]
            }
        ],
        "overall_score": 92,
        "category_scores": {
            "technical_seo": 92
        },
        "total_checks": 10,
        "passed_checks": 10,
        "failed_checks": 0,
        "ai_analysis": {
            "overall_summary": "전반적으로 우수한 SEO 상태입니다. 모든 핵심 요소가 최적화되어 있으며, 검색엔진 최적화가 잘 되어 있습니다.",
            "seo_level": "고급",
            "strengths": ["완벽한 robots.txt 설정", "최적화된 메타 태그", "우수한 웹 성능"],
            "priority_recommendations": [
                {
                    "priority": "낮음",
                    "title": "콘텐츠 품질 향상",
                    "description": "현재 기술적 SEO는 완벽하므로, 콘텐츠 품질과 사용자 경험을 더욱 향상시켜 보세요.",
                    "category": "콘텐츠 최적화",
                    "expected_impact": "사용자 참여도 증가"
                }
            ],
            "technical_insights": ["모든 기술적 요소가 최적화됨"],
            "content_insights": ["콘텐츠 품질 향상 여지 있음"],
            "expected_score_improvement": "+5-8점",
            "implementation_timeline": "1-2주"
        },
        "recommendations": [
            {
                "priority": "낮음",
                "title": "콘텐츠 품질 향상",
                "description": "현재 기술적 SEO는 완벽하므로, 콘텐츠 품질과 사용자 경험을 더욱 향상시켜 보세요.",
                "category": "콘텐츠 최적화",
                "expected_impact": "사용자 참여도 증가"
            }
        ],
        "critical_issues": []
    }
    return SEOResult(**sample_data)

@app.get("/analyze/test/bad", response_model=SEOResult)
async def analyze_test_bad():
    """에러 케이스 테스트 데이터 반환"""
    sample_data = {
        "url": "https://example.com",
        "checklist_version": "2.0",
        "categories": [
            {
                "category_id": "technical_seo",
                "category_name": "기술 SEO 6개 영역",
                "category_score": 35,
                "sections": [
                    {
                        "section_id": "crawling_indexing",
                        "section_name": "크롤링 및 색인 관리",
                        "checks": [
                            {
                                "id": 1,
                                "title": "robots.txt 200 응답",
                                "importance": 5,
                                "criteria": "GET /robots.txt → 200 OK",
                                "status": "fail",
                                "message": "robots.txt를 찾을 수 없습니다 (상태 코드: 404).",
                                "score": 0
                            },
                            {
                                "id": 2,
                                "title": "User-agent=* Disallow 비율",
                                "importance": 5,
                                "criteria": "핵심 디렉터리 차단 0%",
                                "status": "fail",
                                "message": "치명적 오류: 'User-agent: *'에 'Disallow: /'가 설정되어 모든 검색엔진을 차단합니다.",
                                "score": 0
                            },
                            {
                                "id": 3,
                                "title": "Sitemap 지시자 존재",
                                "importance": 4,
                                "criteria": "Sitemap: ≥1 줄",
                                "status": "fail",
                                "message": "robots.txt 파일이 없습니다.",
                                "score": 0
                            },
                            {
                                "id": 7,
                                "title": "<lastmod> 정확도",
                                "importance": 2,
                                "criteria": "페이지 갱신 ±72 h",
                                "status": "fail",
                                "message": "사이트맵에 lastmod 태그가 없습니다.",
                                "score": 20
                            }
                        ],
                        "total_checks": 4,
                        "passed_checks": 0,
                        "section_score": 5,
                        "ai_suggestions": [
                            {
                                "id": 1,
                                "suggestion": "robots.txt 파일을 서버 루트 디렉터리에 생성하고, 검색엔진이 접근할 수 있도록 설정하세요.",
                                "expected_impact": "검색엔진 크롤링 허용 및 색인 개선"
                            },
                            {
                                "id": 2,
                                "suggestion": "User-agent: *에 Disallow: / 규칙을 제거하고, 필요한 페이지만 선택적으로 차단하세요.",
                                "expected_impact": "검색엔진 접근 허용 및 순위 향상"
                            },
                            {
                                "id": 3,
                                "suggestion": "robots.txt에 Sitemap 지시자를 추가하여 사이트맵 위치를 명시하세요.",
                                "expected_impact": "검색엔진 색인 효율성 증대"
                            },
                            {
                                "id": 7,
                                "suggestion": "사이트맵 XML 파일에 lastmod 태그를 추가하여 페이지 수정 시간을 정확히 표시하세요.",
                                "expected_impact": "검색엔진 크롤링 최적화"
                            }
                        ]
                    },
                    {
                        "section_id": "page_optimization",
                        "section_name": "페이지 최적화",
                        "checks": [
                            {
                                "id": 1,
                                "title": "Title 30–60 자",
                                "importance": 5,
                                "criteria": "주요 키워드 포함",
                                "status": "fail",
                                "message": "제목이 너무 짧습니다 (15자)",
                                "score": 30
                            },
                            {
                                "id": 2,
                                "title": "Meta Description 150–160 자",
                                "importance": 4,
                                "criteria": "타겟 키워드 포함",
                                "status": "fail",
                                "message": "메타 디스크립션이 없습니다.",
                                "score": 0
                            },
                            {
                                "id": 3,
                                "title": "H1 태그 1개",
                                "importance": 4,
                                "criteria": "페이지당 1개",
                                "status": "fail",
                                "message": "H1 태그가 없습니다.",
                                "score": 0
                            }
                        ],
                        "total_checks": 3,
                        "passed_checks": 0,
                        "section_score": 10,
                        "ai_suggestions": [
                            {
                                "id": 1,
                                "suggestion": "페이지 제목을 30-60자로 늘리고 주요 키워드를 포함하세요.",
                                "expected_impact": "검색 결과 노출 개선 및 클릭률 증가"
                            },
                            {
                                "id": 2,
                                "suggestion": "메타 디스크립션을 150-160자로 작성하고 타겟 키워드를 포함하세요.",
                                "expected_impact": "검색 결과 클릭률 향상"
                            },
                            {
                                "id": 3,
                                "suggestion": "페이지에 H1 태그를 1개 추가하여 콘텐츠 구조를 명확히 하세요.",
                                "expected_impact": "검색엔진 이해도 향상 및 순위 개선"
                            }
                        ]
                    },
                    {
                        "section_id": "web_performance_technical",
                        "section_name": "웹 성능 및 기술적 요소",
                        "checks": [
                            {
                                "id": 1,
                                "title": "LCP ≤2.5초",
                                "importance": 5,
                                "criteria": "모바일 기준",
                                "status": "fail",
                                "message": "LCP: 4.2초 (느림)",
                                "score": 20
                            },
                            {
                                "id": 2,
                                "title": "INP ≤200ms",
                                "importance": 5,
                                "criteria": "모바일 기준",
                                "status": "fail",
                                "message": "INP: 350ms (느림)",
                                "score": 20
                            },
                            {
                                "id": 3,
                                "title": "CLS ≤0.1",
                                "importance": 5,
                                "criteria": "모바일 기준",
                                "status": "fail",
                                "message": "CLS: 0.25 (나쁨)",
                                "score": 20
                            }
                        ],
                        "total_checks": 3,
                        "passed_checks": 0,
                        "section_score": 20,
                        "ai_suggestions": [
                            {
                                "id": 1,
                                "suggestion": "이미지 최적화, CDN 사용, 서버 응답 시간 개선을 통해 LCP를 2.5초 이하로 단축하세요.",
                                "expected_impact": "사용자 경험 개선 및 이탈률 감소"
                            },
                            {
                                "id": 2,
                                "suggestion": "JavaScript 최적화, 이벤트 핸들러 개선을 통해 INP를 200ms 이하로 단축하세요.",
                                "expected_impact": "사용자 상호작용 반응성 향상"
                            },
                            {
                                "id": 3,
                                "suggestion": "레이아웃 시프트를 방지하기 위해 이미지와 광고에 고정 크기를 설정하세요.",
                                "expected_impact": "시각적 안정성 향상 및 사용자 경험 개선"
                            }
                        ]
                    }
                ]
            }
        ],
        "overall_score": 35,
        "category_scores": {
            "technical_seo": 35
        },
        "total_checks": 10,
        "passed_checks": 0,
        "failed_checks": 10,
        "ai_analysis": {
            "overall_summary": "심각한 SEO 문제가 다수 발견되었습니다. 즉시 개선이 필요한 핵심 요소들이 있습니다.",
            "seo_level": "초급",
            "strengths": [],
            "priority_recommendations": [
                {
                    "priority": "높음",
                    "title": "robots.txt 파일 생성",
                    "description": "검색엔진이 사이트에 접근할 수 있도록 robots.txt 파일을 생성하고 올바르게 설정하세요.",
                    "category": "크롤링 최적화",
                    "expected_impact": "검색엔진 접근 허용 및 색인 개선"
                },
                {
                    "priority": "높음",
                    "title": "메타 태그 최적화",
                    "description": "Title과 Meta Description을 적절한 길이로 작성하고 타겟 키워드를 포함하세요.",
                    "category": "페이지 최적화",
                    "expected_impact": "검색 결과 노출 및 클릭률 향상"
                },
                {
                    "priority": "높음",
                    "title": "웹 성능 개선",
                    "description": "LCP, INP, CLS 등 Core Web Vitals를 개선하여 사용자 경험을 향상시키세요.",
                    "category": "성능 최적화",
                    "expected_impact": "사용자 경험 개선 및 순위 향상"
                }
            ],
            "technical_insights": ["robots.txt 누락", "메타 태그 부족", "성능 문제"],
            "content_insights": ["콘텐츠 구조 개선 필요"],
            "expected_score_improvement": "+40-50점",
            "implementation_timeline": "4-6주"
        },
        "recommendations": [
            {
                "priority": "높음",
                "title": "robots.txt 파일 생성",
                "description": "검색엔진이 사이트에 접근할 수 있도록 robots.txt 파일을 생성하고 올바르게 설정하세요.",
                "category": "크롤링 최적화",
                "expected_impact": "검색엔진 접근 허용 및 색인 개선"
            },
            {
                "priority": "높음",
                "title": "메타 태그 최적화",
                "description": "Title과 Meta Description을 적절한 길이로 작성하고 타겟 키워드를 포함하세요.",
                "category": "페이지 최적화",
                "expected_impact": "검색 결과 노출 및 클릭률 향상"
            },
            {
                "priority": "높음",
                "title": "웹 성능 개선",
                "description": "LCP, INP, CLS 등 Core Web Vitals를 개선하여 사용자 경험을 향상시키세요.",
                "category": "성능 최적화",
                "expected_impact": "사용자 경험 개선 및 순위 향상"
            }
        ],
        "critical_issues": [
            "robots.txt 파일 누락",
            "모든 검색엔진 차단",
            "메타 태그 부족",
            "웹 성능 문제"
        ]
    }
    return SEOResult(**sample_data)

 

@app.post("/result", response_model=SaveAnalysisResponse)
async def save_result(payload: SaveAnalysisRequest, session: AsyncSession = Depends(get_session)):
    try:
        ttl_days = payload.ttl_days if payload.ttl_days and payload.ttl_days > 0 else 7
        from datetime import datetime, timedelta

        user: Optional[User] = None
        if payload.user_email:
            # Upsert user by email
            result = await session.execute(select(User).where(User.email == payload.user_email))
            user = result.scalars().first()
            if user is None:
                user = User(email=payload.user_email, display_name=payload.user_display_name)
                session.add(user)
                await session.flush()

        analysis = SEOAnalysis(
            url=str(payload.result.get("url", "")),
            overall_score=int(payload.result.get("overall_score", 0) or 0),
            payload=dict(payload.result),
            is_public=payload.is_public,
            expired_at=datetime.utcnow() + timedelta(days=ttl_days),
            user_id=user.id if user else None,
        )
        session.add(analysis)
        await session.commit()
        await session.refresh(analysis)

        base_url = os.getenv("PUBLIC_BASE_URL") or f"http://localhost:{os.getenv('PORT','8000')}"
        share_url = f"{base_url}/result/{analysis.id}"

        return SaveAnalysisResponse(id=str(analysis.id), share_url=share_url, created_at=analysis.created_at)
    except Exception as e:
        await session.rollback()
        raise HTTPException(status_code=500, detail=f"결과 저장 실패: {str(e)}")


@app.get("/result/{analysis_id}", response_model=SEOResult)
async def load_result(analysis_id: str, session: AsyncSession = Depends(get_session)):
    # Return the stored payload directly as SEOResult for compatibility with UI
    result = await session.get(SEOAnalysis, analysis_id)
    if result is None:
        raise HTTPException(status_code=404, detail="분석 결과를 찾을 수 없습니다.")
    # Check public and TTL
    from datetime import datetime
    if not result.is_public:
        raise HTTPException(status_code=403, detail="이 분석 결과는 공개되지 않았습니다.")
    if result.expired_at and result.expired_at < datetime.utcnow():
        raise HTTPException(status_code=410, detail="이 분석 결과는 만료되었습니다.")
    try:
        return SEOResult(**result.payload)
    except Exception:
        # if legacy payload shape, at least return something descriptive
        raise HTTPException(status_code=500, detail="저장된 결과 형식에 문제가 있습니다.")


@app.get("/results", response_model=ResultsResponse)
async def list_results(page: int = 1, page_size: int = 20, session: AsyncSession = Depends(get_session)):
    if page < 1 or page_size < 1 or page_size > 100:
        raise HTTPException(status_code=400, detail="잘못된 페이지 파라미터")
    from sqlalchemy import select, func
    # total count
    total_q = await session.execute(select(func.count()).select_from(SEOAnalysis))
    total = total_q.scalar() or 0
    # items
    offset = (page - 1) * page_size
    q = await session.execute(
        select(SEOAnalysis).order_by(SEOAnalysis.created_at.desc()).offset(offset).limit(page_size)
    )
    rows = q.scalars().all()
    items = [
        AnalysisListItem(
            id=str(r.id), url=r.url, overall_score=int(r.overall_score or 0), created_at=r.created_at
        )
        for r in rows
    ]
    return ResultsResponse(items=items, total=total, page=page, page_size=page_size)


if __name__ == "__main__":
    host = os.getenv("HOST", "0.0.0.0")
    port = int(os.getenv("PORT", "8000"))
    reload = is_development
    
    print(f"🚀 서버 시작: {'개발' if is_development else '배포'} 모드")
    print(f"📍 주소: http://{host}:{port}")
    print(f"🔄 자동 재로드: {reload}")
    
    uvicorn.run("main:app", host=host, port=port, reload=reload) 