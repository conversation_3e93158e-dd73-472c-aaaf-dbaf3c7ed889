import React, { useEffect, useState, useId } from "react";
import { useLocation, useParams, useNavigate } from "react-router-dom";
import SEOReport from "../../components/SEOReport/SEOReport";
import { getApiUrl } from "../../config/api";
import { SEOAnalysisResult } from "../../types";
import styles from "./ResultPage.module.scss";
import classNames from "classnames/bind";
import { saveResult } from "../../services/api";

const cx = classNames.bind(styles);

interface CategoryInfo {
  id: string;
  name: string;
  score: number;
}

const getCategoryList = (result: SEOAnalysisResult): CategoryInfo[] => {
  if (!result || !result.categories) {
    return [];
  }

  const categoryList = result.categories.flatMap((category) =>
    category.sections.map((section) => ({
      id: section.section_id,
      name: section.section_name,
      score: section.section_score,
    }))
  );

  return categoryList;
};

const getScoreClass = (score: number): string => {
  if (score >= 90) return "excellent";
  if (score >= 70) return "good";
  if (score >= 40) return "warning";
  return "danger";
};

const getStatusText = (score: number): string => {
  if (score >= 90) return "우수 상태";
  if (score >= 70) return "양호";
  if (score >= 40) return "개선 권장";
  return "긴급 개선 필요";
};

const getPriorityClass = (priority: string): string => {
  switch (priority) {
    case "높음":
      return "priorityHigh";
    case "중간":
      return "priorityMedium";
    case "낮음":
      return "priorityLow";
    default:
      return "";
  }
};

const ResultPage: React.FC = () => {
  const location = useLocation();
  const params = useParams<{ analysisId: string }>();
  const [result, setResult] = useState<SEOAnalysisResult | null>(
    (location.state?.result as SEOAnalysisResult) || null
  );
  const [animatedScore, setAnimatedScore] = useState(0);
  // 공유 링크 복사만 수행하므로 로딩/링크 상태는 사용하지 않음
  const navigate = useNavigate();
  const uniqueId = useId();
  useEffect(() => {
    // URL 파라미터로 진입한 경우, 서버에서 결과를 로드
    const fetchById = async (id: string) => {
      try {
        const res = await fetch(getApiUrl(`/result/${id}`));
        if (!res.ok) throw new Error("failed");
        const data = await res.json();
        setResult(data);
      } catch (e) {
        // 에러 페이지로 이동
        navigate("/error", {
          state: { message: "분석 결과를 불러올 수 없습니다." },
        });
      }
    };
    if (!result && params.analysisId) {
      fetchById(params.analysisId);
    }
    if (result) {
      const targetScore = result.overall_score;
      if (targetScore === 0) {
        setAnimatedScore(0);
        return;
      }

      let current = 0;
      const increment = targetScore / 50;
      const timer = setInterval(() => {
        current += increment;
        if (current >= targetScore) {
          current = targetScore;
          clearInterval(timer);
        }
        setAnimatedScore(Math.floor(current));
      }, 30);

      return () => clearInterval(timer);
    }
  }, [result, params?.analysisId]);

  if (!result) return null;

  const categoryList = getCategoryList(result);
  const conicGradient = `conic-gradient(#4c63b6 0deg ${
    result.overall_score * 3.6
  }deg, #e9ecef ${result.overall_score * 3.6}deg 360deg)`;

  const { ai_analysis, recommendations } = result;

  return (
    <div className={cx("dashboard")} data-testid="result-page">
      <div
        className={cx("actions-row")}
        style={{
          display: "flex",
          gap: 12,
          justifyContent: "flex-end",
          marginBottom: 12,
        }}
      >
        <button
          type="button"
          onClick={async () => {
            const saved = await saveResult(result, {
              isPublic: true,
              ttlDays: 7,
            });
            try {
              await navigator.clipboard.writeText(
                `${window.location.origin}/result/${saved.id}`
              );
              alert("공유 링크가 복사되었습니다.");
            } catch {
              // 무시
            }
          }}
          className={cx("save-button")}
        >
          공유 링크 복사
        </button>
        {/* 공유 링크 열기 링크는 표시하지 않음 */}
      </div>
      <div className={cx("score-and-categories")}>
        <div className={cx("main-score")} data-testid="main-score-section">
          <div className={cx("score-circle")}>
            <div
              className={cx("circle-bg")}
              style={{ background: conicGradient }}
            >
              <div className={cx("circle-inner")}>
                <div className={cx("score-number")} data-testid="overall-score">
                  {animatedScore}
                </div>
                <div className={cx("score-label")} data-testid="score-label">
                  종합 점수
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className={cx("categories-panel")}>
          <div className={cx("categories-grid")} data-testid="categories-grid">
            {categoryList.map((category) => (
              <button
                type="button"
                key={`${category.id}-${uniqueId}`}
                className={cx("category-card", getScoreClass(category.score))}
                data-testid={`category-${category.id}`}
                onClick={(e) => {
                  e.preventDefault();
                  const targetSection = document.getElementById(category.id);
                  if (targetSection) {
                    const targetPosition = targetSection.offsetTop - 100;
                    window.scrollTo({
                      top: targetPosition,
                      behavior: "smooth",
                    });
                  }
                }}
              >
                <div className={cx("category-header")}>
                  <div className={cx("category-info")}>
                    <h3
                      className={cx("category-title")}
                      data-testid={`category-title-${category.id}`}
                    >
                      {category.name}
                    </h3>
                    <div
                      className={cx("category-score")}
                      data-testid={`category-score-${category.id}`}
                    >
                      {category.score}/100
                    </div>
                  </div>
                  <div className={cx("progress-bar")}>
                    <div
                      className={cx("progress-fill")}
                      style={{ width: `${category.score}%` }}
                    ></div>
                  </div>
                  <div className={cx("category-status")}>
                    <div className={cx("status-icon")}></div>
                    <span data-testid={`category-status-${category.id}`}>
                      {getStatusText(category.score)}
                    </span>
                  </div>
                </div>
              </button>
            ))}
          </div>
        </div>
      </div>

      <div className={cx("ai-insights")} data-testid="ai-analysis">
        <div className={cx("insights-header")}>
          <div className={cx("insights-icon")}>🤖</div>
          <div className={cx("insights-title")} data-testid="ai-insights-title">
            AI 종합 분석 요약
          </div>
        </div>

        <p
          className={cx("ai-overall-summary")}
          data-testid="ai-overall-summary"
        >
          {ai_analysis.overall_summary}
        </p>

        <div className={cx("ai-highlights")} data-testid="ai-highlights">
          <div className={cx("highlight-box")} data-testid="seo-level-box">
            <h4>SEO 레벨</h4>
            <p data-testid="seo-level">{ai_analysis.seo_level}</p>
          </div>
          <div
            className={cx("highlight-box")}
            data-testid="expected-improvement-box"
          >
            <h4>예상 개선 점수</h4>
            <p data-testid="expected-improvement">
              +{ai_analysis.expected_score_improvement}
            </p>
          </div>
          <div className={cx("highlight-box")} data-testid="timeline-box">
            <h4>예상 소요 기간</h4>
            <p data-testid="timeline">{ai_analysis.implementation_timeline}</p>
          </div>
        </div>

        <div className={cx("insights-grid2")} data-testid="insights-grid">
          <div
            className={cx("insight-section")}
            data-testid="strengths-section"
          >
            <h4>👍 강점</h4>
            <ul className={cx("insight-list")} data-testid="strengths-list">
              {ai_analysis.strengths.map((item, index) => (
                <li key={`strength-${index}`} data-testid={`strength-${index}`}>
                  {item}
                </li>
              ))}
            </ul>
          </div>

          <div
            className={cx("insight-section")}
            data-testid="urgent-improvements-section"
          >
            <h4>⚠️ 긴급 개선사항</h4>
            <ul
              className={cx("insight-list")}
              data-testid="urgent-improvements-list"
            >
              {ai_analysis.technical_insights.map((item, index) => (
                <li
                  key={`tech-insight-${index}`}
                  data-testid={`urgent-improvement-${index}`}
                >
                  {item}
                </li>
              ))}
            </ul>
          </div>

          <div
            className={cx("insight-section")}
            data-testid="priority-recommendations-section"
          >
            <h4>💡 주요 개선사항</h4>
            <ul
              className={cx("insight-list")}
              data-testid="priority-recommendations-list"
            >
              {ai_analysis.priority_recommendations
                .filter((item) => item.priority === "높음")
                .map((item, index) => (
                  <li
                    key={`urgent-${index}`}
                    className={cx(getPriorityClass(item.priority))}
                    data-testid={`priority-recommendation-${index}`}
                  >
                    {item.title}
                  </li>
                ))}
            </ul>
          </div>

          <div
            className={cx("insight-section")}
            data-testid="content-insights-section"
          >
            <h4>✍️ 콘텐츠 개선사항</h4>
            <ul
              className={cx("insight-list")}
              data-testid="content-insights-list"
            >
              {ai_analysis.content_insights.map((item, index) => (
                <li
                  key={`content-insight-${index}`}
                  data-testid={`content-insight-${index}`}
                >
                  {item}
                </li>
              ))}
            </ul>
          </div>
        </div>
      </div>

      <div
        className={cx("recommendations")}
        data-testid="recommendations-section"
      >
        <h4>📋 다음 단계 권장사항</h4>
        <p>현재 SEO 점수 향상을 위한 우선순위별 개선 계획:</p>
        <div
          className={cx("recommendations-grid")}
          data-testid="recommendations-grid"
        >
          {recommendations.slice(0, 3).map((rec, index) => {
            const priorityColor = ["#dc2626", "#ff9a26", "#059669"];
            return (
              <div
                key={`rec-plan-${index}`}
                className={cx("recommendation-item")}
                data-testid={`recommendation-${index}`}
                style={{ borderLeftColor: priorityColor[index] }}
              >
                <strong
                  className={cx("recommendation-week")}
                  style={{ color: priorityColor[index] }}
                >
                  {index + 1}주차
                </strong>
                <br />
                <small className={cx("recommendation-title")}>
                  {rec.title}
                </small>
              </div>
            );
          })}
        </div>
      </div>

      {/* Detailed report is still available below the new dashboard */}
      <div
        className={cx("detailed-report-section")}
        data-testid="detailed-report"
      >
        <SEOReport result={result} />
      </div>
    </div>
  );
};

export default ResultPage;
