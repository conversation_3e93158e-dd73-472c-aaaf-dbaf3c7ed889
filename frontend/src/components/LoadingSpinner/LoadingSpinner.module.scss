.container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  backdrop-filter: blur(5px);
}

.content {
  background: white;
  border-radius: 20px;
  padding: 3rem;
  text-align: center;
  max-width: 500px;
  width: 90%;
}

.icon-wrapper {
  position: relative;
  margin-bottom: 2rem;
  display: inline-block;
}

.chart-icon {
  width: 60px;
  height: 60px;
  color: #667eea;
  animation: float 3s ease-in-out infinite;
}

.brain-icon {
  position: absolute;
  top: -10px;
  right: -10px;
  width: 30px;
  height: 30px;
  color: #fbbf24;
  animation: pulse 2s ease-in-out infinite;
}

.title {
  font-size: 1.8rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 2rem;
}

.progress-steps {
  display: flex;
  justify-content: space-between;
  margin-bottom: 2rem;
  gap: 1rem;
}

.step {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  flex: 1;
  opacity: 0.5;
  transition: all 0.3s ease;

  &.active {
    opacity: 1;
  }

  span {
    font-size: 0.9rem;
    color: #666;
    text-align: center;
  }
}

.step-icon {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: #f3f4f6;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  color: #666;
  transition: all 0.3s ease;

  .active & {
    background: #667eea;
    color: white;
  }
}

.progress-bar {
  width: 100%;
  height: 8px;
  background: #f3f4f6;
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 1rem;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #667eea, #764ba2);
  border-radius: 4px;
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 1.1rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 1rem;
}

.description {
  color: #666;
  line-height: 1.5;
  margin: 0;
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes pulse {
  0%,
  100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}
