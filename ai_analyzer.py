import google.generativeai as genai
import json
import os
from typing import Dict, List
import re
import httpx
from bs4 import BeautifulSoup
import urllib.parse
import time
import asyncio

class AIAnalyzer:
    def __init__(self):
        self.api_keys = self._load_api_keys()
        if not self.api_keys:
            raise ValueError("GOOGLE_API_KEY 환경 변수가 설정되지 않았습니다.")
        
        self.current_key_index = 0
        self.model = None
        self._configure_current_api_key()
        
        # API 키별 사용 통계
        self.key_usage_stats = {key: {'success': 0, 'failure': 0, 'last_used': None} for key in self.api_keys}

    def _load_api_keys(self) -> List[str]:
        """환경 변수에서 API 키들을 로드합니다."""
        api_keys = []
        
        # 메인 API 키
        main_key = os.getenv("GOOGLE_API_KEY")
        if main_key:
            api_keys.append(main_key)
        
        # 스페어 API 키들 (GOOGLE_API_KEY_1, GOOGLE_API_KEY_2, ...)
        i = 1
        while True:
            spare_key = os.getenv(f"GOOGLE_API_KEY_{i}")
            if not spare_key:
                break
            api_keys.append(spare_key)
            i += 1
        
        # 쉼표로 구분된 여러 키 (GOOGLE_API_KEYS)
        combined_keys = os.getenv("GOOGLE_API_KEYS")
        if combined_keys:
            keys_from_env = [key.strip() for key in combined_keys.split(",") if key.strip()]
            api_keys.extend(keys_from_env)
        
        # 중복 제거
        unique_keys = list(dict.fromkeys(api_keys))
        print(f"로드된 API 키 개수: {len(unique_keys)}")
        return unique_keys

    def _configure_current_api_key(self):
        """현재 선택된 API 키로 Gemini를 설정합니다."""
        if self.current_key_index >= len(self.api_keys):
            raise ValueError("모든 API 키가 소진되었습니다.")
        
        current_key = self.api_keys[self.current_key_index]
        genai.configure(api_key=current_key)
        self.model = genai.GenerativeModel('gemini-2.0-flash')
        print(f"API 키 {self.current_key_index + 1}/{len(self.api_keys)} 사용 중")

    def _switch_to_next_api_key(self) -> bool:
        """다음 API 키로 전환합니다."""
        self.current_key_index += 1
        if self.current_key_index >= len(self.api_keys):
            print("모든 API 키가 소진되었습니다.")
            return False
        
        print(f"API 키 {self.current_key_index + 1}/{len(self.api_keys)}로 전환")
        self._configure_current_api_key()
        print(f"새로운 모델 인스턴스 생성 완료")
        return True

    def _is_api_error_retryable(self, error: Exception) -> bool:
        """API 오류가 재시도 가능한지 확인합니다."""
        error_str = str(error).lower()
        
        # 재시도 가능한 오류들
        retryable_errors = [
            'quota exceeded',
            'rate limit',
            'quota',
            'limit exceeded',
            'too many requests',
            'resource exhausted',
            'permission denied',
            'invalid api key',
            'authentication',
            'api key'
        ]
        
        return any(retry_error in error_str for retry_error in retryable_errors)

    async def _make_api_call_with_retry(self, prompt: str):
        """API 호출을 재시도 로직과 함께 수행합니다."""
        max_retries = len(self.api_keys)
        retry_count = 0
        
        while retry_count < max_retries:
            try:
                # API 키 사용 통계 업데이트
                current_key = self.api_keys[self.current_key_index]
                self.key_usage_stats[current_key]['last_used'] = time.time()
                
                # 현재 모델로 API 호출 수행
                result = await self.model.generate_content_async(prompt)
                
                # 성공 시 통계 업데이트
                self.key_usage_stats[current_key]['success'] += 1
                return result
                
            except Exception as e:
                # 실패 시 통계 업데이트
                current_key = self.api_keys[self.current_key_index]
                self.key_usage_stats[current_key]['failure'] += 1
                
                print(f"API 호출 실패 (키 {self.current_key_index + 1}): {e}")
                
                # 재시도 가능한 오류인지 확인
                if self._is_api_error_retryable(e):
                    print(f"재시도 가능한 오류로 판단됨: {str(e)[:100]}...")
                    if self._switch_to_next_api_key():
                        retry_count += 1
                        print(f"재시도 {retry_count}/{max_retries} 시도 중...")
                        continue
                    else:
                        print("사용 가능한 API 키가 없습니다.")
                        raise e
                else:
                    # 재시도 불가능한 오류 (예: 잘못된 요청 형식)
                    print(f"재시도 불가능한 오류입니다: {str(e)[:100]}...")
                    raise e
        
        # 모든 재시도 실패
        raise Exception("모든 API 키로 시도했지만 실패했습니다.")

    def get_api_key_status(self) -> Dict:
        """현재 API 키들의 상태를 반환합니다."""
        return {
            'current_key_index': self.current_key_index,
            'total_keys': len(self.api_keys),
            'usage_stats': self.key_usage_stats
        }

    def _create_prompt(self, analysis_result: Dict) -> str:
        analysis_str = json.dumps(analysis_result, indent=2, ensure_ascii=False)
        
        prompt = f"""
당신은 세계 최고 수준의 SEO 컨설턴트입니다. 주어진 SEO 분석 결과를 바탕으로, 전문가적이고 실행 가능한 심층 분석 보고서를 작성해야 합니다.

중요: 아래의 모든 텍스트 출력은 반드시 한국어로 작성하십시오. JSON의 키 이름은 예시와 동일한 영문을 사용하되, 값 문자열은 모두 자연스러운 한국어로 작성하세요.

**분석 결과:**
```json
{analysis_str}
```

**보고서 작성 지침:**
주어진 분석 결과를 해석하여, 다음 항목들을 포함한 전문가 보고서를 JSON 형식으로 작성해주십시오. 특히 '분석 미지원'으로 표시된 항목들을 중점적으로 추론하고 평가해야 합니다.

1.  **페이지 최적화 (page_optimization)**: 메타데이터(Title, Meta Description, H1), Open Graph 태그, 구조화 데이터(Article, Product, FAQPage 등), Invalid Field 검사 등의 결과를 바탕으로 페이지 최적화 수준을 평가하십시오.
2.  **URL 및 콘텐츠 표준화 (추론)**: URL 구조의 의미성 등을 `url_content_standardization` 섹션 결과를 바탕으로 평가하고, '분석 미지원' 항목에 대한 의견을 제시하십시오.
3.  **사이트 구조 및 사용자 경험 (추론)**: 내부 링크 구조, 탐색 용이성 등을 `site_structure_ux` 섹션 결과를 바탕으로 평가하고, '분석 미지원' 항목에 대한 의견을 제시하십시오.
4.  **전반적인 요약 (overall_summary)**: 위 내용을 포함한 모든 분석 결과를 종합하여 사이트의 현재 SEO 상태를 2-3 문장으로 요약합니다.
5.  **SEO 레벨 (seo_level)**: "초급", "중급", "고급", "전문가" 중 하나로 사이트의 SEO 수준을 평가합니다.
6.  **강점 (strengths)**: 'pass'된 항목 중 가장 돋보이는 강점 2-3개를 설명합니다.
7.  **우선순위 높은 추천 (priority_recommendations)**: 가장 시급하고 효과가 클 것으로 예상되는 개선 항목 3-5개를 'fail'된 항목과 AI의 추론적 평가를 바탕으로 제시합니다. 각 항목은 다음 JSON 형식을 따라야 합니다:
    - `priority`: "높음", "중간", "낮음"
    - `title`: 추천 항목의 제목
    - `description`: 구체적인 실행 방법
    - `category`: 관련 SEO 영역 (예: "콘텐츠", "사이트 구조")
    - `expected_impact`: 예상되는 긍정적 효과
8.  **예상 점수 향상 (expected_score_improvement)**: 추천 사항 적용 시 예상되는 전체 점수 향상 범위를 제시합니다.
9.  **예상 실행 기간 (implementation_timeline)**: 추천 사항 적용에 필요한 예상 기간을 제시합니다.

**최종 JSON 출력 형식:**
```json
{{
  "overall_summary": "...",
  "seo_level": "...",
  "strengths": ["...", "..."],
  "priority_recommendations": [
    {{
      "priority": "...", "title": "...", "description": "...", "category": "...", "expected_impact": "..."
    }}
  ],
  "technical_insights": ["...", "..."],
  "content_insights": ["...", "..."],
  "expected_score_improvement": "...",
  "implementation_timeline": "..."
}}
```
"""
        return prompt

    # (삭제) E-E-A-T 전용 프롬프트 생성자는 더 이상 사용하지 않습니다.

    async def analyze_web_performance(self, url: str, website_data: Dict) -> Dict:
        """웹 성능 AI 분석을 수행합니다."""
        import time
        
        start_time = time.time()
        print(f"🔍 웹 성능 AI 분석 시작: {url}")
        
        try:
            # AI 분석 수행
            ai_result = await self._analyze_web_performance_with_data(url, website_data)
            
            # 결과 검증 및 정규화
            validated_result = await self._validate_web_performance_response(ai_result, url)
            
            total_time = time.time() - start_time
            print(f"✅ 웹 성능 AI 분석 완료 (소요시간: {total_time:.2f}초)")
            return validated_result
            
        except Exception as e:
            print(f"❌ 웹 성능 AI 분석 오류: {e}")
            return self._get_default_web_performance_insights(str(e))

    async def analyze_url_content_standardization(self, url: str, website_data: Dict) -> Dict:
        """URL 및 콘텐츠 표준화 AI 분석을 수행합니다."""
        import time
        
        start_time = time.time()
        print(f"🔍 URL 콘텐츠 표준화 AI 분석 시작: {url}")
        
        try:
            # AI 분석 수행
            ai_result = await self._analyze_url_content_with_data(url, website_data)
            
            # 결과 검증 및 정규화
            validated_result = await self._validate_url_content_response(ai_result, url)
            
            total_time = time.time() - start_time
            print(f"✅ URL 콘텐츠 표준화 AI 분석 완료 (소요시간: {total_time:.2f}초)")
            return validated_result
            
        except Exception as e:
            print(f"❌ URL 콘텐츠 표준화 AI 분석 오류: {e}")
            return self._get_default_url_content_insights(str(e))

    async def _collect_website_data(self, url: str) -> Dict:
        """실제 웹사이트를 방문하여 E-E-A-T 분석에 필요한 데이터를 수집합니다."""
        import time
        
        try:
            print(f"🌐 웹사이트 데이터 수집 시작: {url}")
            
            async with httpx.AsyncClient(timeout=60.0, follow_redirects=True) as client:
                # 메인 페이지 수집
                main_start_time = time.time()
                print(f"  📄 메인 페이지 수집 중: {url}")
                main_response = await client.get(url)
                main_html = main_response.text
                main_time = time.time() - main_start_time
                print(f"    ✅ 메인 페이지 수집 완료 (상태: {main_response.status_code}, 크기: {len(main_html)} 문자, 소요시간: {main_time:.2f}초)")
                
                # 메인 페이지에서 주요 링크들 찾기
                link_extraction_start = time.time()
                print(f"  🔍 주요 페이지 링크 탐색 중...")
                important_links = self._extract_important_links(main_html, url)
                link_extraction_time = time.time() - link_extraction_start
                print(f"    📋 발견된 주요 링크: {len(important_links)}개")
                print(f"    ⏱️ 링크 추출 소요 시간: {link_extraction_time:.2f}초")
                for link in important_links[:10]:  # 처음 10개만 로그
                    print(f"      - {link}")
                
                # 발견된 주요 페이지들 수집
                pages_data = {}
                collected_count = 0
                max_pages = 10  # 최대 10개 페이지만 수집
                
                for page_url in important_links[:max_pages]:
                    try:
                        page_start_time = time.time()
                        print(f"  📄 페이지 수집 중 ({collected_count + 1}/{min(len(important_links), max_pages)}): {page_url}")
                        response = await client.get(page_url, timeout=30.0)
                        page_time = time.time() - page_start_time
                        
                        if response.status_code == 200:
                            pages_data[page_url] = response.text
                            collected_count += 1
                            print(f"    ✅ 성공 (크기: {len(response.text)} 문자, 소요시간: {page_time:.2f}초)")
                        else:
                            print(f"    ❌ 실패 (상태: {response.status_code}, 소요시간: {page_time:.2f}초)")
                    except Exception as e:
                        page_time = time.time() - page_start_time
                        print(f"    ❌ 오류: {e} (소요시간: {page_time:.2f}초)")
                
                # 데이터 구조화
                website_data = {
                    "main_page": {
                        "url": url,
                        "html": main_html,
                        "status_code": main_response.status_code,
                        "protocol": "https" if url.startswith("https") else "http",
                        "size": len(main_html)
                    },
                    "pages": pages_data,
                    "total_pages_found": len(important_links),
                    "pages_collected": collected_count,
                    "domain": url.split("//")[1].split("/")[0] if "//" in url else url
                }
                
                print(f"✅ 웹사이트 데이터 수집 완료")
                print(f"  📊 수집 통계:")
                print(f"    - 발견된 주요 페이지: {len(important_links)}개")
                print(f"    - 성공적으로 수집: {collected_count}개")
                print(f"    - 메인 페이지 크기: {len(main_html)} 문자")
                print(f"    - 총 수집 데이터 크기: {len(main_html) + sum(len(html) for html in pages_data.values())} 문자")
                
                return website_data
                
        except Exception as e:
            print(f"❌ 웹사이트 데이터 수집 실패: {e}")
            import traceback
            traceback.print_exc()
            return {"error": f"데이터 수집 실패: {str(e)}"}

    def _extract_important_links(self, html_content: str, base_url: str) -> List[str]:
        """HTML에서 E-E-A-T 분석에 중요한 페이지 링크들을 추출합니다."""
        try:
            soup = BeautifulSoup(html_content, 'html.parser')
            important_links = []
            
            # E-E-A-T 분석에 중요한 키워드들
            important_keywords = [
                # 저자/팀 관련
                'about', 'team', 'member', 'staff', 'profile', 'author', 'writer',
                '소개', '팀', '멤버', '스태프', '프로필', '저자', '작성자',
                
                # 회사 정보 관련
                'contact', 'company', 'info', '연락처', '회사', '정보',
                
                # 콘텐츠 관련
                'blog', 'news', 'insights', 'article', 'post', 'content',
                '블로그', '뉴스', '인사이트', '기사', '포스트', '콘텐츠',
                
                # 법적 문서 관련
                'privacy', 'terms', 'policy', 'legal', '개인정보', '약관', '정책', '법적',
                
                # 서비스 관련
                'service', 'portfolio', 'project', 'case', 'work',
                '서비스', '포트폴리오', '프로젝트', '사례', '작업',
                
                # 고객 관련
                'client', 'testimonial', 'review', 'feedback', '고객', '후기', '평가'
            ]
            
            # 모든 링크 찾기
            links = soup.find_all('a', href=True)
            print(f"    🔍 전체 링크 수: {len(links)}개")
            
            for link in links:
                href = link.get('href', '').strip()
                link_text = link.get_text(strip=True).lower()
                
                # 상대 URL을 절대 URL로 변환
                if href.startswith('/'):
                    full_url = urllib.parse.urljoin(base_url, href)
                elif href.startswith('http'):
                    # 같은 도메인인지 확인
                    try:
                        link_domain = urllib.parse.urlparse(href).netloc
                        base_domain = urllib.parse.urlparse(base_url).netloc
                        if link_domain != base_domain:
                            continue
                        full_url = href
                    except:
                        continue
                else:
                    continue
                
                # 중요도 점수 계산
                importance_score = 0
                
                # URL 경로에서 키워드 확인
                url_lower = full_url.lower()
                for keyword in important_keywords:
                    if keyword in url_lower:
                        importance_score += 1
                
                # 링크 텍스트에서 키워드 확인
                for keyword in important_keywords:
                    if keyword in link_text:
                        importance_score += 2  # 텍스트가 더 중요
                
                # 특별히 중요한 페이지들
                if any(keyword in url_lower for keyword in ['about', 'contact', 'team', '소개', '연락처', '팀']):
                    importance_score += 3
                
                # 점수가 높은 링크만 추가
                if importance_score >= 1 and full_url not in important_links:
                    important_links.append(full_url)
            
            # 중요도 순으로 정렬 (중복 제거)
            important_links = list(dict.fromkeys(important_links))  # 순서 유지하면서 중복 제거
            
            print(f"    📊 중요 링크 분석 결과:")
            print(f"      - 중요도 1점 이상: {len(important_links)}개")
            
            return important_links[:20]  # 최대 20개만 반환
            
        except Exception as e:
            print(f"    ❌ 링크 추출 중 오류: {e}")
            return []

    # (삭제) 규칙 기반 E-E-A-T 분석 로직 제거
    async def _analyze_eeat_with_data(self, url: str, website_data: Dict) -> Dict:
        """수집된 웹사이트 데이터를 기반으로 E-E-A-T 분석을 수행합니다."""
        import time
        
        try:
            print(f"🔍 E-E-A-T 데이터 분석 시작")
            print(f"  📊 분석 대상:")
            print(f"    - 메인 페이지 크기: {website_data['main_page']['size']} 문자")
            print(f"    - 수집된 페이지 수: {website_data['pages_collected']}개")
            print(f"    - 총 데이터 크기: {website_data['main_page']['size'] + sum(len(html) for html in website_data['pages'].values())} 문자")
            
            # 각 E-E-A-T 항목별 분석을 순차적으로 수행
            print(f"  📋 개별 항목 분석 시작...")
            
            print(f"    1️⃣ 저자 프로필 분석 중...")
            item_start = time.time()
            author_profile = await self._analyze_author_profile(website_data)
            item_time = time.time() - item_start
            print(f"       ✅ 완료: {author_profile['score']:.2f}점 ({author_profile['status']}) - {item_time:.2f}초")
            
            print(f"    2️⃣ 자격 증명 분석 중...")
            item_start = time.time()
            credentials = await self._analyze_credentials(website_data)
            item_time = time.time() - item_start
            print(f"       ✅ 완료: {credentials['score']:.2f}점 ({credentials['status']}) - {item_time:.2f}초")
            
            print(f"    3️⃣ 외부 리뷰 분석 중...")
            item_start = time.time()
            external_reviews = await self._analyze_external_reviews(website_data)
            item_time = time.time() - item_start
            print(f"       ✅ 완료: {external_reviews['score']:.2f}점 ({external_reviews['status']}) - {item_time:.2f}초")
            
            print(f"    4️⃣ 신뢰성 있는 출처 분석 중...")
            item_start = time.time()
            reliable_sources = await self._analyze_reliable_sources(website_data)
            item_time = time.time() - item_start
            print(f"       ✅ 완료: {reliable_sources['score']:.2f}점 ({reliable_sources['status']}) - {item_time:.2f}초")
            
            print(f"    5️⃣ 브랜드 존재감 분석 중...")
            item_start = time.time()
            brand_presence = await self._analyze_brand_presence(website_data)
            item_time = time.time() - item_start
            print(f"       ✅ 완료: {brand_presence['score']:.2f}점 ({brand_presence['status']}) - {item_time:.2f}초")
            
            print(f"    6️⃣ 보안 배지 분석 중...")
            item_start = time.time()
            security_badges = await self._analyze_security_badges(website_data)
            item_time = time.time() - item_start
            print(f"       ✅ 완료: {security_badges['score']:.2f}점 ({security_badges['status']}) - {item_time:.2f}초")
            
            print(f"    7️⃣ 프라이버시 정책 분석 중...")
            item_start = time.time()
            privacy_policy = await self._analyze_privacy_policy(website_data)
            item_time = time.time() - item_start
            print(f"       ✅ 완료: {privacy_policy['score']:.2f}점 ({privacy_policy['status']}) - {item_time:.2f}초")
            
            print(f"    8️⃣ 회사 정보 분석 중...")
            item_start = time.time()
            company_info = await self._analyze_company_info(website_data)
            item_time = time.time() - item_start
            print(f"       ✅ 완료: {company_info['score']:.2f}점 ({company_info['status']}) - {item_time:.2f}초")
            
            # 종합 점수 계산
            scores = [
                author_profile["score"],
                credentials["score"],
                external_reviews["score"],
                reliable_sources["score"],
                brand_presence["score"],
                security_badges["score"],
                privacy_policy["score"],
                company_info["score"]
            ]
            
            eeat_score = sum(scores) / len(scores)
            eeat_percentage = eeat_score * 100
            
            print(f"  📊 종합 점수 계산:")
            print(f"    - 개별 점수: {[f'{s:.2f}' for s in scores]}")
            print(f"    - 평균 점수: {eeat_score:.2f}")
            print(f"    - 백분율: {eeat_percentage:.1f}%")
            
            # 강점과 약점 분석
            strengths = []
            weaknesses = []
            urgent_improvements = []
            
            items = [
                ("저자 프로필", author_profile),
                ("자격 증명", credentials),
                ("외부 리뷰", external_reviews),
                ("신뢰성 있는 출처", reliable_sources),
                ("브랜드 존재감", brand_presence),
                ("보안 배지", security_badges),
                ("프라이버시 정책", privacy_policy),
                ("회사 정보", company_info)
            ]
            
            for name, item in items:
                if item["score"] >= 0.7:
                    strengths.append(f"{name}: {item['status']}")
                elif item["score"] <= 0.3:
                    weaknesses.append(f"{name}: {item['status']}")
                    if item["score"] <= 0.2:
                        urgent_improvements.append(f"{name} 개선 필요")
            
            print(f"  💪 강점 분석:")
            for strength in strengths:
                print(f"    ✅ {strength}")
            
            print(f"  ⚠️ 약점 분석:")
            for weakness in weaknesses:
                print(f"    ❌ {weakness}")
            
            print(f"  🚨 긴급 개선 필요:")
            for improvement in urgent_improvements:
                print(f"    🔥 {improvement}")
            
            # 우선순위 권장사항 생성
            priority_recommendations = []
            for name, item in items:
                if item["score"] <= 0.3:
                    priority_recommendations.append({
                        "priority": "높음" if item["score"] <= 0.2 else "중간",
                        "title": f"{name} 개선",
                        "description": f"{name} 항목의 점수가 낮습니다 ({item['score']:.2f}점). {', '.join(item.get('improvements', []))}",
                        "category": "E-E-A-T",
                        "expected_impact": "신뢰성 및 권위성 향상"
                    })
            
            # 업계 벤치마킹
            industry_benchmark = "업계 평균"
            
            return {
                "eeat_score": eeat_score,
                "eeat_percentage": eeat_percentage,
                "author_profile": author_profile,
                "credentials": credentials,
                "external_reviews": external_reviews,
                "reliable_sources": reliable_sources,
                "brand_presence": brand_presence,
                "security_badges": security_badges,
                "privacy_policy": privacy_policy,
                "company_info": company_info,
                "strengths": strengths,
                "weaknesses": weaknesses,
                "urgent_improvements": urgent_improvements,
                "priority_recommendations": priority_recommendations,
                "industry_benchmark": industry_benchmark
            }
            
        except Exception as e:
            print(f"❌ E-E-A-T 분석 중 오류: {e}")
            import traceback
            traceback.print_exc()
            return {
                "error": f"E-E-A-T 분석 중 오류 발생: {str(e)}",
                "eeat_score": 0.0,
                "eeat_percentage": 0.0,
                "author_profile": {"score": 0.0, "status": "오류", "evidence": [], "improvements": []},
                "credentials": {"score": 0.0, "status": "오류", "evidence": [], "improvements": []},
                "external_reviews": {"score": 0.0, "status": "오류", "evidence": [], "improvements": []},
                "reliable_sources": {"score": 0.0, "status": "오류", "evidence": [], "improvements": []},
                "brand_presence": {"score": 0.0, "status": "오류", "evidence": [], "improvements": []},
                "security_badges": {"score": 0.0, "status": "오류", "evidence": [], "improvements": []},
                "privacy_policy": {"score": 0.0, "status": "오류", "evidence": [], "improvements": []},
                "company_info": {"score": 0.0, "status": "오류", "evidence": [], "improvements": []},
                "strengths": [],
                "weaknesses": ["분석 중 오류 발생"],
                "urgent_improvements": ["분석 재시도 필요"],
                "priority_recommendations": [],
                "industry_benchmark": "분석 불가"
            }

    # (삭제) E-E-A-T 세부 항목 분석 함수들 제거
    async def _analyze_author_profile(self, data: Dict) -> Dict:
        """저자 프로필 분석"""
        try:
            html_content = data["main_page"]["html"].lower()
            pages_html = " ".join(data["pages"].values()).lower()
            all_content = html_content + " " + pages_html
            
            print(f"        🔍 저자 프로필 분석 세부사항:")
            
            score = 0
            evidence = []
            improvements = []
            
            # 1. 개별 저자/전문가 정보 확인
            author_indicators = ["author", "writer", "contributor", "team", "member", "전문가", "저자", "팀원"]
            has_author_info = any(indicator in all_content for indicator in author_indicators)
            if has_author_info:
                score += 0.2
                evidence.append("개별 저자/전문가 정보 발견")
                print(f"          ✅ 개별 저자/전문가 정보: 발견됨")
            else:
                improvements.append("개별 전문가 프로필 페이지 추가")
                print(f"          ❌ 개별 저자/전문가 정보: 발견되지 않음")
            
            # 2. 프로필 사진 확인
            if "profile" in all_content or "photo" in all_content or "image" in all_content:
                score += 0.2
                evidence.append("프로필 사진 관련 요소 발견")
                print(f"          ✅ 프로필 사진: 발견됨")
            else:
                improvements.append("전문가별 프로필 사진 추가")
                print(f"          ❌ 프로필 사진: 발견되지 않음")
            
            # 3. 약력/경력 정보 확인
            bio_indicators = ["about", "bio", "career", "experience", "약력", "경력", "소개"]
            has_bio = any(indicator in all_content for indicator in bio_indicators)
            if has_bio:
                score += 0.2
                evidence.append("약력/경력 정보 발견")
                print(f"          ✅ 약력/경력 정보: 발견됨")
            else:
                improvements.append("전문가별 상세 약력 정보 추가")
                print(f"          ❌ 약력/경력 정보: 발견되지 않음")
            
            # 4. 자격증/학력 정보 확인
            qualification_indicators = ["certificate", "degree", "education", "자격증", "학력", "학위"]
            has_qualification = any(indicator in all_content for indicator in qualification_indicators)
            if has_qualification:
                score += 0.2
                evidence.append("자격증/학력 정보 발견")
                print(f"          ✅ 자격증/학력 정보: 발견됨")
            else:
                improvements.append("전문가별 자격증 및 학력 정보 추가")
                print(f"          ❌ 자격증/학력 정보: 발견되지 않음")
            
            # 5. 연락처 정보 확인
            contact_indicators = ["contact", "email", "phone", "연락처", "이메일", "전화"]
            has_contact = any(indicator in all_content for indicator in contact_indicators)
            if has_contact:
                score += 0.2
                evidence.append("연락처 정보 발견")
                print(f"          ✅ 연락처 정보: 발견됨")
            else:
                improvements.append("전문가별 연락처 정보 추가")
                print(f"          ❌ 연락처 정보: 발견되지 않음")
            
            status = "충족" if score >= 0.8 else "부분충족" if score >= 0.4 else "미흡"
            
            return {
                "score": round(score, 2),
                "status": status,
                "evidence": "; ".join(evidence) if evidence else "저자 프로필 관련 정보 부족",
                "improvements": improvements
            }
            
        except Exception as e:
            print(f"          ❌ 저자 프로필 분석 오류: {e}")
            return {"score": 0, "status": "미흡", "evidence": f"분석 오류: {e}", "improvements": ["분석 시스템 개선 필요"]}

    async def _analyze_credentials(self, data: Dict) -> Dict:
        """자격 증명 유효성 분석"""
        try:
            html_content = data["main_page"]["html"].lower()
            pages_html = " ".join(data["pages"].values()).lower()
            all_content = html_content + " " + pages_html
            
            score = 0
            evidence = []
            improvements = []
            
            # 1. 회사 경력 정보 확인
            if "20+" in all_content or "years" in all_content or "년" in all_content:
                score += 0.3
                evidence.append("장기 사업 경력 확인")
            else:
                improvements.append("회사 설립년도 및 사업 이력 명시")
            
            # 2. 수상 이력 확인
            award_indicators = ["award", "prize", "winner", "수상", "어워드", "red dot", "if", "idea"]
            has_awards = any(indicator in all_content for indicator in award_indicators)
            if has_awards:
                score += 0.3
                evidence.append("수상 이력 발견")
            else:
                improvements.append("업계 수상 이력 및 인증서 추가")
            
            # 3. 클라이언트 포트폴리오 확인
            client_indicators = ["client", "portfolio", "project", "case", "클라이언트", "프로젝트"]
            has_clients = any(indicator in all_content for indicator in client_indicators)
            if has_clients:
                score += 0.2
                evidence.append("클라이언트 포트폴리오 발견")
            else:
                improvements.append("주요 클라이언트 및 프로젝트 사례 추가")
            
            # 4. 공식 인증 확인
            certification_indicators = ["certified", "accredited", "licensed", "인증", "승인"]
            has_certification = any(indicator in all_content for indicator in certification_indicators)
            if has_certification:
                score += 0.2
                evidence.append("공식 인증 정보 발견")
            else:
                improvements.append("업계 인증서 및 자격 증명 추가")
            
            status = "충족" if score >= 0.8 else "부분충족" if score >= 0.4 else "미흡"
            
            return {
                "score": round(score, 2),
                "status": status,
                "evidence": "; ".join(evidence) if evidence else "자격 증명 관련 정보 부족",
                "improvements": improvements
            }
            
        except Exception as e:
            return {"score": 0, "status": "미흡", "evidence": f"분석 오류: {e}", "improvements": ["분석 시스템 개선 필요"]}

    async def _analyze_external_reviews(self, data: Dict) -> Dict:
        """외부 리뷰 평점 분석"""
        try:
            html_content = data["main_page"]["html"].lower()
            pages_html = " ".join(data["pages"].values()).lower()
            all_content = html_content + " " + pages_html
            
            score = 0
            evidence = []
            improvements = []
            
            # 1. Google 리뷰 시스템 확인
            if "google" in all_content and "review" in all_content:
                score += 0.3
                evidence.append("Google 리뷰 시스템 발견")
            else:
                improvements.append("Google 리뷰 위젯 추가")
            
            # 2. 클라이언트 후기 섹션 확인
            review_indicators = ["review", "testimonial", "feedback", "후기", "평가", "추천"]
            has_reviews = any(indicator in all_content for indicator in review_indicators)
            if has_reviews:
                score += 0.3
                evidence.append("클라이언트 후기 섹션 발견")
            else:
                improvements.append("고객 후기 및 추천서 섹션 추가")
            
            # 3. 평점 표시 시스템 확인
            rating_indicators = ["rating", "star", "score", "평점", "별점"]
            has_rating = any(indicator in all_content for indicator in rating_indicators)
            if has_rating:
                score += 0.2
                evidence.append("평점 표시 시스템 발견")
            else:
                improvements.append("평점 및 평가 시스템 추가")
            
            # 4. 외부 리뷰 플랫폼 연동 확인
            platform_indicators = ["clutch", "g2", "trustpilot", "yelp"]
            has_platforms = any(indicator in all_content for indicator in platform_indicators)
            if has_platforms:
                score += 0.2
                evidence.append("외부 리뷰 플랫폼 연동 발견")
            else:
                improvements.append("업계 리뷰 플랫폼 연동 추가")
            
            status = "충족" if score >= 0.8 else "부분충족" if score >= 0.4 else "미흡"
            
            return {
                "score": round(score, 2),
                "status": status,
                "evidence": "; ".join(evidence) if evidence else "외부 리뷰 시스템 부족",
                "improvements": improvements
            }
            
        except Exception as e:
            return {"score": 0, "status": "미흡", "evidence": f"분석 오류: {e}", "improvements": ["분석 시스템 개선 필요"]}

    async def _analyze_reliable_sources(self, data: Dict) -> Dict:
        """신뢰성 있는 출처 인용 분석"""
        try:
            html_content = data["main_page"]["html"].lower()
            pages_html = " ".join(data["pages"].values()).lower()
            all_content = html_content + " " + pages_html
            
            score = 0
            evidence = []
            improvements = []
            
            # 1. 정부/공공기관 데이터 인용 확인
            gov_indicators = [".gov", ".ac.kr", ".edu", ".org", "정부", "공공기관"]
            has_gov_sources = any(indicator in all_content for indicator in gov_indicators)
            if has_gov_sources:
                score += 0.3
                evidence.append("정부/공공기관 데이터 인용 발견")
            else:
                improvements.append("정부/공공기관 데이터 인용 추가")
            
            # 2. 학술 자료 참조 확인
            academic_indicators = ["research", "study", "paper", "journal", "학술", "연구", "논문"]
            has_academic = any(indicator in all_content for indicator in academic_indicators)
            if has_academic:
                score += 0.3
                evidence.append("학술 자료 참조 발견")
            else:
                improvements.append("학술 연구 자료 참조 추가")
            
            # 3. 참고문헌 섹션 확인
            reference_indicators = ["reference", "bibliography", "source", "참고문헌", "출처"]
            has_references = any(indicator in all_content for indicator in reference_indicators)
            if has_references:
                score += 0.2
                evidence.append("참고문헌 섹션 발견")
            else:
                improvements.append("참고문헌 및 출처 명시 추가")
            
            # 4. 통계 데이터 출처 확인
            stats_indicators = ["statistics", "data", "survey", "통계", "데이터", "조사"]
            has_stats = any(indicator in all_content for indicator in stats_indicators)
            if has_stats:
                score += 0.2
                evidence.append("통계 데이터 출처 발견")
            else:
                improvements.append("통계 데이터의 출처 투명성 확보")
            
            status = "충족" if score >= 0.8 else "부분충족" if score >= 0.4 else "미흡"
            
            return {
                "score": round(score, 2),
                "status": status,
                "evidence": "; ".join(evidence) if evidence else "신뢰성 있는 출처 인용 부족",
                "improvements": improvements
            }
            
        except Exception as e:
            return {"score": 0, "status": "미흡", "evidence": f"분석 오류: {e}", "improvements": ["분석 시스템 개선 필요"]}

    async def _analyze_brand_presence(self, data: Dict) -> Dict:
        """브랜드 검색량 추이 분석"""
        try:
            html_content = data["main_page"]["html"].lower()
            pages_html = " ".join(data["pages"].values()).lower()
            all_content = html_content + " " + pages_html
            
            score = 0
            evidence = []
            improvements = []
            
            # 1. 사업 이력 확인
            if "20+" in all_content or "years" in all_content or "년" in all_content:
                score += 0.3
                evidence.append("장기 사업 이력 확인")
            else:
                improvements.append("회사 설립년도 및 사업 이력 명시")
            
            # 2. 지속적인 콘텐츠 발행 확인
            content_indicators = ["blog", "news", "insights", "article", "블로그", "뉴스", "인사이트"]
            has_content = any(indicator in all_content for indicator in content_indicators)
            if has_content:
                score += 0.3
                evidence.append("지속적인 콘텐츠 발행 확인")
            else:
                improvements.append("정기적인 콘텐츠 발행 체계 구축")
            
            # 3. 다중 채널 운영 확인
            social_indicators = ["instagram", "linkedin", "twitter", "facebook", "youtube"]
            has_social = any(indicator in all_content for indicator in social_indicators)
            if has_social:
                score += 0.2
                evidence.append("다중 소셜미디어 채널 운영")
            else:
                improvements.append("소셜미디어 채널 확장")
            
            # 4. 업계 내 인지도 확인
            industry_indicators = ["industry", "market", "sector", "업계", "시장", "분야"]
            has_industry = any(indicator in all_content for indicator in industry_indicators)
            if has_industry:
                score += 0.2
                evidence.append("업계 내 인지도 확인")
            else:
                improvements.append("업계 내 포지셔닝 강화")
            
            status = "충족" if score >= 0.8 else "부분충족" if score >= 0.4 else "미흡"
            
            return {
                "score": round(score, 2),
                "status": status,
                "evidence": "; ".join(evidence) if evidence else "브랜드 존재감 부족",
                "improvements": improvements
            }
            
        except Exception as e:
            return {"score": 0, "status": "미흡", "evidence": f"분석 오류: {e}", "improvements": ["분석 시스템 개선 필요"]}

    async def _analyze_security_badges(self, data: Dict) -> Dict:
        """HTTPS 및 보안배지 분석"""
        try:
            protocol = data["main_page"]["protocol"]
            html_content = data["main_page"]["html"].lower()
            
            score = 0
            evidence = []
            improvements = []
            
            # 1. HTTPS 프로토콜 확인
            if protocol == "https":
                score += 0.5
                evidence.append("HTTPS 프로토콜 적용됨")
            else:
                improvements.append("HTTPS 프로토콜 적용 필요")
            
            # 2. SSL 인증서 확인
            if protocol == "https":
                score += 0.3
                evidence.append("SSL 인증서 적용됨")
            else:
                improvements.append("SSL 인증서 설치 필요")
            
            # 3. 보안 배지 확인
            security_indicators = ["security", "ssl", "certificate", "보안", "인증"]
            has_security_badges = any(indicator in html_content for indicator in security_indicators)
            if has_security_badges:
                score += 0.2
                evidence.append("보안 배지 발견")
            else:
                improvements.append("보안 인증 배지 추가")
            
            status = "충족" if score >= 0.8 else "부분충족" if score >= 0.4 else "미흡"
            
            return {
                "score": round(score, 2),
                "status": status,
                "evidence": "; ".join(evidence) if evidence else "보안 설정 부족",
                "improvements": improvements
            }
            
        except Exception as e:
            return {"score": 0, "status": "미흡", "evidence": f"분석 오류: {e}", "improvements": ["분석 시스템 개선 필요"]}

    async def _analyze_privacy_policy(self, data: Dict) -> Dict:
        """프라이버시 정책 분석"""
        try:
            html_content = data["main_page"]["html"].lower()
            pages_html = " ".join(data["pages"].values()).lower()
            all_content = html_content + " " + pages_html
            
            score = 0
            evidence = []
            improvements = []
            
            # 1. 개인정보처리방침 확인
            privacy_indicators = ["privacy", "개인정보", "개인정보처리방침"]
            has_privacy = any(indicator in all_content for indicator in privacy_indicators)
            if has_privacy:
                score += 0.4
                evidence.append("개인정보처리방침 발견")
            else:
                improvements.append("개인정보처리방침 페이지 추가")
            
            # 2. 이용약관 확인
            terms_indicators = ["terms", "conditions", "이용약관", "약관"]
            has_terms = any(indicator in all_content for indicator in terms_indicators)
            if has_terms:
                score += 0.3
                evidence.append("이용약관 발견")
            else:
                improvements.append("이용약관 페이지 추가")
            
            # 3. 쿠키 정책 확인
            cookie_indicators = ["cookie", "쿠키"]
            has_cookies = any(indicator in all_content for indicator in cookie_indicators)
            if has_cookies:
                score += 0.3
                evidence.append("쿠키 정책 발견")
            else:
                improvements.append("쿠키 정책 페이지 추가")
            
            status = "충족" if score >= 0.8 else "부분충족" if score >= 0.4 else "미흡"
            
            return {
                "score": round(score, 2),
                "status": status,
                "evidence": "; ".join(evidence) if evidence else "프라이버시 정책 부족",
                "improvements": improvements
            }
            
        except Exception as e:
            return {"score": 0, "status": "미흡", "evidence": f"분석 오류: {e}", "improvements": ["분석 시스템 개선 필요"]}

    async def _analyze_company_info(self, data: Dict) -> Dict:
        """회사 정보 및 연락처 분석"""
        try:
            html_content = data["main_page"]["html"].lower()
            pages_html = " ".join(data["pages"].values()).lower()
            all_content = html_content + " " + pages_html
            
            score = 0
            evidence = []
            improvements = []
            
            # 1. 회사명 확인
            company_indicators = ["company", "corp", "inc", "ltd", "회사", "기업"]
            has_company_name = any(indicator in all_content for indicator in company_indicators)
            if has_company_name:
                score += 0.2
                evidence.append("회사명 확인")
            else:
                improvements.append("회사명 명확히 표시")
            
            # 2. 주소 정보 확인
            address_indicators = ["address", "location", "주소", "위치"]
            has_address = any(indicator in all_content for indicator in address_indicators)
            if has_address:
                score += 0.2
                evidence.append("주소 정보 확인")
            else:
                improvements.append("정확한 주소 정보 추가")
            
            # 3. 전화번호 확인
            phone_pattern = r'\d{2,3}-\d{3,4}-\d{4}'
            if re.search(phone_pattern, all_content):
                score += 0.2
                evidence.append("전화번호 확인")
            else:
                improvements.append("전화번호 정보 추가")
            
            # 4. 이메일 확인
            email_pattern = r'[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}'
            if re.search(email_pattern, all_content):
                score += 0.2
                evidence.append("이메일 주소 확인")
            else:
                improvements.append("이메일 주소 추가")
            
            # 5. 사업자등록번호 확인
            business_pattern = r'\d{3}-\d{2}-\d{5}'
            if re.search(business_pattern, all_content):
                score += 0.2
                evidence.append("사업자등록번호 확인")
            else:
                improvements.append("사업자등록번호 추가")
            
            status = "충족" if score >= 0.8 else "부분충족" if score >= 0.4 else "미흡"
            
            return {
                "score": round(score, 2),
                "status": status,
                "evidence": "; ".join(evidence) if evidence else "회사 정보 부족",
                "improvements": improvements
            }
            
        except Exception as e:
            return {"score": 0, "status": "미흡", "evidence": f"분석 오류: {e}", "improvements": ["분석 시스템 개선 필요"]}

    # (삭제) 업계 벤치마크 평가 제거

    # (삭제) E-E-A-T 응답 검증 로직 제거
    async def _validate_ai_response(self, ai_result: Dict, original_url: str) -> Dict:
        """AI 응답에서 언급된 URL들을 실제로 검증합니다."""
        try:
            # AI 응답에서 URL 패턴 찾기
            import re
            url_pattern = r'https?://[^\s<>"{}|\\^`\[\]]+'
            
            # 전체 응답을 문자열로 변환
            response_text = json.dumps(ai_result, ensure_ascii=False)
            mentioned_urls = re.findall(url_pattern, response_text)
            
            print(f"🔍 발견된 URL들: {mentioned_urls}")
            
            # 원본 URL 제외하고 검증
            urls_to_validate = [url for url in mentioned_urls if url != original_url]
            
            if urls_to_validate:
                print(f"🔍 {len(urls_to_validate)}개 URL 검증 시작...")
                
                # URL 검증 결과를 저장할 딕셔너리
                url_validation = {}
                
                async with httpx.AsyncClient(timeout=30.0) as client:
                    for url in urls_to_validate:
                        try:
                            print(f"  🔍 검증 중: {url}")
                            response = await client.head(url, follow_redirects=True)
                            url_validation[url] = {
                                "exists": response.status_code < 400,
                                "status_code": response.status_code
                            }
                            print(f"    ✅ {url}: {response.status_code}")
                        except Exception as e:
                            url_validation[url] = {
                                "exists": False,
                                "error": str(e)
                            }
                            print(f"    ❌ {url}: 접근 불가 - {e}")
                
                # 검증 결과를 AI 결과에 추가
                ai_result["url_validation"] = url_validation
                
                # 존재하지 않는 URL이 있다면 경고 추가
                invalid_urls = [url for url, result in url_validation.items() if not result.get("exists", False)]
                if invalid_urls:
                    print(f"⚠️ 존재하지 않는 URL 발견: {invalid_urls}")
                    ai_result["validation_warnings"] = [
                        f"AI가 언급한 다음 URL들은 실제로 존재하지 않습니다: {', '.join(invalid_urls)}"
                    ]
                else:
                    print(f"✅ 모든 언급된 URL이 유효합니다")
            else:
                print(f"🔍 검증할 URL이 없습니다")
            
            return ai_result
            
        except Exception as e:
            print(f"❌ URL 검증 중 오류: {e}")
            return ai_result

    def _create_item_specific_prompt(self, failed_checks: list) -> str:
        """실패한 개별 항목들에 대한 구체적인 해결책을 요청하는 프롬프트를 생성합니다."""
        items_str = ""
        for check in failed_checks:
            check_id = check.get('id')
            check_title = check.get('title') or check.get('suggestion') or "알 수 없는 항목"
            check_message = check.get('message') or "상세 메시지 없음"
            items_str += f"- ID: {check_id}, 항목: {check_title}\n  결과: {check_message}\n"

        prompt = f"""
당신은 세계 최고 수준의 SEO 컨설턴트입니다. 다음은 SEO 분석에서 '실패' 또는 '주의' 판정을 받은 항목들의 목록입니다. 각 항목에 대해, 초보자도 이해하고 따라 할 수 있도록 구체적인 해결 방안과 예상 효과를 제안해주십시오.

**분석 결과:**
{items_str}

**참고사항:**
구조화 데이터 관련 항목의 경우, JSON-LD 형식으로 필수 필드(headline, author, datePublished 등)를 포함하여 제안해주십시오.

**요청 사항:**
각 항목에 대한 해결책을 JSON 형식의 리스트로 반환해주십시오. 각 JSON 객체는 다음 네 개의 키를 가져야 합니다:
- `id`: 위에서 제공된 ID를 그대로 사용
- `title`: 위에서 제공된 항목명을 **정확히 그대로** 사용 (임의로 변경하지 마세요)
- `suggestion`: 구체적인 해결 방안 (1-2문장)
- `expected_impact`: 예상되는 구체적인 효과 (예: "검색 결과 노출 개선 및 클릭률 증가", "페이지 로딩 속도 향상 및 사용자 경험 개선" 등)

**출력 예시:**
```json
[
  {{
    "id": 5,
    "title": "Self-canonical 설정",
    "suggestion": "페이지 <head> 섹션에 <link rel='canonical' href='현재페이지URL' /> 태그를 추가하여 중복 콘텐츠 문제를 해결하세요.",
    "expected_impact": "검색 엔진 최적화 및 순위 향상"
  }},
  {{
    "id": 8,
    "title": "robots.txt 접근 가능",
    "suggestion": "robots.txt 파일이 검색엔진에 의해 접근 가능하도록, 서버 루트 디렉토리에 파일을 업로드하고 권한을 확인하십시오.",
    "expected_impact": "검색 엔진 크롤링 효율성 증대 및 색인 개선"
  }}
]
```

**중요:** 
- 각 제안의 `id`와 `title`은 반드시 위에서 제공된 것과 정확히 일치해야 합니다.
- **절대적으로 중요**: `title`을 임의로 변경하거나 새로 만들지 마세요. 위에서 제공된 "항목" 이름을 정확히 그대로 사용하세요.
- **중요**: 각 ID에 대해 하나의 제안만 생성하세요. 중복된 ID의 제안을 만들지 마세요.
- **절대 중복 금지**: 같은 ID나 비슷한 내용의 제안을 여러 번 생성하지 마세요.
- `expected_impact`는 구체적이고 실용적인 효과를 명시해야 합니다 (예: "검색 결과 클릭률 증가", "페이지 로딩 속도 향상", "모바일 사용자 경험 개선" 등).
"""
        return prompt

    async def get_suggestions_for_failed_checks(self, failed_checks: list) -> list:
        """실패한 항목 목록을 받아 AI에게 해결책을 요청하고, 그 결과를 반환합니다."""
        if not failed_checks:
            return []
        
        # 실패한 항목이 너무 많으면 상위 5개만 처리 (속도 개선)
        limited_checks = failed_checks[:5] if len(failed_checks) > 5 else failed_checks
        
        prompt = self._create_item_specific_prompt(limited_checks)
        try:
            response = await self._make_api_call_with_retry(prompt)
            suggestions = self._parse_suggestions_response(response.text, limited_checks)
            return suggestions
        except Exception as e:
            print(f"개별 항목 AI 제안 생성 중 오류: {e}")
            return self._get_default_suggestions(limited_checks)

    async def combine_analysis_with_ai_insights(self, analysis_result: Dict) -> Dict:
        prompt = self._create_prompt(analysis_result)
        try:
            response = await self._make_api_call_with_retry(prompt)
            ai_insights = self._parse_main_report_response(response.text)
        except Exception as e:
            print(f"AI 분석 중 오류 발생: {e}")
            ai_insights = self._get_default_insights()

        analysis_result["ai_analysis"] = ai_insights
        return analysis_result

    def _parse_main_report_response(self, response_text: str) -> Dict:
        """AI가 생성한 메인 분석 보고서(JSON 객체)를 파싱합니다."""
        try:
            json_match = re.search(r"```json\s*([\s\S]*?)\s*```", response_text)
            if json_match:
                json_content = json_match.group(1)
            else:
                json_content = response_text
            
            parsed_data = json.loads(json_content)

            if not isinstance(parsed_data, dict):
                 print(f"AI 메인 리포트가 딕셔너리 형식이 아님: {parsed_data}")
                 return self._get_default_insights(error="AI response was not a dictionary.")

            return self._normalize_ai_insights(parsed_data)
        except json.JSONDecodeError as e:
            print(f"AI 응답 JSON 파싱 실패: {e}")
            return self._get_default_insights(error=str(e))

    def _parse_suggestions_response(self, response_text: str, failed_checks: list) -> list:
        """AI가 생성한 항목별 제안(JSON 리스트)을 파싱합니다."""
        try:
            json_match = re.search(r"```json\s*([\s\S]*?)\s*```", response_text)
            if json_match:
                json_content = json_match.group(1).strip()
            else:
                json_content = response_text.strip()
            
            # 응답이 비어있는 경우 처리
            if not json_content:
                return []

            parsed_data = json.loads(json_content)

            if isinstance(parsed_data, list):
                # 각 항목의 유효성 검사 및 ID 안전 변환
                valid_suggestions = []
                # 실패한 항목들을 AI용 ID로 인덱싱 (섹션/원본 ID 컨텍스트 포함)
                failed_checks_index = {check['id']: check for check in failed_checks}
                
                for item in parsed_data:
                    if isinstance(item, dict) and 'id' in item and 'suggestion' in item:
                        try:
                            ai_id_raw = item['id']
                            try:
                                ai_id = int(ai_id_raw)
                            except (ValueError, TypeError):
                                ai_id = str(ai_id_raw)
                            
                            failed_check = failed_checks_index.get(ai_id)
                            if not failed_check:
                                # 숫자/문자열 혼용 가능성 대비
                                fc = None
                                if isinstance(ai_id_raw, str) and ai_id_raw.isdigit():
                                    fc = failed_checks_index.get(int(ai_id_raw))
                                elif isinstance(ai_id_raw, int):
                                    fc = failed_checks_index.get(str(ai_id_raw))
                                failed_check = fc
                            
                            if not failed_check:
                                print(f"⚠️ AI 응답 ID를 원본 체크와 매칭하지 못함: {ai_id_raw}")
                                continue
                            
                            actual_title = failed_check.get('title', '알 수 없는 항목')
                            section_id = failed_check.get('section_id')
                            original_check_id = failed_check.get('original_check_id', failed_check.get('id'))
                            
                            suggestion_data = {
                                'id': original_check_id,           # 프론트 매핑용 원본 체크 ID
                                'section_id': section_id,           # 섹션 컨텍스트
                                'title': actual_title,
                                'suggestion': str(item['suggestion']),
                                'ai_id': ai_id                      # 내부 추적용
                            }
                            
                            if 'title' in item and str(item['title']).strip() != actual_title:
                                print(f"⚠️ AI 제목 불일치 수정: '{item['title']}' → '{actual_title}'")
                            
                            if 'expected_impact' in item:
                                suggestion_data['expected_impact'] = str(item['expected_impact'])
                            else:
                                suggestion_data['expected_impact'] = "SEO 개선 효과 예상"
                            
                            valid_suggestions.append(suggestion_data)
                        except Exception:
                            print(f"잘못된 ID 형식 또는 매핑 실패: {item.get('id')}")
                            continue
                
                # 중복 제거: 같은 섹션/원본ID와 내용의 제안이 여러 개 있으면 첫 번째 것만 유지
                unique_suggestions = []
                seen_keys = set()
                
                for suggestion in valid_suggestions:
                    suggestion_content = suggestion.get('suggestion', '').strip()
                    section_id = suggestion.get('section_id')
                    original_id = suggestion.get('id')
                    content_key = f"{section_id}:{original_id}:{suggestion_content}"
                    
                    if content_key not in seen_keys:
                        unique_suggestions.append(suggestion)
                        seen_keys.add(content_key)
                    else:
                        print(f"중복 제안 제거: Section {section_id}, ID {original_id}, 내용: {suggestion_content[:50]}...")
                
                # AI가 응답하지 않은 항목들을 위한 기본 제안 생성 (AI ID 기준)
                responded_ai_ids = {s['ai_id'] for s in unique_suggestions if 'ai_id' in s}
                missing_suggestions = []
                
                for check in failed_checks:
                    if check['id'] not in responded_ai_ids:
                        missing_suggestions.append({
                            'id': check.get('original_check_id', check.get('id')),
                            'section_id': check.get('section_id'),
                            'title': check['title'],
                            'suggestion': f"{check['title']} 항목을 개선하기 위해 전문가의 도움을 받거나 추가 분석이 필요합니다.",
                            'expected_impact': "전문가 상담 후 구체적 효과 예측 가능",
                            'ai_id': check['id']
                        })
                
                # 외부로 내보내기 전 내부용 ai_id 제거
                all_suggestions = []
                for s in unique_suggestions + missing_suggestions:
                    s = s.copy()
                    s.pop('ai_id', None)
                    all_suggestions.append(s)
                return all_suggestions
            else:
                print(f"AI 제안 응답이 리스트 형식이 아님: {parsed_data}")
                # AI 응답이 잘못된 경우 기본 제안 생성
                return [{'id': check['id'], 'title': check['title'], 'suggestion': f"{check['title']} 항목을 개선하기 위해 전문가의 도움을 받거나 추가 분석이 필요합니다.", 'expected_impact': "전문가 상담 후 구체적 효과 예측 가능"} for check in failed_checks]
        except json.JSONDecodeError as e:
            print(f"AI 제안 응답 JSON 파싱 실패: {e}")
            # JSON 파싱 실패 시 기본 제안 생성
            return [{'id': check['id'], 'title': check['title'], 'suggestion': f"{check['title']} 항목을 개선하기 위해 전문가의 도움을 받거나 추가 분석이 필요합니다.", 'expected_impact': "전문가 상담 후 구체적 효과 예측 가능"} for check in failed_checks]
        except Exception as e:
            print(f"AI 제안 파싱 중 알 수 없는 오류: {e}")
            # 기타 오류 시 기본 제안 생성
            return [{'id': check['id'], 'title': check['title'], 'suggestion': f"{check['title']} 항목을 개선하기 위해 전문가의 도움을 받거나 추가 분석이 필요합니다.", 'expected_impact': "전문가 상담 후 구체적 효과 예측 가능"} for check in failed_checks]

    def _normalize_ai_insights(self, ai_data: Dict) -> Dict:
        defaults = self._get_default_insights()
        
        # 필드 이름이 다른 경우를 대비해 매핑
        if 'timeline' in ai_data:
            ai_data['implementation_timeline'] = ai_data.pop('timeline')
        if 'summary' in ai_data:
            ai_data['overall_summary'] = ai_data.pop('summary')
        if 'expected_improvement' in ai_data:
            ai_data['expected_score_improvement'] = ai_data.pop('expected_improvement')
        
        # 모든 필수 필드에 대해 기본값 설정
        for key, value in defaults.items():
            ai_data.setdefault(key, value)
        
        # priority_recommendations의 구조 안정화 - Recommendation 모델에 맞게 변환
        if isinstance(ai_data.get('priority_recommendations'), list):
            normalized_recommendations = []
            for rec in ai_data['priority_recommendations']:
                if isinstance(rec, dict):
                    normalized_rec = {
                        "priority": rec.get("priority", "중간"),
                        "title": rec.get("title", "제목 없음"),
                        "description": rec.get("description", "설명 없음"),
                        "category": rec.get("category", "일반"),
                        "expected_impact": rec.get("expected_impact", "효과 예측 불가")
                    }
                    normalized_recommendations.append(normalized_rec)
            ai_data['priority_recommendations'] = normalized_recommendations
        else:
            ai_data['priority_recommendations'] = defaults['priority_recommendations']

        return ai_data

    def _get_default_insights(self, error: str = None) -> Dict:
        summary = "AI 분석 중 오류가 발생했습니다. 잠시 후 다시 시도해주세요."
        if error:
            summary += f" (오류: {error})"
            
        return {
            "overall_summary": summary,
            "seo_level": "분석 실패",
            "strengths": [],
            "priority_recommendations": [
                {
                    "priority": "높음",
                    "title": "AI 분석 재시도",
                    "description": "AI 분석 서비스를 다시 시도해주세요.",
                    "category": "기술적 문제",
                    "expected_impact": "분석 정확도 향상"
                }
            ],
            "technical_insights": [],
            "content_insights": [],
            "expected_score_improvement": "N/A",
            "implementation_timeline": "N/A"
        }

    def _get_default_suggestions(self, failed_checks: list) -> list:
        """AI 분석 실패 시 기본 제안을 생성합니다."""
        suggestions = []
        for check in failed_checks:
            suggestions.append({
                'id': check['id'],
                'title': check['title'],  # 실제 항목 제목 포함
                'suggestion': f"{check['title']} 항목을 개선하기 위해 전문가의 도움을 받거나 추가 분석이 필요합니다.",
                'expected_impact': "전문가 상담 후 구체적 효과 예측 가능"
            })
        return suggestions

    async def _extract_sitemap_urls(self, url: str) -> List[str]:
        """사이트맵에서 URL들을 추출합니다."""
        try:
            # 사이트맵 URL 추측
            domain = url.split("//")[1].split("/")[0] if "//" in url else url
            sitemap_urls = [
                f"https://{domain}/sitemap.xml",
                f"https://{domain}/sitemap_index.xml",
                f"https://{domain}/sitemap/sitemap.xml",
                f"http://{domain}/sitemap.xml"
            ]
            
            async with httpx.AsyncClient(timeout=30.0) as client:
                for sitemap_url in sitemap_urls:
                    try:
                        print(f"  🔍 사이트맵 확인 중: {sitemap_url}")
                        response = await client.get(sitemap_url)
                        
                        if response.status_code == 200:
                            print(f"    ✅ 사이트맵 발견: {sitemap_url}")
                            return await self._parse_sitemap(response.text, domain)
                    except Exception as e:
                        print(f"    ❌ 사이트맵 접근 실패: {e}")
                        continue
            
            print(f"  ⚠️ 사이트맵을 찾을 수 없음, 메인 페이지에서 링크 추출")
            return []
            
        except Exception as e:
            print(f"❌ 사이트맵 추출 오류: {e}")
            return []

    async def _parse_sitemap(self, sitemap_content: str, domain: str) -> List[str]:
        """사이트맵 XML을 파싱하여 URL들을 추출합니다."""
        try:
            urls = []
            soup = BeautifulSoup(sitemap_content, 'xml')
            
            # URL 태그들 찾기
            url_tags = soup.find_all('url')
            for url_tag in url_tags:
                loc_tag = url_tag.find('loc')
                if loc_tag and loc_tag.text:
                    urls.append(loc_tag.text.strip())
            
            # sitemap 태그들도 확인 (sitemap index인 경우)
            sitemap_tags = soup.find_all('sitemap')
            for sitemap_tag in sitemap_tags:
                loc_tag = sitemap_tag.find('loc')
                if loc_tag and loc_tag.text:
                    # 하위 사이트맵도 파싱
                    try:
                        async with httpx.AsyncClient(timeout=30.0) as client:
                            response = await client.get(loc_tag.text.strip())
                            if response.status_code == 200:
                                sub_urls = await self._parse_sitemap(response.text, domain)
                                urls.extend(sub_urls)
                    except:
                        continue
            
            print(f"    📋 사이트맵에서 {len(urls)}개 URL 추출")
            return urls[:50]  # 최대 50개만 반환
            
        except Exception as e:
            print(f"❌ 사이트맵 파싱 오류: {e}")
            return []

    # (삭제) E-E-A-T 수집 경로 제거 (site structure/성능 수집과 통합 예정)
    async def _collect_website_data_with_sitemap(self, url: str, sitemap_urls: List[str]) -> Dict:
        """사이트맵 URL들을 활용하여 웹사이트 데이터를 수집합니다."""
        import time
        
        try:
            print(f"🌐 웹사이트 데이터 수집 시작 (사이트맵 활용): {url}")
            
            async with httpx.AsyncClient(timeout=30.0, follow_redirects=True) as client:
                # 메인 페이지 수집
                main_start_time = time.time()
                print(f"  📄 메인 페이지 수집 중: {url}")
                main_response = await client.get(url)
                main_html = main_response.text
                main_time = time.time() - main_start_time
                print(f"    ✅ 메인 페이지 수집 완료 (상태: {main_response.status_code}, 크기: {len(main_html)} 문자, 소요시간: {main_time:.2f}초)")
                
                # 사이트맵에서 발견한 URL들과 메인 페이지에서 추출한 링크들을 결합
                all_urls = set([url])  # 메인 페이지 포함
                
                # 사이트맵 URL들 추가
                for sitemap_url in sitemap_urls:
                    all_urls.add(sitemap_url)
                
                # 메인 페이지에서 추가 링크들 추출
                main_page_links = self._extract_important_links(main_html, url)
                for link in main_page_links:
                    all_urls.add(link)
                
                print(f"  📋 총 수집 대상 URL: {len(all_urls)}개")
                print(f"    - 사이트맵 URL: {len(sitemap_urls)}개")
                print(f"    - 메인 페이지 링크: {len(main_page_links)}개")
                
                # URL들을 E-E-A-T 관련성에 따라 필터링
                filtered_urls = self._filter_eeat_relevant_urls(list(all_urls))
                print(f"  🔍 E-E-A-T 관련 URL 필터링: {len(filtered_urls)}개")
                
                # 발견된 페이지들 수집
                pages_data = {}
                collected_count = 0
                max_pages = 15  # 최대 15개 페이지만 수집
                
                for page_url in filtered_urls[:max_pages]:
                    try:
                        page_start_time = time.time()
                        print(f"  📄 페이지 수집 중 ({collected_count + 1}/{min(len(filtered_urls), max_pages)}): {page_url}")
                        response = await client.get(page_url, timeout=30.0)
                        page_time = time.time() - page_start_time
                        
                        if response.status_code == 200:
                            pages_data[page_url] = response.text
                            collected_count += 1
                            print(f"    ✅ 성공 (크기: {len(response.text)} 문자, 소요시간: {page_time:.2f}초)")
                        else:
                            print(f"    ❌ 실패 (상태: {response.status_code}, 소요시간: {page_time:.2f}초)")
                    except Exception as e:
                        page_time = time.time() - page_start_time
                        print(f"    ❌ 오류: {e} (소요시간: {page_time:.2f}초)")
                
                # 데이터 구조화
                website_data = {
                    "main_page": {
                        "url": url,
                        "html": main_html,
                        "status_code": main_response.status_code,
                        "protocol": "https" if url.startswith("https") else "http",
                        "size": len(main_html)
                    },
                    "pages": pages_data,
                    "sitemap_urls": sitemap_urls,
                    "total_urls_found": len(all_urls),
                    "filtered_urls": filtered_urls,
                    "pages_collected": collected_count,
                    "domain": url.split("//")[1].split("/")[0] if "//" in url else url
                }
                
                print(f"✅ 웹사이트 데이터 수집 완료")
                print(f"  📊 수집 통계:")
                print(f"    - 사이트맵 URL: {len(sitemap_urls)}개")
                print(f"    - 총 발견 URL: {len(all_urls)}개")
                print(f"    - E-E-A-T 관련 URL: {len(filtered_urls)}개")
                print(f"    - 성공적으로 수집: {collected_count}개")
                print(f"    - 메인 페이지 크기: {len(main_html)} 문자")
                print(f"    - 총 수집 데이터 크기: {len(main_html) + sum(len(html) for html in pages_data.values())} 문자")
                
                return website_data
                
        except Exception as e:
            print(f"❌ 웹사이트 데이터 수집 오류: {e}")
            return {"error": str(e)}

    # (삭제) E-E-A-T URL 필터 제거
    def _filter_eeat_relevant_urls(self, urls: List[str]) -> List[str]:
        """E-E-A-T 분석에 관련된 URL들을 필터링합니다."""
        eeat_keywords = [
            'about', 'team', 'profile', 'author', 'expert', 'specialist',
            'contact', 'company', 'corporate', 'info', 'information',
            'privacy', 'policy', 'terms', 'legal', 'security',
            'award', 'certificate', 'credential', 'portfolio',
            'review', 'testimonial', 'client', 'case', 'study',
            'blog', 'news', 'article', 'insight', 'research',
            'service', 'solution', 'product', 'technology'
        ]
        
        filtered_urls = []
        for url in urls:
            url_lower = url.lower()
            # 키워드가 URL에 포함되어 있거나
            if any(keyword in url_lower for keyword in eeat_keywords):
                filtered_urls.append(url)
            # 또는 메인 페이지인 경우
            elif url == urls[0]:  # 첫 번째 URL은 메인 페이지로 가정
                filtered_urls.append(url)
        
        return filtered_urls

    async def _request_ai_analysis(self, url: str, website_data: Dict) -> Dict:
        """수집된 데이터를 AI에게 분석 요청합니다."""
        try:
            print(f"🤖 AI 분석 요청 준비 중...")
            
            # 수집된 데이터를 AI가 분석할 수 있는 형태로 정리
            analysis_data = {
                "url": url,
                "main_page_size": len(website_data.get("main_page", {}).get("html", "")),
                "pages_collected": len(website_data.get("pages", {})),
                "total_data_size": len(website_data.get("main_page", {}).get("html", "")) + 
                                 sum(len(html) for html in website_data.get("pages", {}).values()),
                "domain": website_data.get("domain", ""),
                "collected_urls": list(website_data.get("pages", {}).keys())
            }
            
            # AI 프롬프트 생성
            prompt = self._create_ai_analysis_prompt(url, analysis_data)
            
            # Gemini API 호출
            print(f"  📤 Gemini API 호출 중...")
            response = await self._call_gemini_api(prompt)
            
            if response and "error" not in response:
                print(f"  ✅ AI 분석 응답 수신 완료")
                return response
            else:
                print(f"  ❌ AI 분석 실패: {response.get('error', 'Unknown error')}")
                return {"error": "AI 분석 실패"}
                
        except Exception as e:
            print(f"❌ AI 분석 요청 오류: {e}")
            return {"error": str(e)}

    # (삭제) E-E-A-T 전용 AI 프롬프트 제거
    def _create_ai_analysis_prompt(self, url: str, analysis_data: Dict) -> str:
        """AI 분석을 위한 프롬프트를 생성합니다."""
        return f"""
당신은 웹사이트의 E-E-A-T (Experience, Expertise, Authoritativeness, Trustworthiness) 분석 전문가입니다.

분석 대상: {url}
수집된 데이터:
- 메인 페이지 크기: {analysis_data['main_page_size']} 문자
- 수집된 URL들: {', '.join(analysis_data['collected_urls'][:10])}

다음 8개 E-E-A-T 항목에 대해 각각 0-1점으로 평가하고, 근거와 개선방안을 제시해주세요:

1. 저자 프로필 (Author Profile)
2. 자격 증명 (Credentials)
3. 외부 리뷰 (External Reviews)
4. 신뢰성 있는 출처 (Reliable Sources)
5. 브랜드 존재감 (Brand Presence)
6. 보안 배지 (Security Badges)
7. 프라이버시 정책 (Privacy Policy)
8. 회사 정보 (Company Information)

응답 형식:
{{
            "eeat_score": 0.75,
        "eeat_percentage": 75.0,
    "author_profile": {{
        "score": 0.6,
        "status": "부분충족",
        "evidence": "발견된 근거들",
        "improvements": ["개선방안1", "개선방안2"]
    }},
    // ... 나머지 7개 항목도 동일한 형식
    "strengths": ["강점1", "강점2"],
    "weaknesses": ["약점1", "약점2"],
    "priority_recommendations": [
        {{
            "priority": "높음/중간/낮음",
            "title": "제목",
            "description": "설명",
            "expected_impact": "예상 효과"
        }}
    ]
}}

중요: 실제 수집된 데이터만을 기반으로 분석하고, 존재하지 않는 정보는 추측하지 마세요.
"""

    async def _call_gemini_api(self, prompt: str) -> Dict:
        """Gemini API를 호출합니다."""
        try:
            import google.generativeai as genai
            import json
            import re
            
            # API 키 설정
            api_key = os.getenv("GOOGLE_API_KEY")
            if not api_key:
                return {"error": "Google API 키가 설정되지 않았습니다."}
            
            genai.configure(api_key=api_key)
            model = genai.GenerativeModel('gemini-2.0-flash-exp')
            
            response = model.generate_content(prompt)
            
            if response.text:
                # JSON 파싱 시도 (여러 방법)
                response_text = response.text.strip()
                
                # 1. 직접 JSON 파싱 시도
                try:
                    result = json.loads(response_text)
                    return result
                except json.JSONDecodeError:
                    pass
                
                # 2. 코드 블록에서 JSON 추출 시도
                try:
                    json_match = re.search(r'```json\s*(\{.*?\})\s*```', response_text, re.DOTALL)
                    if json_match:
                        json_str = json_match.group(1)
                        result = json.loads(json_str)
                        return result
                except (json.JSONDecodeError, AttributeError):
                    pass
                
                # 3. 중괄호로 둘러싸인 JSON 추출 시도
                try:
                    json_match = re.search(r'(\{.*\})', response_text, re.DOTALL)
                    if json_match:
                        json_str = json_match.group(1)
                        result = json.loads(json_str)
                        return result
                except (json.JSONDecodeError, AttributeError):
                    pass
                
                # 4. 모든 방법이 실패하면 raw_response 반환
                print(f"⚠️ AI 응답 JSON 파싱 실패, raw_response 반환")
                return {"raw_response": response_text}
            else:
                return {"error": "AI 응답이 비어있습니다."}
                
        except Exception as e:
            return {"error": f"Gemini API 호출 오류: {str(e)}"}

    async def _analyze_web_performance_with_data(self, url: str, website_data: Dict) -> Dict:
        """웹 성능 AI 분석을 수행합니다."""
        try:
            # AI 분석 프롬프트 생성
            prompt = self._create_web_performance_analysis_prompt(url, website_data)
            
            # AI API 호출
            response = await self._call_gemini_api(prompt)
            
            return response
            
        except Exception as e:
            print(f"❌ 웹 성능 AI 분석 오류: {e}")
            return {"error": str(e)}

    async def _analyze_url_content_with_data(self, url: str, website_data: Dict) -> Dict:
        """URL 및 콘텐츠 표준화 AI 분석을 수행합니다."""
        try:
            # AI 분석 프롬프트 생성
            prompt = self._create_url_content_analysis_prompt(url, website_data)
            
            # AI API 호출
            response = await self._call_gemini_api(prompt)
            
            return response
            
        except Exception as e:
            print(f"❌ URL 콘텐츠 AI 분석 오류: {e}")
            return {"error": str(e)}

    def _create_web_performance_analysis_prompt(self, url: str, website_data: Dict) -> str:
        """웹 성능 분석을 위한 프롬프트를 생성합니다."""
        prompt = """
당신은 웹 성능 전문가입니다. 제공된 웹사이트 URL에 대해 웹 성능 및 기술적 최적화를 체계적으로 분석해주세요.

## 분석 대상 URL
""" + url + """

## ⚠️ 중요 규칙
1. **실제 방문하지 않은 URL을 언급하지 마세요** - 제공된 URL만 분석하세요
2. **추측하지 마세요** - 실제로 확인할 수 없는 정보는 "확인 불가"로 표시하세요
3. **구체적인 URL을 언급할 때는 반드시 실제 존재하는 페이지만 언급하세요**
4. **존재하지 않는 페이지나 섹션을 가정하지 마세요**

## 분석 프로세스

### 1단계: 제공된 URL 분석
주어진 URL에서 직접 확인할 수 있는 정보만을 바탕으로 분석하세요.

### 2단계: 웹 성능 핵심 항목 체계적 분석
각 항목을 0점(미흡) ~ 1점(충족) 척도로 평가하고, 구체적 근거와 개선방안을 제시하세요.

#### 📋 평가 체크리스트

**웹 성능 및 기술적 최적화 (8개 항목)**

**1) LCP (Largest Contentful Paint)**
- [ ] LCP ≤ 2.5초 (우수)
- [ ] LCP ≤ 4.0초 (개선 필요)
- [ ] LCP > 4.0초 (나쁨)

**2) INP (Interaction to Next Paint)**
- [ ] INP ≤ 200ms (우수)
- [ ] INP ≤ 500ms (개선 필요)
- [ ] INP > 500ms (나쁨)

**3) CLS (Cumulative Layout Shift)**
- [ ] CLS < 0.1 (우수)
- [ ] CLS < 0.25 (개선 필요)
- [ ] CLS ≥ 0.25 (나쁨)

**4) TTFB (Time to First Byte)**
- [ ] TTFB ≤ 0.8초 (우수)
- [ ] TTFB ≤ 1.8초 (개선 필요)
- [ ] TTFB > 1.8초 (나쁨)

**5) 모바일 친화성**
- [ ] Viewport 메타 태그 설정
- [ ] 반응형 디자인 적용
- [ ] 터치 친화적 인터페이스

**6) 페이지 크기**
- [ ] 총 페이지 크기 ≤ 500KB
- [ ] 총 페이지 크기 ≤ 1MB
- [ ] 총 페이지 크기 > 1MB

**7) 이미지 최적화**
- [ ] 이미지 압축 적용
- [ ] 적절한 이미지 형식 사용
- [ ] 지연 로딩 적용

**8) 리소스 최적화**
- [ ] CSS/JS 압축
- [ ] 브라우저 캐싱 설정
- [ ] 불필요한 리소스 제거

### 3단계: 결과 리포트 형식

## [웹사이트명] 웹 성능 분석 리포트

### 종합 점수: X.X/8점 (XX.X%)

### 항목별 세부 평가

1) LCP (Largest Contentful Paint)
- 점수: X점 (충족/부분충족/미흡)
- 근거: [구체적 발견 사항 - 실제 확인된 내용만]
- 개선방안: [실행 가능한 개선안]

[나머지 7개 항목도 동일 형식으로 작성]

### 주요 발견사항
- 강점: [잘 구현된 부분]
- 약점: [개선이 필요한 부분]
- 우선순위: [가장 중요한 개선사항]

### 개선 권장사항
1. [우선순위 1] - [구체적 개선방안]
2. [우선순위 2] - [구체적 개선방안]
3. [우선순위 3] - [구체적 개선방안]

### 예상 개선 효과
- 성능 점수 향상: [예상 점수]
- 사용자 경험 개선: [예상 효과]
- 검색 엔진 최적화: [예상 효과]

JSON 형식으로 응답해주세요:
```json
{
  "web_performance_score": 0.0,
  "web_performance_percentage": 0.0,
  "performance_metrics": {
    "lcp": {"score": 0.0, "evidence": "", "improvement": ""},
    "inp": {"score": 0.0, "evidence": "", "improvement": ""},
    "cls": {"score": 0.0, "evidence": "", "improvement": ""},
    "ttfb": {"score": 0.0, "evidence": "", "improvement": ""},
    "mobile_friendly": {"score": 0.0, "evidence": "", "improvement": ""},
    "page_size": {"score": 0.0, "evidence": "", "improvement": ""},
    "image_optimization": {"score": 0.0, "evidence": "", "improvement": ""},
    "resource_optimization": {"score": 0.0, "evidence": "", "improvement": ""}
  },
  "strengths": [],
  "weaknesses": [],
  "priority_recommendations": [],
  "expected_improvement": ""
}
```
"""
        return prompt

    def _create_url_content_analysis_prompt(self, url: str, website_data: Dict) -> str:
        """URL 및 콘텐츠 표준화 분석을 위한 프롬프트를 생성합니다."""
        prompt = """
당신은 SEO 전문가입니다. 제공된 웹사이트 URL에 대해 URL 및 콘텐츠 표준화를 체계적으로 분석해주세요.

## 분석 대상 URL
""" + url + """

## ⚠️ 중요 규칙
1. **실제 방문하지 않은 URL을 언급하지 마세요** - 제공된 URL만 분석하세요
2. **추측하지 마세요** - 실제로 확인할 수 없는 정보는 "확인 불가"로 표시하세요
3. **구체적인 URL을 언급할 때는 반드시 실제 존재하는 페이지만 언급하세요**
4. **존재하지 않는 페이지나 섹션을 가정하지 마세요**

## 분석 프로세스

### 1단계: 제공된 URL 분석
주어진 URL에서 직접 확인할 수 있는 정보만을 바탕으로 분석하세요.

### 2단계: 18개 핵심 항목 체계적 분석
각 항목을 0점(미흡) ~ 1점(충족) 척도로 평가하고, 구체적 근거와 개선방안을 제시하세요.

#### 📋 평가 체크리스트

**URL 및 표준화 관리 (9개 항목)**

**1) 짧은 영문-kebab URL**
- [ ] URL 길이 ≤115자
- [ ] 소문자 사용
- [ ] kebab-case 형식 (단어-단어)
- [ ] 의미있는 키워드 포함

**2) 중복 파라미터 제거**
- [ ] 동일한 파라미터 중복 없음
- [ ] 불필요한 파라미터 제거
- [ ] ID와 콘텐츠 1:1 매핑

**3) Self-canonical 100%**
- [ ] rel=canonical 태그 존재
- [ ] 자기 자신을 가리키는 canonical
- [ ] 올바른 URL 형식

**4) Canonical 체인 0건**
- [ ] canonical 체인 없음
- [ ] A→B 단일 호출 구조

**5) 교차 도메인 정합**
- [ ] cross-domain canonical 적용
- [ ] 올바른 도메인 지정

**6) HTTP vs HTTPS 혼재**
- [ ] HTTPS 우선 사용
- [ ] HTTP 리소스 없음
- [ ] Mixed content 없음

**7) 매개변수 정책**
- [ ] GSC URL 파라미터 설정
- [ ] 추적 파라미터 관리
- [ ] 세션 파라미터 제거

**8) 언어/지역 세그먼트**
- [ ] /en-us/ 패턴 정규화
- [ ] 언어 코드 표준화
- [ ] 지역 코드 표준화

**9) Redirect Hop ≤1**
- [ ] 301 연속 1회로 종료
- [ ] 리다이렉트 체인 최소화

**중복 및 얇은 콘텐츠 (9개 항목)**

**10) 해시 중복 ≤10%**
- [ ] SHA-256 유사도 분석
- [ ] 중복 콘텐츠 비율 ≤10%

**11) Title 중복 ≤5%**
- [ ] 제목 중복 비율 ≤5%
- [ ] 고유한 제목 사용

**12) Meta Description 중복 ≤5%**
- [ ] 메타 설명 중복 비율 ≤5%
- [ ] 고유한 설명 사용

**13) Near-duplicate TF-IDF**
- [ ] Cosine 유사도 ≤0.9
- [ ] TF-IDF 기반 분석

**14) Thin 단어수 하위 20%**
- [ ] 얇은 콘텐츠 비율 ≤15%
- [ ] 충분한 단어수 확보

**15) Facet 파라미터 noindex**
- [ ] Facet 페이지 noindex 설정
- [ ] 필터링 페이지 관리



**17) Syndication cross-domain rel=canonical**
- [ ] 원본 콘텐츠 지정
- [ ] 교차 도메인 canonical

**18) SessionID 제거**
- [ ] URL에서 세션 ID 제거
- [ ] 쿠키 우선 사용

### 3단계: 결과 리포트 형식

## [웹사이트명] URL 콘텐츠 표준화 분석 리포트

### 종합 점수: X.X/18점 (XX.X%)

### 항목별 세부 평가

1) 짧은 영문-kebab URL
- 점수: X점 (충족/부분충족/미흡)
- 근거: [구체적 발견 사항 - 실제 확인된 내용만]
- 개선방안: [실행 가능한 개선안]

[나머지 17개 항목도 동일 형식으로 작성]

### 주요 발견사항
- 강점: [잘 구현된 부분]
- 약점: [개선이 필요한 부분]
- 우선순위: [가장 중요한 개선사항]

### 개선 권장사항
1. [우선순위 1] - [구체적 개선방안]
2. [우선순위 2] - [구체적 개선방안]
3. [우선순위 3] - [구체적 개선방안]

### 예상 개선 효과
- SEO 점수 향상: [예상 점수]
- 검색 노출 개선: [예상 효과]
- 사용자 경험 향상: [예상 효과]

JSON 형식으로 응답해주세요:
```json
{
  "url_content_score": 0.0,
  "url_content_percentage": 0.0,
  "url_management": {
    "url_length_format": {"score": 0.0, "evidence": "", "improvement": ""},
    "duplicate_parameters": {"score": 0.0, "evidence": "", "improvement": ""},
    "self_canonical": {"score": 0.0, "evidence": "", "improvement": ""},
    "canonical_chains": {"score": 0.0, "evidence": "", "improvement": ""},
    "cross_domain_canonical": {"score": 0.0, "evidence": "", "improvement": ""},
    "http_https_mixed": {"score": 0.0, "evidence": "", "improvement": ""},
    "url_parameters": {"score": 0.0, "evidence": "", "improvement": ""},
    "language_segments": {"score": 0.0, "evidence": "", "improvement": ""},
    "redirect_hops": {"score": 0.0, "evidence": "", "improvement": ""}
  },
  "content_duplication": {
    "hash_duplicates": {"score": 0.0, "evidence": "", "improvement": ""},
    "title_duplicates": {"score": 0.0, "evidence": "", "improvement": ""},
    "description_duplicates": {"score": 0.0, "evidence": "", "improvement": ""},
    "near_duplicate_content": {"score": 0.0, "evidence": "", "improvement": ""},
    "thin_content": {"score": 0.0, "evidence": "", "improvement": ""},
    "facet_parameters": {"score": 0.0, "evidence": "", "improvement": ""},

    "syndication_canonical": {"score": 0.0, "evidence": "", "improvement": ""},
    "session_id_removal": {"score": 0.0, "evidence": "", "improvement": ""}
  },
  "strengths": [],
  "weaknesses": [],
  "priority_recommendations": [],
  "expected_improvement": ""
}
```
"""
        return prompt

    async def _validate_web_performance_response(self, ai_result: Dict, original_url: str) -> Dict:
        """웹 성능 AI 응답을 검증하고 정규화합니다."""
        try:
            if "error" in ai_result:
                return self._get_default_web_performance_insights(ai_result["error"])
            
            # 기본 구조 검증
            if not isinstance(ai_result, dict):
                return self._get_default_web_performance_insights("잘못된 응답 형식")
            
            # 필수 필드 확인
            required_fields = ["web_performance_score", "web_performance_percentage"]
            for field in required_fields:
                if field not in ai_result:
                    return self._get_default_web_performance_insights(f"필수 필드 누락: {field}")
            
            # 점수 정규화 (0-1 범위)
            score = float(ai_result.get("web_performance_score", 0))
            percentage = float(ai_result.get("web_performance_percentage", 0))
            
            # 성능 메트릭 검증
            performance_metrics = ai_result.get("performance_metrics", {})
            if not isinstance(performance_metrics, dict):
                performance_metrics = {}
            
            # 결과 정규화
            normalized_result = {
                "web_performance_score": min(max(score, 0), 1),
                "web_performance_percentage": min(max(percentage, 0), 100),
                "performance_metrics": performance_metrics,
                "strengths": ai_result.get("strengths", []),
                "weaknesses": ai_result.get("weaknesses", []),
                "priority_recommendations": ai_result.get("priority_recommendations", []),
                "expected_improvement": ai_result.get("expected_improvement", "")
            }
            
            return normalized_result
            
        except Exception as e:
            print(f"❌ 웹 성능 응답 검증 오류: {e}")
            return self._get_default_web_performance_insights(str(e))

    async def _validate_url_content_response(self, ai_result: Dict, original_url: str) -> Dict:
        """URL 콘텐츠 표준화 AI 응답을 검증하고 정규화합니다."""
        try:
            if "error" in ai_result:
                return self._get_default_url_content_insights(ai_result["error"])
            
            # 기본 구조 검증
            if not isinstance(ai_result, dict):
                return self._get_default_url_content_insights("잘못된 응답 형식")
            
            # 필수 필드 확인
            required_fields = ["url_content_score", "url_content_percentage"]
            for field in required_fields:
                if field not in ai_result:
                    return self._get_default_url_content_insights(f"필수 필드 누락: {field}")
            
            # 점수 정규화 (0-1 범위)
            score = float(ai_result.get("url_content_score", 0))
            percentage = float(ai_result.get("url_content_percentage", 0))
            
            score = max(0.0, min(1.0, score))
            percentage = max(0.0, min(100.0, percentage))
            
            # 결과 정규화
            normalized_result = {
                "url_content_score": score,
                "url_content_percentage": percentage,
                "url_management": ai_result.get("url_management", {}),
                "content_duplication": ai_result.get("content_duplication", {}),
                "strengths": ai_result.get("strengths", []),
                "weaknesses": ai_result.get("weaknesses", []),
                "priority_recommendations": ai_result.get("priority_recommendations", []),
                "expected_improvement": ai_result.get("expected_improvement", ""),
                "url": original_url
            }
            
            return normalized_result
            
        except Exception as e:
            print(f"❌ URL 콘텐츠 응답 검증 오류: {e}")
            return self._get_default_url_content_insights(str(e))

    def _get_default_web_performance_insights(self, error: str = None) -> Dict:
        """웹 성능 기본 인사이트를 반환합니다."""
        return {
            "web_performance_score": 0.0,
            "web_performance_percentage": 0.0,
            "performance_metrics": {},
            "strengths": [],
            "weaknesses": ["분석 중 오류가 발생했습니다."] if error else [],
            "priority_recommendations": [],
            "expected_improvement": "분석을 완료한 후 개선 방안을 제시할 수 있습니다.",
            "error": error
        }

    def _get_default_url_content_insights(self, error: str = None) -> Dict:
        """URL 콘텐츠 표준화 기본 인사이트를 반환합니다."""
        return {
            "url_content_score": 0.0,
            "url_content_percentage": 0.0,
            "url_management": {},
            "content_duplication": {},
            "strengths": [],
            "weaknesses": ["분석 중 오류가 발생했습니다."] if error else [],
            "priority_recommendations": [],
            "expected_improvement": "분석을 완료한 후 개선 방안을 제시할 수 있습니다.",
            "error": error
        }

    async def _combine_ai_and_rule_analysis(self, url: str, website_data: Dict, ai_result: Dict) -> Dict:
        """AI 분석 결과와 규칙 기반 분석 결과를 결합합니다."""
        try:
            print(f"🔄 AI 분석 결과와 규칙 기반 분석 결과 결합 중...")
            
            # 규칙 기반 분석 수행
            rule_result = await self._analyze_eeat_with_data(url, website_data)
            
            # AI 결과가 성공적인 경우 결합
            if ai_result and "error" not in ai_result:
                print(f"  ✅ AI 분석 결과와 규칙 기반 분석 결과 결합 완료")
                
                # AI 분석과 규칙 기반 분석의 점수를 합산하여 평균 계산
                ai_score = ai_result.get("eeat_score", 0)
                rule_score = rule_result.get("eeat_score", 0)
                combined_score = (ai_score + rule_score) / 2 if ai_score > 0 and rule_score > 0 else max(ai_score, rule_score)
                
                ai_percentage = ai_result.get("eeat_percentage", 0)
                rule_percentage = rule_result.get("eeat_percentage", 0)
                combined_percentage = (ai_percentage + rule_percentage) / 2 if ai_percentage > 0 and rule_percentage > 0 else max(ai_percentage, rule_percentage)
                
                print(f"  📊 점수 계산:")
                print(f"    - AI 점수: {ai_score:.3f} ({ai_percentage}%)")
                print(f"    - Rule 점수: {rule_score:.3f} ({rule_percentage}%)")
                print(f"    - 결합 점수: {combined_score:.3f} ({combined_percentage:.1f}%)")
                
                # 기본 combined_result 생성
                combined_result = {
                    "url": url,
                    "eeat_score": round(combined_score, 2),
                    "eeat_percentage": round(combined_percentage, 1),
                    "ai_analysis": ai_result,
                    "rule_analysis": rule_result,
                    "analysis_method": "AI + Rule-based",
                    "strengths": ai_result.get("strengths", rule_result.get("strengths", [])),
                    "weaknesses": ai_result.get("weaknesses", rule_result.get("weaknesses", [])),
                    "priority_recommendations": ai_result.get("priority_recommendations", rule_result.get("priority_recommendations", [])),
                    "industry_benchmark": rule_result.get("industry_benchmark", "업계 평균")
                }
                
                # urgent_improvements 필드 생성
                urgent_improvements = []
                for name, item in [("저자 프로필", combined_result.get("author_profile", {})),
                                 ("자격 증명", combined_result.get("credentials", {})),
                                 ("외부 리뷰", combined_result.get("external_reviews", {})),
                                 ("신뢰성 있는 출처", combined_result.get("reliable_sources", {})),
                                 ("브랜드 존재감", combined_result.get("brand_presence", {})),
                                 ("보안 배지", combined_result.get("security_badges", {})),
                                 ("프라이버시 정책", combined_result.get("privacy_policy", {})),
                                 ("회사 정보", combined_result.get("company_info", {}))]:
                    if isinstance(item, dict) and item.get("score", 0) <= 0.2:
                        urgent_improvements.append(f"{name} 개선 필요")
                
                combined_result["urgent_improvements"] = urgent_improvements
                
                # 개별 항목들도 AI와 Rule 분석의 평균값으로 결합
                for item in ["author_profile", "credentials", "external_reviews", "reliable_sources", 
                           "brand_presence", "security_badges", "privacy_policy", "company_info"]:
                    ai_item = ai_result.get(item, {})
                    rule_item = rule_result.get(item, {})
                    
                    # AI와 Rule 점수의 평균 계산
                    ai_item_score = ai_item.get("score", 0) if isinstance(ai_item, dict) else 0
                    rule_item_score = rule_item.get("score", 0) if isinstance(rule_item, dict) else 0
                    combined_item_score = (ai_item_score + rule_item_score) / 2 if ai_item_score > 0 and rule_item_score > 0 else max(ai_item_score, rule_item_score)
                    
                    # 상태 결정 (평균 점수 기준)
                    if combined_item_score >= 0.6:
                        combined_status = "충족"
                    elif combined_item_score >= 0.3:
                        combined_status = "부분충족"
                    else:
                        combined_status = "미흡"
                    
                    # 안전한 데이터 추출
                    ai_evidence = ai_item.get("evidence", "") if isinstance(ai_item, dict) else ""
                    rule_evidence = rule_item.get("evidence", "") if isinstance(rule_item, dict) else ""
                    ai_improvements = ai_item.get("improvements", []) if isinstance(ai_item, dict) else []
                    rule_improvements = rule_item.get("improvements", []) if isinstance(rule_item, dict) else []
                    
                    combined_result[item] = {
                        "score": round(combined_item_score, 2),
                        "status": combined_status,
                        "evidence": ai_evidence or rule_evidence or "분석 완료",
                        "improvements": ai_improvements or rule_improvements or ["개선 방안을 확인하세요"],
                        "ai_evidence": ai_evidence,
                        "rule_evidence": rule_evidence
                    }
                
                return combined_result
            else:
                print(f"  ⚠️ AI 분석 실패, 규칙 기반 분석 결과만 사용")
                rule_result["analysis_method"] = "Rule-based only"
                rule_result["ai_error"] = ai_result.get("error", "Unknown error") if ai_result else "No AI result"
                
                # urgent_improvements 필드가 없는 경우 추가
                if "urgent_improvements" not in rule_result:
                    urgent_improvements = []
                    for name, item in [("저자 프로필", rule_result.get("author_profile", {})),
                                     ("자격 증명", rule_result.get("credentials", {})),
                                     ("외부 리뷰", rule_result.get("external_reviews", {})),
                                     ("신뢰성 있는 출처", rule_result.get("reliable_sources", {})),
                                     ("브랜드 존재감", rule_result.get("brand_presence", {})),
                                     ("보안 배지", rule_result.get("security_badges", {})),
                                     ("프라이버시 정책", rule_result.get("privacy_policy", {})),
                                     ("회사 정보", rule_result.get("company_info", {}))]:
                        if isinstance(item, dict) and item.get("score", 0) <= 0.2:
                            urgent_improvements.append(f"{name} 개선 필요")
                    rule_result["urgent_improvements"] = urgent_improvements
                
                return rule_result
                
        except Exception as e:
            print(f"❌ 결과 결합 오류: {e}")
            import traceback
            traceback.print_exc()
            return {"error": f"결과 결합 실패: {str(e)}"}

    # ===== 페이지 최적화 콘텐츠 품질 분석 메소드들 =====
    
    async def analyze_content_quality(self, url: str, website_data: Dict) -> Dict:
        """웹사이트의 콘텐츠 품질을 AI로 분석합니다."""
        try:
            print(f"🔍 콘텐츠 품질 분석 시작: {url}")
            
            # 콘텐츠 품질 분석을 위한 데이터 준비
            content_analysis_data = {
                "url": url,
                "title": website_data.get("title", ""),
                "meta_description": website_data.get("meta_description", ""),
                "content": website_data.get("content", ""),
                "headings": website_data.get("headings", []),
                "images": website_data.get("images", []),
                "links": website_data.get("links", []),
                "structured_data": website_data.get("structured_data", [])
            }
            
            # AI를 통한 종합적인 콘텐츠 품질 분석
            print("  📝 전체 콘텐츠 품질 분석...")
            ai_analysis = await self._request_content_quality_analysis(content_analysis_data)
            
            # 개별 품질 요소 분석
            print("  🎯 제목-콘텐츠 관련성 분석...")
            title_relevance = await self._analyze_title_content_relevance(content_analysis_data)
            
            print("  📄 메타 설명 품질 분석...")
            meta_quality = await self._analyze_meta_description_quality(content_analysis_data)
            
            print("  📑 헤딩 구조 분석...")
            heading_structure = await self._analyze_heading_structure(content_analysis_data)
            
            print("  🔤 키워드 최적화 분석...")
            keyword_optimization = await self._analyze_keyword_optimization(content_analysis_data)
            
            print("  🖼️ 이미지 최적화 분석...")
            image_optimization = await self._analyze_image_optimization(content_analysis_data)
            
            print("  🏗️ 구조화 데이터 품질 분석...")
            structured_data_quality = await self._analyze_structured_data_quality(content_analysis_data)
            
            print(f"  ✅ 콘텐츠 품질 분석 완료")
            
            return {
                "overall_content_quality": ai_analysis,
                "title_relevance": title_relevance,
                "meta_description_quality": meta_quality,
                "heading_structure": heading_structure,
                "keyword_optimization": keyword_optimization,
                "image_optimization": image_optimization,
                "structured_data_quality": structured_data_quality,
                "analysis_timestamp": time.strftime("%Y-%m-%d %H:%M:%S")
            }
            
        except Exception as e:
            print(f"❌ 콘텐츠 품질 분석 중 오류: {e}")
            return self._get_default_content_quality_result(str(e))

    async def _request_content_quality_analysis(self, data: Dict) -> Dict:
        """AI를 통한 전체 콘텐츠 품질 분석 요청"""
        prompt = self._create_content_quality_prompt(data)
        
        try:
            response = await self._make_api_call_with_retry(self._call_gemini_api, prompt)
            return self._parse_content_quality_response(response)
        except Exception as e:
            print(f"AI 콘텐츠 품질 분석 실패: {e}")
            return {"score": 50, "summary": "AI 분석 실패", "issues": [], "recommendations": []}

    def _create_content_quality_prompt(self, data: Dict) -> str:
        """콘텐츠 품질 분석을 위한 프롬프트 생성"""
        return f"""
웹페이지의 콘텐츠 품질을 SEO 관점에서 분석해주세요.

URL: {data['url']}
제목: {data['title']}
메타 설명: {data['meta_description']}
콘텐츠 길이: {len(data['content'])}자
헤딩 개수: {len(data['headings'])}개
이미지 개수: {len(data['images'])}개

분석할 콘텐츠:
{data['content'][:2000]}{'...' if len(data['content']) > 2000 else ''}

다음 요소들을 100점 만점으로 평가하고 JSON 형태로 응답해주세요:

```json
{{
  "overall_score": 85,
  "summary": "콘텐츠 품질에 대한 종합 평가",
  "strengths": ["강점1", "강점2"],
  "weaknesses": ["약점1", "약점2"],
  "seo_relevance": 90,
  "readability": 85,
  "content_depth": 80,
  "user_value": 88,
  "recommendations": [
    "구체적인 개선 제안1",
    "구체적인 개선 제안2"
  ]
}}
```
"""

    async def _analyze_title_content_relevance(self, data: Dict) -> Dict:
        """제목과 콘텐츠의 관련성 분석"""
        prompt = f"""
웹페이지의 제목과 실제 콘텐츠의 관련성을 분석해주세요.

제목: {data['title']}
콘텐츠: {data['content'][:1000]}...

다음 JSON 형태로 응답해주세요:
```json
{{
  "relevance_score": 85,
  "is_relevant": true,
  "title_promises": ["제목이 약속하는 내용1", "제목이 약속하는 내용2"],
  "content_delivers": ["실제 콘텐츠가 제공하는 내용1", "실제 콘텐츠가 제공하는 내용2"],
  "gaps": ["부족한 부분1", "부족한 부분2"],
  "recommendation": "구체적인 개선 제안"
}}
```
"""
        
        try:
            response = await self._make_api_call_with_retry(self._call_gemini_api, prompt)
            return self._parse_json_response(response, {"relevance_score": 50, "is_relevant": False, "recommendation": "분석 실패"})
        except Exception as e:
            return {"relevance_score": 50, "is_relevant": False, "recommendation": f"분석 중 오류: {e}"}

    async def _analyze_meta_description_quality(self, data: Dict) -> Dict:
        """메타 설명의 품질과 매력도 분석"""
        prompt = f"""
메타 설명의 품질을 SEO와 사용자 경험 관점에서 분석해주세요.

메타 설명: {data['meta_description']}
페이지 제목: {data['title']}
실제 콘텐츠: {data['content'][:1000]}...

다음 JSON 형태로 응답해주세요:
```json
{{
  "quality_score": 85,
  "appeal_score": 80,
  "accuracy_score": 90,
  "strengths": ["강점1", "강점2"],
  "issues": ["문제점1", "문제점2"],
  "ctr_potential": "높음/중간/낮음",
  "improved_version": "개선된 메타 설명 제안",
  "recommendation": "구체적인 개선 방안"
}}
```
"""
        
        try:
            response = await self._make_api_call_with_retry(self._call_gemini_api, prompt)
            return self._parse_json_response(response, {"quality_score": 50, "appeal_score": 50, "recommendation": "분석 실패"})
        except Exception as e:
            return {"quality_score": 50, "appeal_score": 50, "recommendation": f"분석 중 오류: {e}"}

    async def _analyze_heading_structure(self, data: Dict) -> Dict:
        """헤딩 구조의 논리성과 SEO 최적화 분석"""
        headings_text = "\n".join([f"H{h.get('level', 1)}: {h.get('text', '')}" for h in data['headings']])
        
        prompt = f"""
웹페이지의 헤딩 구조를 SEO와 사용자 경험 관점에서 분석해주세요.

페이지 제목: {data['title']}
헤딩 구조:
{headings_text}

콘텐츠 개요: {data['content'][:800]}...

다음 JSON 형태로 응답해주세요:
```json
{{
  "structure_score": 85,
  "hierarchy_logical": true,
  "keyword_optimization": 75,
  "issues": ["문제점1", "문제점2"],
  "missing_headings": ["추가하면 좋을 헤딩1", "추가하면 좋을 헤딩2"],
  "recommended_structure": ["H1: 제안1", "H2: 제안2"],
  "seo_impact": "높음/중간/낮음"
}}
```
"""
        
        try:
            response = await self._make_api_call_with_retry(self._call_gemini_api, prompt)
            return self._parse_json_response(response, {"structure_score": 50, "hierarchy_logical": False, "seo_impact": "중간"})
        except Exception as e:
            return {"structure_score": 50, "hierarchy_logical": False, "seo_impact": f"분석 오류: {e}"}

    async def _analyze_keyword_optimization(self, data: Dict) -> Dict:
        """키워드 최적화 상태 분석"""
        prompt = f"""
웹페이지의 키워드 최적화 상태를 분석해주세요.

제목: {data['title']}
메타 설명: {data['meta_description']}
콘텐츠: {data['content'][:1500]}...

다음 JSON 형태로 응답해주세요:
```json
{{
  "optimization_score": 85,
  "primary_keywords": ["주요 키워드1", "주요 키워드2"],
  "keyword_density": {{"키워드1": 2.5, "키워드2": 1.8}},
  "keyword_placement": {{
    "title": true,
    "meta_description": true,
    "headings": true,
    "content": true
  }},
  "keyword_stuffing_risk": "낮음/중간/높음",
  "natural_usage": true,
  "missing_opportunities": ["놓친 키워드1", "놓친 키워드2"],
  "recommendations": ["개선 제안1", "개선 제안2"]
}}
```
"""
        
        try:
            response = await self._make_api_call_with_retry(self._call_gemini_api, prompt)
            return self._parse_json_response(response, {"optimization_score": 50, "natural_usage": False, "keyword_stuffing_risk": "중간"})
        except Exception as e:
            return {"optimization_score": 50, "natural_usage": False, "keyword_stuffing_risk": f"분석 오류: {e}"}

    async def _analyze_image_optimization(self, data: Dict) -> Dict:
        """이미지 최적화 상태 분석"""
        images_info = []
        for img in data['images'][:10]:  # 처음 10개 이미지만 분석
            images_info.append({
                "src": img.get('src', ''),
                "alt": img.get('alt', ''),
                "title": img.get('title', '')
            })
        
        prompt = f"""
웹페이지의 이미지 최적화 상태를 SEO 관점에서 분석해주세요.

페이지 주제: {data['title']}
이미지 정보: {images_info}

다음 JSON 형태로 응답해주세요:
```json
{{
  "optimization_score": 85,
  "total_images": {len(data['images'])},
  "images_with_alt": 8,
  "alt_quality_score": 75,
  "descriptive_alts": 6,
  "keyword_relevant_alts": 4,
  "issues": ["문제점1", "문제점2"],
  "good_examples": ["좋은 alt 텍스트 예시1"],
  "poor_examples": ["개선이 필요한 alt 텍스트 예시1"],
  "recommendations": ["개선 제안1", "개선 제안2"]
}}
```
"""
        
        try:
            response = await self._make_api_call_with_retry(self._call_gemini_api, prompt)
            return self._parse_json_response(response, {"optimization_score": 50, "total_images": len(data['images'])})
        except Exception as e:
            return {"optimization_score": 50, "total_images": len(data['images']), "recommendations": [f"분석 오류: {e}"]}

    async def _analyze_structured_data_quality(self, data: Dict) -> Dict:
        """구조화 데이터의 콘텐츠 품질 분석"""
        structured_data_summary = []
        for sd in data['structured_data']:
            structured_data_summary.append({
                "type": sd.get('@type', 'Unknown'),
                "has_required_fields": bool(sd.get('name') or sd.get('headline') or sd.get('title')),
                "completeness": len(sd.keys())
            })
        
        prompt = f"""
웹페이지의 구조화 데이터 품질을 분석해주세요.

페이지 콘텐츠: {data['content'][:1000]}...
구조화 데이터: {structured_data_summary}

다음 JSON 형태로 응답해주세요:
```json
{{
  "quality_score": 85,
  "content_accuracy": 90,
  "completeness": 80,
  "schema_types": ["Article", "BreadcrumbList"],
  "accurate_schemas": ["정확한 스키마1"],
  "inaccurate_schemas": ["부정확한 스키마1"],
  "missing_schemas": ["추가하면 좋을 스키마1"],
  "content_mismatch": ["콘텐츠와 불일치하는 부분1"],
  "recommendations": ["개선 제안1", "개선 제안2"]
}}
```
"""
        
        try:
            response = await self._make_api_call_with_retry(self._call_gemini_api, prompt)
            return self._parse_json_response(response, {"quality_score": 50, "content_accuracy": 50})
        except Exception as e:
            return {"quality_score": 50, "content_accuracy": 50, "recommendations": [f"분석 오류: {e}"]}

    def _parse_content_quality_response(self, response: str) -> Dict:
        """콘텐츠 품질 분석 응답 파싱"""
        try:
            return self._parse_json_response(response, {
                "overall_score": 50,
                "summary": "AI 분석 실패",
                "strengths": [],
                "weaknesses": [],
                "recommendations": []
            })
        except Exception as e:
            return {
                "overall_score": 50,
                "summary": f"분석 실패: {e}",
                "strengths": [],
                "weaknesses": [],
                "recommendations": []
            }

    def _parse_json_response(self, response: str, default: Dict) -> Dict:
        """JSON 응답을 안전하게 파싱"""
        try:
            # JSON 블록 추출
            json_match = re.search(r"```json\s*([\s\S]*?)\s*```", response)
            if json_match:
                json_content = json_match.group(1)
            else:
                json_content = response
            
            parsed = json.loads(json_content)
            return parsed if isinstance(parsed, dict) else default
        except (json.JSONDecodeError, AttributeError):
            return default

    def _get_default_content_quality_result(self, error: str) -> Dict:
        """콘텐츠 품질 분석 실패 시 기본 결과"""
        return {
            "overall_content_quality": {
                "overall_score": 50,
                "summary": f"분석 실패: {error}",
                "recommendations": ["AI 분석을 다시 시도해주세요."]
            },
            "title_relevance": {"relevance_score": 50, "recommendation": "분석 실패"},
            "meta_description_quality": {"quality_score": 50, "recommendation": "분석 실패"},
            "heading_structure": {"structure_score": 50, "seo_impact": "불명"},
            "keyword_optimization": {"optimization_score": 50, "natural_usage": False},
            "image_optimization": {"optimization_score": 50, "recommendations": []},
            "structured_data_quality": {"quality_score": 50, "recommendations": []},
            "analysis_timestamp": time.strftime("%Y-%m-%d %H:%M:%S")
        }

    # 사이트 구조 및 사용자 경험 분석
    async def analyze_site_structure_ux(self, url: str, website_data: Dict) -> Dict:
        """사이트 구조 및 사용자 경험 AI 분석"""
        try:
            analysis_result = await self._analyze_site_structure_with_data(url, website_data)
            return analysis_result
        except Exception as e:
            logger.error(f"사이트 구조 분석 중 오류 발생: {str(e)}")
            return self._get_default_site_structure_result(str(e))

    async def _analyze_site_structure_with_data(self, url: str, website_data: Dict) -> Dict:
        """웹사이트 데이터를 사용한 사이트 구조 분석"""
        try:
            # AI 분석 요청
            ai_result = await self._request_site_structure_analysis(url, website_data)
            
            # 응답 검증 및 정규화
            validated_result = await self._validate_site_structure_response(ai_result, url)
            
            return validated_result
            
        except Exception as e:
            logger.error(f"사이트 구조 데이터 분석 실패: {str(e)}")
            return self._get_default_site_structure_result(str(e))

    async def _request_site_structure_analysis(self, url: str, website_data: Dict) -> Dict:
        """AI에게 사이트 구조 분석 요청"""
        prompt = self._create_site_structure_analysis_prompt(url, website_data)
        
        try:
            response = await self._call_gemini_api(prompt)
            return response
        except Exception as e:
            logger.error(f"사이트 구조 AI 분석 요청 실패: {str(e)}")
            raise

    def _create_site_structure_analysis_prompt(self, url: str, website_data: Dict) -> str:
        """사이트 구조 분석을 위한 AI 프롬프트 생성"""
        
        # 웹사이트 데이터에서 관련 정보 추출
        sitemap_urls = website_data.get('sitemap_urls', [])
        main_content = website_data.get('main_page_content', '')
        internal_links = website_data.get('internal_links', [])
        navigation_structure = website_data.get('navigation', {})
        anchor_texts = website_data.get('anchor_texts', [])
        url_structure = website_data.get('url_structure_analysis', {})
        depth_analysis = website_data.get('depth_analysis', {})
        
        prompt = f"""
다음 웹사이트의 사이트 구조 및 사용자 경험을 분석해주세요:

웹사이트 URL: {url}

분석 데이터:
1. 사이트맵 URL 수: {len(sitemap_urls)}개
2. 내부 링크 수: {len(internal_links)}개
3. 메인 페이지 콘텐츠 길이: {len(main_content)}자
4. 앵커 텍스트 수: {len(anchor_texts)}개
5. URL 구조 분석: {url_structure}

네비게이션 구조:
{navigation_structure}

내부 링크 샘플 (최대 20개):
{internal_links[:20]}

사이트맵 URL 샘플 (최대 20개):
{sitemap_urls[:20]}

앵커 텍스트 샘플 (최대 15개):
{anchor_texts[:15]}

URL 깊이 분석 (샘플):
{dict(list(depth_analysis.items())[:10]) if depth_analysis else "깊이 분석 데이터 없음"}

다음 항목들을 분석해주세요:

1. **평균 깊이 분석 (≤4 클릭)**
   - URL 구조를 통한 페이지 깊이 계산
   - 홈페이지에서 각 페이지까지의 클릭 수 추정
   - BFS(너비 우선 탐색) 기반 평균 깊이 계산

2. **고아 페이지 검출 (0건)**
   - 사이트맵에는 있지만 내부 링크가 없는 페이지
   - 내부 링크 그래프에서 연결이 끊어진 페이지
   - 접근할 수 없는 페이지 식별

3. **앵커 텍스트 다양성 (≥0.3)**
   - 앵커 텍스트의 n-gram 엔트로피 계산
   - 반복되는 앵커 텍스트 패턴 분석
   - 다양성 점수 계산 (0~1 범위)

4. **메인 네비게이션 계층 구조 (상위 3계층)**
   - 주 메뉴의 계층 구조 분석
   - 카테고리 분류의 명확성
   - 사용자 친화적 구조 평가

5. **컨텍스트 링크 비율 (본문 >70%)**
   - 본문 내 링크와 네비게이션/푸터 링크 구분
   - 컨텍스트가 있는 링크의 비율 계산
   - 링크의 의미적 연관성 분석

6. **동적 콘텐츠 크롤링 관리**
   - 필터/정렬 URL의 SEO 영향 분석
   - noindex 태그 적용 필요성
   - Ajax 로딩 콘텐츠 처리 방안

응답은 반드시 다음 JSON 형식으로 제공해주세요:

{{
    "overall_score": 점수(0-100),
    "analysis_results": {{
        "average_depth": {{
            "score": 점수(0-100),
            "current_depth": 현재_평균_깊이,
            "target_depth": 4,
            "status": "통과" 또는 "실패",
            "details": "분석 세부사항"
        }},
        "orphan_pages": {{
            "score": 점수(0-100),
            "orphan_count": 고아_페이지_수,
            "target_count": 0,
            "status": "통과" 또는 "실패",
            "orphan_urls": ["고아페이지URL들"],
            "details": "분석 세부사항"
        }},
        "anchor_diversity": {{
            "score": 점수(0-100),
            "diversity_score": 다양성_점수,
            "target_score": 0.3,
            "status": "통과" 또는 "실패",
            "common_anchors": ["자주_사용되는_앵커들"],
            "details": "분석 세부사항"
        }},
        "navigation_hierarchy": {{
            "score": 점수(0-100),
            "hierarchy_levels": 계층_수,
            "target_levels": 3,
            "status": "통과" 또는 "실패",
            "structure_analysis": "구조_분석_결과",
            "details": "분석 세부사항"
        }},
        "contextual_links": {{
            "score": 점수(0-100),
            "contextual_ratio": 컨텍스트_링크_비율,
            "target_ratio": 0.7,
            "status": "통과" 또는 "실패",
            "total_links": 전체_링크_수,
            "contextual_links": 컨텍스트_링크_수,
            "details": "분석 세부사항"
        }},
        "facet_crawl_management": {{
            "score": 점수(0-100),
            "filter_urls_found": 필터_URL_수,
            "noindex_applied": noindex_적용여부,
            "ajax_loading": ajax_로딩_사용여부,
            "status": "통과" 또는 "실패",
            "details": "분석 세부사항"
        }}
    }},
    "recommendations": [
        "개선사항1",
        "개선사항2",
        "개선사항3"
    ],
    "priority_actions": [
        "우선순위1",
        "우선순위2"
    ]
}}

분석 시 다음 기준을 적용해주세요:
- 100점: 모든 기준을 완벽히 충족
- 80-99점: 대부분 기준 충족, 소수 개선사항
- 60-79점: 기본 기준 충족, 일부 개선 필요
- 40-59점: 기준 미달, 상당한 개선 필요
- 0-39점: 심각한 문제, 즉시 개선 필요

반드시 JSON 형식만 응답해주세요.
"""
        return prompt

    async def _validate_site_structure_response(self, ai_result: Dict, original_url: str) -> Dict:
        """사이트 구조 분석 응답 검증 및 정규화"""
        try:
            if not ai_result or 'analysis_results' not in ai_result:
                logger.warning("사이트 구조 분석 응답이 올바르지 않음")
                return self._get_default_site_structure_result("응답 형식 오류")
                
            # 필수 필드 검증
            required_fields = ['average_depth', 'orphan_pages', 'anchor_diversity', 
                             'navigation_hierarchy', 'contextual_links', 'facet_crawl_management']
            
            analysis_results = ai_result.get('analysis_results', {})
            for field in required_fields:
                if field not in analysis_results:
                    logger.warning(f"사이트 구조 분석에서 {field} 필드 누락")
                    analysis_results[field] = {
                        "score": 50,
                        "status": "분석실패",
                        "details": "데이터 부족으로 분석 불가"
                    }
            
            # 점수 정규화 (0-100)
            for field_name, field_data in analysis_results.items():
                if isinstance(field_data, dict) and 'score' in field_data:
                    score = field_data['score']
                    if not isinstance(score, (int, float)) or score < 0 or score > 100:
                        analysis_results[field_name]['score'] = 50
            
            # 전체 점수 계산
            total_score = sum(result.get('score', 50) for result in analysis_results.values()) / len(analysis_results)
            ai_result['overall_score'] = round(total_score)
            
            # 기본값 설정
            if 'recommendations' not in ai_result:
                ai_result['recommendations'] = ["사이트 구조 최적화를 위한 추가 분석이 필요합니다."]
            if 'priority_actions' not in ai_result:
                ai_result['priority_actions'] = ["핵심 페이지의 접근성을 개선하세요."]
                
            return ai_result
            
        except Exception as e:
            logger.error(f"사이트 구조 응답 검증 실패: {str(e)}")
            return self._get_default_site_structure_result(str(e))

    def _get_default_site_structure_result(self, error: str = None) -> Dict:
        """사이트 구조 분석 기본 결과 반환"""
        return {
            "overall_score": 60,  # 기술적 분석 가능 항목을 고려하여 상향
            "analysis_results": {
                "average_depth": {
                    "score": 70,  # 기술적 분석 가능하므로 기본점수 상향
                    "current_depth": 0,
                    "target_depth": 4,
                    "status": "기술적분석가능",
                    "details": "기술적 분석으로 대체 가능" if not error else f"AI 분석 실패하지만 기술적 분석 가능: {error}"
                },
                "orphan_pages": {
                    "score": 60,  # 부분적 기술적 분석 가능
                    "orphan_count": 0,
                    "target_count": 0,
                    "status": "부분분석가능",
                    "orphan_urls": [],
                    "details": "네비게이션 기반 부분 분석 가능" if not error else f"AI 분석 실패: {error}"
                },
                "anchor_diversity": {
                    "score": 70,  # 기술적 분석 가능
                    "diversity_score": 0,
                    "target_score": 0.3,
                    "status": "기술적분석가능",
                    "common_anchors": [],
                    "details": "기술적 분석으로 대체 가능" if not error else f"AI 분석 실패하지만 기술적 분석 가능: {error}"
                },
                "navigation_hierarchy": {
                    "score": 50,  # AI 분석 의존적
                    "hierarchy_levels": 0,
                    "target_levels": 3,
                    "status": "AI분석필요",
                    "structure_analysis": "AI 분석 필요",
                    "details": "복잡한 네비게이션 구조 분석은 AI 분석 필요" if not error else f"분석 실패: {error}"
                },
                "contextual_links": {
                    "score": 50,  # AI 분석 의존적
                    "contextual_ratio": 0,
                    "target_ratio": 0.7,
                    "status": "AI분석필요",
                    "total_links": 0,
                    "contextual_links": 0,
                    "details": "컨텍스트 링크 분석은 AI 분석 필요" if not error else f"분석 실패: {error}"
                },
                "facet_crawl_management": {
                    "score": 50,  # AI 분석 의존적
                    "filter_urls_found": 0,
                    "noindex_applied": False,
                    "ajax_loading": False,
                    "status": "AI분석필요",
                    "details": "동적 콘텐츠 관리 분석은 AI 분석 필요" if not error else f"분석 실패: {error}"
                }
            },
            "recommendations": [
                "기술적 분석 결과를 우선 활용하여 개선점을 파악합니다.",
                "AI 분석이 실패한 항목은 수동 검토를 권장합니다.",
                "내부 링크 구조와 네비게이션 개선에 집중하세요."
            ],
            "priority_actions": [
                "기술적으로 측정 가능한 항목들부터 우선 개선하세요.",
                "사이트 구조의 기본적인 접근성을 확보하세요."
            ]
        }