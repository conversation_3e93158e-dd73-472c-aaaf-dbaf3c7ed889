const { createProxyMiddleware } = require("http-proxy-middleware");

module.exports = function (app) {
  // API 요청을 백엔드 서버로 프록시
  app.use(
    "/api",
    createProxyMiddleware({
      target: "http://localhost:8000",
      changeOrigin: true,
      secure: false, // HTTPS 환경에서 HTTP 백엔드 허용
      pathRewrite: {
        "^/api": "", // /api 경로를 제거하고 백엔드로 전달
      },
      logLevel: "debug", // 개발 중 디버깅용
    })
  );
};
