.ai-section-suggestions {
  margin-top: 1.5rem;
  padding: 1rem;
  background-color: #f8f8f8;
  border-top: 2px dashed #d1d5db;
  border-radius: 0 0 8px 8px;

  h5 {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 1rem;
    font-weight: 600;
    color: #4b5563;
    margin: 0 0 0.75rem 0;
  }

  ul {
    list-style: none;
    padding: 0;
    margin: 0;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;

    li {
      font-size: 0.9rem;
      color: #374151;
      line-height: 1.6;
      padding: 0.75rem;
      border-radius: 6px;
      border-left: 4px solid #d1d5db;

      // 중요도별 색상 구분 (3단계)
      &.importance-high {
        border-left-color: #ef4444;
        background-color: #fef2f2;
      }

      &.importance-medium {
        border-left-color: #f59e0b;
        background-color: #fffbeb;
      }

      &.importance-low {
        border-left-color: #10b981;
        background-color: #f0fdf4;
      }

      .suggestion-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 0.5rem;
        gap: 1rem;

        strong {
          font-weight: 600;
          color: #111827;
          flex: 1;
        }

        .suggestion-meta {
          display: flex;
          flex-direction: column;
          align-items: flex-end;
          gap: 0.25rem;
          min-width: fit-content;

          .importance-badge {
            font-size: 0.75rem;
            font-weight: 600;
            padding: 0.25rem 0.5rem;
            border-radius: 12px;
            color: white;
          }
        }
      }

      // 중요도별 배지 색상
      &.importance-high .importance-badge {
        background-color: #ef4444;
      }

      &.importance-medium .importance-badge {
        background-color: #f59e0b;
      }

      &.importance-low .importance-badge {
        background-color: #10b981;
      }

      .suggestion-content {
        color: #4b5563;
        line-height: 1.5;
        margin-bottom: 0.5rem;
      }

      .expected-impact {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        font-size: 0.8rem;
        color: #059669;
        font-weight: 500;
        padding-top: 0.5rem;
        border-top: 1px solid #e5e7eb;
        margin-top: 0.5rem;

        svg {
          color: #059669;
        }
      }
    }
  }
}

.section-container {
  margin-bottom: 2rem;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  overflow: hidden;
  background: white;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  background: #f8f9fa;
  border: 1px solid #e5e7eb;
  border-radius: 8px 8px 0 0;
  cursor: pointer;
  transition: background-color 0.2s ease;

  &:hover {
    background: #f1f3f4;
  }
}

.section-header-info {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.section-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: #374151;
  margin: 0;
}

.section-score {
  font-size: 1rem;
  font-weight: 600;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  color: white;

  &.excellent {
    background: #10b981;
  }

  &.good {
    background: #3b82f6;
  }

  &.warning {
    background: #f59e0b;
  }

  &.danger {
    background: #ef4444;
  }
}

.section-toggle {
  color: #6b7280;
  transition: color 0.2s ease;

  &:hover {
    color: #374151;
  }
}

.checklist-table-wrapper {
  padding: 1rem;
}

.checklist-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 0.9rem;

  th,
  td {
    padding: 0.75rem;
    text-align: left;
    border-bottom: 1px solid #e5e7eb;
  }

  th {
    background: #f9fafb;
    font-weight: 600;
    color: #374151;
  }

  td {
    color: #4b5563;
  }

  tr:hover {
    background: #f9fafb;
  }
}

.category-scores {
  margin-top: 2rem;
}

.category-item {
  margin-bottom: 2rem;
}

.category-detail {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.status-pass {
  color: #10b981;
  width: 20px;
  height: 20px;
}

.status-fail {
  color: #ef4444;
  width: 20px;
  height: 20px;
}

.status-warning {
  color: #f59e0b;
  width: 20px;
  height: 20px;
}

.status-pending {
  color: #6b7280;
  width: 20px;
  height: 20px;
}
