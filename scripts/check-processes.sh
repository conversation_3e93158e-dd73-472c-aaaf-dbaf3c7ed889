#!/bin/bash

echo "🔍 프로세스 상태 확인 (디버깅용)"
echo "================================"

# 핵심 프로세스만 간단히 확인
echo "📊 현재 프로세스 수:"
echo "- 브라우저: $(ps aux | grep -E "(playwright|chromium|chrome)" | grep -v grep | wc -l)"
echo "- 포트 4000: $(lsof -ti:4000 2>/dev/null | wc -l)"

# 프로세스가 있을 때만 상세 정보 표시
browser_count=$(ps aux | grep -E "(playwright|chromium|chrome)" | grep -v grep | wc -l)
if [ "$browser_count" -gt 0 ]; then
    echo ""
    echo "📱 실행 중인 브라우저 프로세스:"
    ps aux | grep -E "(playwright|chromium|chrome)" | grep -v grep | head -5
    echo ""
    echo "🚨 응급 정리: npm run test:e2e:emergency-cleanup"
else
    echo ""
    echo "✅ 모든 프로세스가 정상적으로 정리되었습니다."
fi
