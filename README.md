# 🔍 AI 기반 SEO 분석 도구 (xe-seo)

웹사이트의 SEO 상태를 종합적으로 분석하고 Google Gemini AI가 제안하는 맞춤형 개선 방안을 제공하는 도구입니다.

## ✨ 주요 기능

### 📊 종합 SEO 분석

- **기본 SEO 요소**: 메타태그, 제목, 설명, 헤딩 구조 분석
- **이미지 최적화**: Alt 텍스트, 접근성 분석
- **링크 구조**: 내부/외부 링크 분석
- **성능 측정**: 페이지 로딩 속도 및 Core Web Vitals
- **기술적 SEO**: SSL, 스키마 마크업, 모바일 최적화
- **콘텐츠 분석**: 키워드 밀도, 읽기 시간, 콘텐츠 품질

### 🤖 AI 기반 개선 제안

- **Google Gemini AI**: 고도화된 SEO 분석 및 개선 제안
- **카테고리별 점수**: 콘텐츠 품질, 기술적 SEO, 사용자 경험, 모바일 최적화, 성능
- **우선순위 기반 개선안**: 높음/중간/낮음 우선순위별 실행 가능한 개선 방안
- **키워드 제안**: 타겟 키워드 및 콘텐츠 개선 방안

### 📱 추가 분석 기능

- **모바일 최적화**: 반응형 디자인, 터치 요소 분석
- **접근성 분석**: ARIA 라벨, 폼 요소, 접근성 점수
- **보안 분석**: HTTPS, 혼합 콘텐츠, CSP 헤더
- **국제화 분석**: 다국어 지원, hreflang 태그

## 🛠 기술 스택

### 백엔드

- **Python 3.9+**
- **FastAPI**: 고성능 비동기 웹 API 프레임워크
- **BeautifulSoup4**: HTML 파싱 및 DOM 분석
- **Selenium**: 동적 콘텐츠 크롤링 및 성능 측정
- **Google Generative AI**: Gemini 2.0 Flash 모델 기반 AI 분석
- **Pydantic**: 데이터 검증 및 시리얼라이제이션
- **Uvicorn**: ASGI 서버

### 프론트엔드

- **React 19**: 최신 React 기반 사용자 인터페이스
- **TypeScript**: 타입 안전성 및 개발자 경험 향상
- **SCSS Modules**: 모듈화된 스타일링
- **React Router**: 클라이언트 사이드 라우팅
- **Axios**: HTTP 클라이언트
- **Lucide React**: 아이콘 라이브러리
- **Recharts**: 데이터 시각화

## 🚀 설치 및 실행

### 1. 프로젝트 클론

```bash
git clone https://github.com/your-username/xe-seo.git
cd xe-seo
```

### 2. 백엔드 설정

#### Python 가상환경 생성 및 활성화

```bash
# Python 3.9 사용 (pyenv 사용 시)
pyenv install 3.9.18
pyenv local 3.9.18

# 가상환경 생성
python -m venv venv

# 가상환경 활성화
source venv/bin/activate  # macOS/Linux
# 또는
venv\Scripts\activate     # Windows
```

#### 의존성 설치

```bash
pip install -r requirements.txt
```

#### 환경 변수 설정

```bash
# .env 파일 생성
touch .env
```

`.env` 파일에 다음 내용 추가:

```env
# Google Gemini API 키 (필수)
GOOGLE_API_KEY=AIzaSyCPxGcKuDmCMFzQeR3oPwfotksfz36nfyI

# PageSpeed Insights API 키 (필수)
PAGESPEED_API_KEY=AIzaSyAYOr2PbkSvVWv_v-fyi4xaF7shOfMRRfY

# 환경 설정
ENVIRONMENT=development

# CORS 설정 (선택사항)
CORS_ORIGINS=http://localhost:4000,http://localhost:3000
```

#### Chrome 및 ChromeDriver 설치

```bash
# Chrome 설치 스크립트 실행 (Linux/macOS)
chmod +x install-chrome.sh
./install-chrome.sh

# 또는 수동 설치
# - Chrome 브라우저 설치
# - ChromeDriver 다운로드 및 PATH 설정
```

#### 백엔드 서버 실행

```bash
python main.py
```

서버는 `http://localhost:8000`에서 실행됩니다.

### 3. 프론트엔드 설정

#### Node.js 버전 설정

```bash
# 프론트엔드 디렉토리로 이동
cd frontend

# Node.js 20.10.0 사용 권장 (nvm 사용 시)
nvm use 20.10.0
```

#### 의존성 설치

```bash
npm install
```

#### 환경 변수 설정

```bash
# .env.local 파일 생성
touch .env.local
```

`.env.local` 파일에 다음 내용 추가:

```env
REACT_APP_API_URL=http://localhost:8000
```

#### 프론트엔드 개발 서버 실행

```bash
npm start
```

앱은 `http://localhost:4000`에서 실행됩니다.

## 📝 API 키 발급 방법

### Google Gemini API 키

1. [Google AI Studio](https://aistudio.google.com/) 접속
2. Google 계정으로 로그인
3. "Get API Key" 클릭하여 새 프로젝트 생성
4. API 키 생성 후 `.env` 파일에 추가

## 🔧 사용 방법

1. 백엔드 서버 실행: `python main.py`
2. 프론트엔드 서버 실행: `npm start` (frontend 디렉토리에서)
3. 웹 브라우저에서 `http://localhost:4000` 접속
4. 분석하고 싶은 웹사이트 URL 입력
5. "SEO 분석 시작" 버튼 클릭
6. AI 분석 완료 후 상세한 결과 리포트 확인

## 📊 API 엔드포인트

### GET /

- **설명**: API 상태 확인
- **응답**: 서버 상태 및 버전 정보

### POST /analyze

- **설명**: SEO 분석 수행
- **요청 본문**:
  ```json
  {
    "url": "https://example.com",
    "ai_provider": "gemini"
  }
  ```
- **응답**: SEO 분석 결과 (점수, 카테고리별 분석, AI 제안사항)

## 🏗 프로젝트 구조

```
xe-seo/
├── main.py                    # FastAPI 메인 애플리케이션
├── seo_analyzer.py            # SEO 분석 모듈
├── ai_analyzer.py             # AI 분석 모듈 (Gemini)
├── requirements.txt           # Python 의존성
├── .env                       # 환경 변수 (gitignore됨)
├── Dockerfile                 # Docker 컨테이너 설정
├── docker-compose.yml         # Docker Compose 설정
├── deploy-aws.sh             # AWS 배포 스크립트
├── nginx-seo.conf            # Nginx 설정
└── frontend/                  # React 프론트엔드
    ├── src/
    │   ├── components/        # React 컴포넌트
    │   │   ├── Header/        # 헤더 컴포넌트
    │   │   ├── LoadingSpinner/ # 로딩 스피너
    │   │   ├── SEOReport/     # SEO 리포트 컴포넌트
    │   │   └── URLForm/       # URL 입력 폼
    │   ├── pages/             # 페이지 컴포넌트
    │   │   ├── HomePage/      # 홈 페이지
    │   │   └── ResultPage/    # 결과 페이지
    │   ├── services/          # API 서비스
    │   │   └── api.ts         # API 클라이언트
    │   └── types/             # TypeScript 타입 정의
    │       └── index.ts       # 타입 정의
    ├── package.json           # Node.js 의존성
    └── .env.local             # 프론트엔드 환경 변수
```

## 🚀 배포

### Docker를 사용한 배포

```bash
# Docker 이미지 빌드
docker build -t xe-seo .

# Docker Compose로 실행
docker-compose up -d
```

### AWS EC2 배포

```bash
# AWS 배포 스크립트 실행
chmod +x deploy-aws.sh
./deploy-aws.sh
```

자세한 배포 가이드는 [DEPLOYMENT.md](DEPLOYMENT.md)를 참조하세요.

## 📊 분석 항목

### 기본 SEO 분석

- 페이지 제목 (길이, 존재 여부, 최적화)
- 메타 설명 (길이, 존재 여부, 최적화)
- 메타 키워드
- 헤딩 구조 (H1-H6, 계층 구조)
- 이미지 최적화 (Alt 텍스트, 접근성)
- 링크 구조 (내부/외부 링크)
- 페이지 성능 (로딩 시간, DOM 로드 시간)
- 소셜 미디어 메타 태그 (Open Graph, Twitter Cards)
- 기술적 SEO (SSL, Canonical URL, Viewport 등)

### AI 분석 (Gemini 2.0 Flash)

- **콘텐츠 품질**: 제목, 메타 설명, 헤딩 구조 분석
- **기술적 SEO**: SSL, 스키마 마크업, 메타 태그 최적화
- **사용자 경험**: 페이지 구조, 네비게이션, 시맨틱 HTML
- **모바일 최적화**: 반응형 디자인, 뷰포트 설정
- **성능**: 로딩 속도, 이미지 최적화

### 점수 체계

- **종합 점수**: 0-100점 (카테고리별 가중평균)
- **카테고리별 점수**: 각 영역별 세부 점수
- **개선 우선순위**: 높음/중간/낮음 3단계
- **예상 효과**: 각 개선사항별 SEO 효과 예측

## 🔧 문제 해결

### 일반적인 문제

1. **Chrome 드라이버 오류**

   ```bash
   # Chrome 설치 스크립트 재실행
   ./install-chrome.sh
   ```

2. **포트 충돌**

   ```bash
   # 포트 사용 중인 프로세스 확인
   lsof -i :8000  # 백엔드
   lsof -i :4000  # 프론트엔드
   ```

3. **API 키 오류**

   ```bash
   # 환경 변수 확인
   echo $GOOGLE_API_KEY
   ```

4. **CORS 오류**
   - 백엔드 CORS 설정 확인
   - 프론트엔드 API URL 확인

### 로그 확인

```bash
# 백엔드 로그
python main.py  # 개발 모드

# 프론트엔드 로그
npm start  # 개발 모드
```

## 🤝 기여하기

1. 이 저장소를 포크합니다
2. 기능 브랜치를 생성합니다 (`git checkout -b feature/AmazingFeature`)
3. 변경 사항을 커밋합니다 (`git commit -m 'Add some AmazingFeature'`)
4. 브랜치에 푸시합니다 (`git push origin feature/AmazingFeature`)
5. Pull Request를 생성합니다

## 📄 라이선스

이 프로젝트는 MIT 라이선스 하에 배포됩니다.

## ⚠️ 주의사항

- **Chrome 브라우저와 ChromeDriver**: 웹 크롤링을 위해 필수 설치
- **Google Gemini API 키**: AI 분석을 위해 필수 설정
- **크롤링 제한**: 일부 웹사이트는 크롤링을 제한할 수 있습니다
- **분석 시간**: 웹사이트 복잡도에 따라 1-3분 소요
- **API 사용량**: Google Gemini API 사용량에 따른 과금 가능

## 📞 지원

문제가 발생하면 다음을 확인하세요:

1. [Issues](https://github.com/your-username/xe-seo/issues)에서 유사한 문제 검색
2. 환경 변수 설정 확인
3. 의존성 설치 상태 확인
4. Chrome 및 ChromeDriver 설치 확인
