# SEO 분석 도구 배포 가이드

## 백엔드 배포 (AWS)

### 1. AWS EC2 배포

#### 인스턴스 설정

1. AWS EC2 인스턴스 생성 (Ubuntu 20.04 LTS 권장)
2. 보안 그룹에서 포트 8000, 80, 443 열기
3. SSH로 인스턴스 접속

#### 배포 스크립트 실행

```bash
# 프로젝트 클론
git clone <your-repo-url>
cd seo

# 배포 스크립트 실행
chmod +x deploy-aws.sh
./deploy-aws.sh

# 환경변수 설정
sudo nano /etc/environment
# 다음 내용 추가:
# GEMINI_API_KEY=your_gemini_api_key
# ENVIRONMENT=production
```

#### 서비스 관리

```bash
# 서비스 상태 확인
sudo systemctl status seo-backend

# 서비스 재시작
sudo systemctl restart seo-backend

# 로그 확인
sudo journalctl -u seo-backend -f
```

### 2. AWS ECS (Docker) 배포

#### Docker 이미지 빌드 및 ECR 푸시

```bash
# AWS CLI 설정
aws configure

# ECR 레포지토리 생성
aws ecr create-repository --repository-name seo-analyzer

# Docker 이미지 빌드
docker build -t seo-analyzer .

# ECR에 푸시
aws ecr get-login-password --region ap-northeast-2 | docker login --username AWS --password-stdin <account-id>.dkr.ecr.ap-northeast-2.amazonaws.com
docker tag seo-analyzer:latest <account-id>.dkr.ecr.ap-northeast-2.amazonaws.com/seo-analyzer:latest
docker push <account-id>.dkr.ecr.ap-northeast-2.amazonaws.com/seo-analyzer:latest
```

#### ECS 서비스 생성

1. ECS 클러스터 생성
2. 태스크 정의 생성 (환경변수 포함)
3. 서비스 생성 및 배포

### 3. AWS Lambda (서버리스)

#### Serverless Framework 사용

```bash
# Serverless 설치
npm install -g serverless

# AWS 자격 증명 설정
serverless config credentials --provider aws --key <access-key> --secret <secret-key>

# 배포
serverless deploy
```

## 프론트엔드 배포

### 1. AWS S3 + CloudFront

#### S3 정적 웹사이트 호스팅

```bash
# 빌드
cd frontend
npm run build

# S3 버킷 생성 및 업로드
aws s3 mb s3://your-bucket-name
aws s3 sync build/ s3://your-bucket-name --delete
```

#### CloudFront 배포

1. CloudFront 배포 생성
2. S3 버킷을 오리진으로 설정
3. 사용자 정의 도메인 설정 (선택사항)

### 2. Vercel (대안)

```bash
cd frontend
npm run build
vercel --prod
```

환경변수 설정:

```
REACT_APP_API_URL=https://seoapi.pxd.co.kr
```

## 환경변수 설정

### 백엔드 환경변수

```
GEMINI_API_KEY=your_gemini_api_key
ENVIRONMENT=production
PRODUCTION_DOMAIN=seoapi.pxd.co.kr
```

### 프론트엔드 환경변수

```
REACT_APP_API_URL=https://seoapi.pxd.co.kr
REACT_APP_ENVIRONMENT=production
```

## 보안 설정

### SSL 인증서 (Let's Encrypt)

```bash
# Certbot 설치
sudo apt install certbot python3-certbot-nginx

# SSL 인증서 발급
sudo certbot --nginx -d your-domain.com

# 자동 갱신 설정
sudo crontab -e
# 다음 라인 추가: 0 12 * * * /usr/bin/certbot renew --quiet
```

### AWS Security Groups

- 포트 22 (SSH): 관리자 IP만 허용
- 포트 80, 443 (HTTP/HTTPS): 모든 IP 허용
- 포트 8000 (백엔드): 프론트엔드 도메인만 허용

## 모니터링 및 로그

### CloudWatch 설정

1. EC2 인스턴스에 CloudWatch 에이전트 설치
2. 로그 그룹 생성
3. 알람 설정 (CPU, 메모리, 디스크 사용률)

### 백업 설정

```bash
# 데이터베이스 백업 (필요시)
# 애플리케이션 로그 백업
aws s3 sync /var/log/seo-backend/ s3://your-backup-bucket/logs/
```

## 배포 후 확인사항

- [ ] 백엔드 API 응답 확인: `GET https://your-domain.com/`
- [ ] 프론트엔드에서 백엔드 API 호출 성공
- [ ] SEO 분석 기능 정상 작동
- [ ] SSL 인증서 정상 적용
- [ ] 환경변수 올바르게 설정됨
- [ ] CloudWatch 모니터링 작동
- [ ] 보안 그룹 및 방화벽 설정 확인

## 트러블슈팅

### 일반적인 문제

1. **Chrome 드라이버 문제**: `install-chrome.sh` 스크립트 재실행
2. **메모리 부족**: EC2 인스턴스 타입 업그레이드
3. **CORS 오류**: 백엔드 CORS 설정 확인
4. **환경변수 미적용**: 서비스 재시작 필요

### 로그 확인

```bash
# 애플리케이션 로그
sudo journalctl -u seo-backend -f

# Nginx 로그
sudo tail -f /var/log/nginx/error.log
sudo tail -f /var/log/nginx/access.log

# 시스템 로그
sudo tail -f /var/log/syslog
```
