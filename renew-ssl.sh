#!/bin/bash

# SSL 인증서 갱신 스크립트

echo "=== SSL 인증서 갱신을 시작합니다 ==="

# 1. 인증서 갱신 시도
echo "1. 인증서 갱신 확인 중..."
sudo certbot renew --dry-run

if [ $? -eq 0 ]; then
    echo "✅ 인증서 갱신 테스트 성공!"
    
    # 2. 실제 갱신 실행
    echo "2. 인증서 갱신 실행..."
    sudo certbot renew
    
    # 3. nginx 재시작
    echo "3. nginx 컨테이너 재시작..."
    docker-compose restart nginx
    
    echo "✅ SSL 인증서 갱신이 완료되었습니다!"
else
    echo "❌ 인증서 갱신에 실패했습니다."
    exit 1
fi
