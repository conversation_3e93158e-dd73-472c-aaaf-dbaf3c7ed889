import asyncio
from typing import List, Dict
from bs4 import BeautifulSoup

class WebPerformanceAnalyzer:
    def __init__(self, url: str, soup: BeautifulSoup, headers: Dict, pagespeed_results: Dict):
        self.url = url
        self.soup = soup
        self.headers = headers
        self.pagespeed_results = pagespeed_results

    async def analyze(self, checks_config: List[Dict], ai_analyzer) -> List[Dict]:
        try:
            technical_results = self._perform_technical_checks(checks_config)
            ai_results = await self._perform_ai_checks(ai_analyzer)
            # 직접 결합 로직 구현
            combined_results = self._combine_technical_and_ai_results(technical_results, ai_results, checks_config)
            return combined_results
        except Exception as e:
            results = []
            for item in checks_config:
                result = item.copy()
                result.update({
                    "status": "fail",
                    "message": f"분석 오류: {str(e)}",
                    "score": 0,
                    "title": item.get("title", "Unknown"),
                    "importance": item.get("importance", 1),
                    "criteria": item.get("criteria", "분석 중 오류 발생")
                })
                results.append(result)
            return results

    def _combine_technical_and_ai_results(self, technical_results: List[Dict], ai_results: Dict, checks_config: List[Dict]) -> List[Dict]:
        """기술적 분석과 AI 분석 결과를 결합합니다."""
        combined_results = []
        
        for i, item in enumerate(checks_config):
            technical_result = technical_results[i] if i < len(technical_results) else item.copy()
            
            # technical_result에 'id' 필드가 없으면 원본 item에서 복사
            if 'id' not in technical_result and 'id' in item:
                technical_result['id'] = item['id']
            
            # 필수 필드들 보장
            if 'title' not in technical_result:
                technical_result['title'] = item.get('title', 'Unknown')
            if 'importance' not in technical_result:
                technical_result['importance'] = item.get('importance', 1)
            if 'criteria' not in technical_result:
                technical_result['criteria'] = item.get('criteria', '분석 중 오류 발생')
            
            # AI 분석 결과가 있으면 보완
            if ai_results and not ai_results.get("error"):
                ai_enhanced_result = self._enhance_with_ai_insights(technical_result, ai_results, item)
                combined_results.append(ai_enhanced_result)
            else:
                combined_results.append(technical_result)
        
        return combined_results

    def _enhance_with_ai_insights(self, technical_result: Dict, ai_results: Dict, item: Dict) -> Dict:
        """AI 인사이트로 기술적 결과를 보완합니다."""
        enhanced_result = technical_result.copy()
        
        # AI 분석에서 해당 항목에 대한 정보 찾기
        performance_metrics = ai_results.get("performance_metrics", {})
        check_title = item.get("title", "").lower()
        
        # AI 분석 결과에서 해당 항목 찾기
        ai_evidence = ""
        ai_improvement = ""
        
        if "lcp" in check_title and "lcp" in performance_metrics:
            lcp_data = performance_metrics["lcp"]
            ai_evidence = lcp_data.get("evidence", "")
            ai_improvement = lcp_data.get("improvement", "")
        elif "inp" in check_title and "inp" in performance_metrics:
            inp_data = performance_metrics["inp"]
            ai_evidence = inp_data.get("evidence", "")
            ai_improvement = inp_data.get("improvement", "")
        elif "cls" in check_title and "cls" in performance_metrics:
            cls_data = performance_metrics["cls"]
            ai_evidence = cls_data.get("evidence", "")
            ai_improvement = cls_data.get("improvement", "")
        elif "ttfb" in check_title and "ttfb" in performance_metrics:
            ttfb_data = performance_metrics["ttfb"]
            ai_evidence = ttfb_data.get("evidence", "")
            ai_improvement = ttfb_data.get("improvement", "")
        elif "모바일" in check_title and "mobile_friendly" in performance_metrics:
            mobile_data = performance_metrics["mobile_friendly"]
            ai_evidence = mobile_data.get("evidence", "")
            ai_improvement = mobile_data.get("improvement", "")
        elif "페이지 크기" in check_title and "page_size" in performance_metrics:
            size_data = performance_metrics["page_size"]
            ai_evidence = size_data.get("evidence", "")
            ai_improvement = size_data.get("improvement", "")
        elif "이미지" in check_title and "image_optimization" in performance_metrics:
            image_data = performance_metrics["image_optimization"]
            ai_evidence = image_data.get("evidence", "")
            ai_improvement = image_data.get("improvement", "")
        
        # AI 분석 결과가 있고 의미있는 내용이 있을 때만 메시지에 추가
        if ai_evidence or ai_improvement:
            current_message = enhanced_result.get("message", "")
            ai_message = ""
            
            # AI 분석 결과와 개선안을 통합하여 자연스러운 문장으로 구성
            if ai_evidence and ai_evidence.strip() and ai_evidence != "확인 불가":
                if ai_improvement and ai_improvement.strip() and ai_improvement != "확인 불가":
                    # 분석 결과와 개선안을 함께 포함
                    ai_message += f" {ai_evidence} {ai_improvement}"
                else:
                    # 분석 결과만 포함
                    ai_message += f" {ai_evidence}"
            
            if ai_message:
                enhanced_result["message"] = current_message + ai_message
        
        return enhanced_result

    def _perform_technical_checks(self, checks_config: List[Dict]) -> List[Dict]:
        results = []
        lighthouse_result = self.pagespeed_results.get('lighthouseResult', {})
        metrics = lighthouse_result.get('audits', {})
        basic_metrics = self.pagespeed_results.get('basicMetrics', {})
        for item in checks_config:
            result = item.copy()
            result.update({
                "status": "fail",
                "message": "성능 데이터를 가져올 수 없습니다.",
                "score": 0,
                "title": item.get("title", "Unknown"),
                "importance": item.get("importance", 1),
                "criteria": item.get("criteria", "분석 중 오류 발생")
            })
            check_title = item.get("title", "").lower()
            try:
                if "lcp" in check_title:
                    lcp_metric = metrics.get('largest-contentful-paint', {})
                    lcp_value = lcp_metric.get('numericValue', 0) / 1000
                    if lcp_value == 0 and basic_metrics:
                        page_load_time = basic_metrics.get('pageLoadTime', 0) / 1000
                        if page_load_time > 0:
                            lcp_value = page_load_time * 0.6
                    if lcp_value > 0 and lcp_value <= 2.5:
                        result.update({"status": "pass", "message": f"LCP가 {lcp_value:.2f}초로 우수합니다.", "score": 100})
                    else:
                        result.update({"message": f"LCP가 {lcp_value:.2f}초로 개선이 필요합니다 (권장: 2.5초 이하).", "score": 30 if lcp_value > 4 else 60})
                elif "inp" in check_title:
                    tbt_metric = metrics.get('total-blocking-time', {})
                    tbt_value = tbt_metric.get('numericValue', 0)
                    if tbt_value == 0 and basic_metrics:
                        dom_load_time = basic_metrics.get('domContentLoaded', 0)
                        if dom_load_time > 0:
                            tbt_value = dom_load_time * 0.3
                    if tbt_value <= 200:
                        result.update({"status": "pass", "message": f"TBT가 {tbt_value:.0f}ms로 우수하여 INP도 양호할 것으로 예상됩니다.", "score": 100})
                    else:
                        result.update({"message": f"TBT가 {tbt_value:.0f}ms로 높아 INP 개선이 필요할 수 있습니다 (권장: 200ms 이하).", "score": 30 if tbt_value > 600 else 60})
                elif "cls" in check_title:
                    cls_metric = metrics.get('cumulative-layout-shift', {})
                    cls_value = cls_metric.get('numericValue', 0)
                    if cls_value == 0 and basic_metrics:
                        image_count = basic_metrics.get('imageCount', 0)
                        if image_count > 10:
                            cls_value = 0.15
                        elif image_count > 5:
                            cls_value = 0.1
                        else:
                            cls_value = 0.05
                    if cls_value < 0.1:
                        result.update({"status": "pass", "message": f"CLS가 {cls_value:.3f}로 매우 안정적입니다.", "score": 100})
                    else:
                        result.update({"message": f"CLS가 {cls_value:.3f}로 레이아웃 불안정성이 높습니다 (권장: 0.1 미만).", "score": 30 if cls_value > 0.25 else 60})
                elif "ttfb" in check_title:
                    ttfb_metric = metrics.get('server-response-time', {})
                    ttfb_value = ttfb_metric.get('numericValue', 0) / 1000
                    if ttfb_value == 0 and basic_metrics:
                        dom_load_time = basic_metrics.get('domContentLoaded', 0) / 1000
                        if dom_load_time > 0:
                            ttfb_value = dom_load_time * 0.4
                    if ttfb_value > 0 and ttfb_value <= 0.8:
                        result.update({"status": "pass", "message": f"TTFB가 {ttfb_value:.3f}초로 빠릅니다.", "score": 100})
                    else:
                        result.update({"message": f"TTFB가 {ttfb_value:.3f}초로 느립니다 (권장: 0.8초 이하).", "score": 40})
                elif "모바일 친화성" in check_title or "viewport" in check_title:
                    viewport_tag = self.soup.find('meta', attrs={'name': 'viewport'})
                    if viewport_tag and 'width=device-width' in viewport_tag.get('content', ''):
                        result.update({"status": "pass", "message": "Viewport 메타 태그가 올바르게 설정되었습니다.", "score": 100})
                    else:
                        result.update({"message": "페이지가 모바일 친화적이지 않거나 Viewport 설정이 올바르지 않습니다.", "score": 20})
                elif "페이지 크기" in check_title:
                    if basic_metrics:
                        total_size_kb = basic_metrics.get('totalSizeKB', 0)
                        if total_size_kb <= 500:
                            result.update({"status": "pass", "message": f"페이지 크기가 {total_size_kb:.1f}KB로 적절합니다.", "score": 100})
                        elif total_size_kb <= 1000:
                            result.update({"status": "warning", "message": f"페이지 크기가 {total_size_kb:.1f}KB로 다소 큽니다.", "score": 70})
                        else:
                            result.update({"message": f"페이지 크기가 {total_size_kb:.1f}KB로 너무 큽니다 (500KB 이하 권장).", "score": 30})
                    else:
                        result.update({"message": "페이지 크기 정보를 가져올 수 없습니다."})
                elif "fcp" in check_title:
                    fcp_metric = metrics.get('first-contentful-paint', {})
                    fcp_value = fcp_metric.get('numericValue', 0) / 1000
                    if fcp_value == 0 and basic_metrics:
                        first_paint = basic_metrics.get('firstPaint', 0) / 1000
                        if first_paint > 0:
                            fcp_value = first_paint * 1.1
                    if fcp_value > 0 and fcp_value <= 1.8:
                        result.update({"status": "pass", "message": f"FCP가 {fcp_value:.2f}초로 우수합니다.", "score": 100})
                    else:
                        result.update({"message": f"FCP가 {fcp_value:.2f}초로 개선이 필요합니다 (권장: 1.8초 이하).", "score": 30 if fcp_value > 3 else 60})
                elif "js bundle" in check_title or "javascript" in check_title:
                    if basic_metrics:
                        js_size_kb = basic_metrics.get('scriptSizeKB', 0)
                        total_size_kb = basic_metrics.get('totalSizeKB', 0)
                        
                        # 디버깅 정보 추가
                        debug_info = f"전체 크기: {total_size_kb:.1f}KB, JS 크기: {js_size_kb:.1f}KB"
                        
                        if js_size_kb > 0:
                            if js_size_kb <= 150:
                                result.update({"status": "pass", "message": f"JavaScript 번들 크기가 {js_size_kb:.1f}KB로 적절합니다. ({debug_info})", "score": 100})
                            elif js_size_kb <= 300:
                                result.update({"status": "warning", "message": f"JavaScript 번들 크기가 {js_size_kb:.1f}KB로 다소 큽니다. ({debug_info})", "score": 70})
                            else:
                                result.update({"message": f"JavaScript 번들 크기가 {js_size_kb:.1f}KB로 너무 큽니다 (150KB 이하 권장). ({debug_info})", "score": 30})
                        else:
                            # JavaScript 크기가 0인 경우 추가 분석
                            script_tags = self.soup.find_all('script', src=True)
                            inline_scripts = self.soup.find_all('script', src=False)
                            
                            if script_tags or inline_scripts:
                                result.update({"message": f"JavaScript 파일이 있지만 크기 측정에 실패했습니다. 외부 스크립트: {len(script_tags)}개, 인라인 스크립트: {len(inline_scripts)}개", "score": 50})
                            else:
                                result.update({"status": "pass", "message": "페이지에 JavaScript 파일이 없습니다.", "score": 100})
                    else:
                        result.update({"message": "성능 메트릭 데이터를 가져올 수 없습니다. PageSpeed API 또는 브라우저 성능 측정에 실패했습니다."})
                elif "lazy load" in check_title:
                    # 이미지 lazy loading 검사
                    images = self.soup.find_all('img')
                    lazy_images = 0
                    total_images = len(images)
                    
                    for img in images:
                        if img.get('loading') == 'lazy' or 'data-src' in img.attrs or 'data-lazy' in img.attrs:
                            lazy_images += 1
                    
                    if total_images == 0:
                        result.update({"status": "pass", "message": "페이지에 이미지가 없습니다.", "score": 100})
                    elif lazy_images >= total_images * 0.9:  # 90% 이상 lazy loading 적용
                        result.update({"status": "pass", "message": f"이미지의 {lazy_images}/{total_images}개({(lazy_images/total_images)*100:.1f}%)가 lazy loading이 적용되어 있습니다.", "score": 100})
                    elif lazy_images >= total_images * 0.5:  # 50% 이상
                        result.update({"status": "warning", "message": f"이미지의 {lazy_images}/{total_images}개({(lazy_images/total_images)*100:.1f}%)가 lazy loading이 적용되어 있습니다.", "score": 70})
                    else:
                        result.update({"message": f"이미지의 {lazy_images}/{total_images}개({(lazy_images/total_images)*100:.1f}%)만 lazy loading이 적용되어 있습니다 (90% 이상 권장).", "score": 30})
                elif "compression" in check_title or "brotli" in check_title:
                    # 압축 방식 검사
                    content_encoding = self.headers.get('content-encoding', '').lower()
                    accept_encoding = self.headers.get('accept-encoding', '').lower()
                    
                    if 'br' in content_encoding:
                        result.update({"status": "pass", "message": "Brotli 압축이 적용되어 있습니다.", "score": 100})
                    elif 'gzip' in content_encoding:
                        result.update({"status": "warning", "message": "Gzip 압축이 적용되어 있습니다 (Brotli 권장).", "score": 70})
                    elif 'br' in accept_encoding:
                        result.update({"message": "서버에서 Brotli 압축을 지원하지 않습니다.", "score": 30})
                    else:
                        result.update({"message": "압축이 적용되지 않았습니다.", "score": 20})
                elif "http/2" in check_title or "http/3" in check_title:
                    # HTTP 버전 검사 - 100% 정확한 측정 시도
                    server_header = self.headers.get('server', '').lower()
                    alt_svc = self.headers.get('alt-svc', '').lower()
                    connection_header = self.headers.get('connection', '').lower()
                    
                    # HTTP/3 확인 (QUIC 프로토콜) - 100% 확실
                    if 'h3' in alt_svc or 'http/3' in alt_svc:
                        result.update({"status": "pass", "message": "HTTP/3이 지원됩니다 (Alt-Svc 헤더 확인 - 100% 확실).", "score": 100})
                    # HTTP/2 확인 (헤더 기반 - 100% 확실)
                    elif 'h2' in server_header or 'http/2' in server_header:
                        result.update({"status": "pass", "message": "HTTP/2가 지원됩니다 (Server 헤더 확인 - 100% 확실).", "score": 100})
                    elif 'keep-alive' in connection_header and 'upgrade' in connection_header:
                        result.update({"status": "pass", "message": "HTTP/2가 지원됩니다 (Connection 헤더 확인 - 100% 확실).", "score": 100})
                    else:
                        # HTTP/2 지원 여부 분석 (헤더 기반)
                        is_https = self.url.startswith('https://')
                        if is_https:
                                # 확률 기반 분석으로 대체
                                modern_indicators = 0
                                total_indicators = 0
                                
                                # 1. 응답 시간 기반 HTTP/2 가능성
                                http2_likely = self.headers.get('x-http2-likely', 'false').lower() == 'true'
                                if http2_likely:
                                    modern_indicators += 1
                                total_indicators += 1
                                
                                # 2. 현대적인 서버 소프트웨어 확인
                                modern_server = self.headers.get('x-modern-server', 'false').lower() == 'true'
                                if modern_server:
                                    modern_indicators += 1
                                total_indicators += 1
                                
                                # 3. 보안 헤더 확인
                                security_headers_count = int(self.headers.get('x-security-headers-count', '0'))
                                if security_headers_count >= 2:
                                    modern_indicators += 1
                                total_indicators += 1
                                
                                # 4. 압축 지원 확인
                                compression_supported = any(comp in self.headers.get('content-encoding', '').lower() 
                                                          for comp in ['gzip', 'br', 'deflate'])
                                if compression_supported:
                                    modern_indicators += 1
                                total_indicators += 1
                                
                                # 5. Alt-Svc 헤더 확인
                                if 'h3' in alt_svc or 'http/3' in alt_svc:
                                    modern_indicators += 1
                                total_indicators += 1
                                
                                support_probability = (modern_indicators / total_indicators) * 100 if total_indicators > 0 else 0
                                
                                if support_probability >= 80:
                                    result.update({"status": "pass", "message": f"HTTP/2가 지원될 가능성이 매우 높습니다 ({support_probability:.0f}% 확률). 현대적인 서버 설정과 빠른 응답이 확인됩니다.", "score": 95})
                                elif support_probability >= 60:
                                    result.update({"status": "pass", "message": f"HTTP/2가 지원될 가능성이 높습니다 ({support_probability:.0f}% 확률). 일부 현대적인 설정이 확인됩니다.", "score": 85})
                                elif support_probability >= 40:
                                    result.update({"status": "warning", "message": f"HTTP/2 지원 가능성이 있습니다 ({support_probability:.0f}% 확률). HTTPS를 사용하고 있지만 서버 설정이 제한적일 수 있습니다.", "score": 70})
                                else:
                                    result.update({"status": "warning", "message": f"HTTP/2 지원 여부를 명확히 확인할 수 없습니다 ({support_probability:.0f}% 확률). HTTPS를 사용하고 있지만 서버 설정이 구식일 수 있습니다.", "score": 50})
                        else:
                            result.update({"status": "fail", "message": "HTTP/1.1을 사용하고 있습니다. HTTPS로 업그레이드하면 HTTP/2 지원이 가능할 수 있습니다.", "score": 30})
                        # 더 정확한 HTTP/2 감지를 위한 추가 분석
                        is_https = self.url.startswith('https://')
                        
                        if is_https:
                            # HTTPS 사이트의 경우 HTTP/2 지원 가능성 높음
                            # 추가 지표들을 확인하여 더 정확한 판단
                            modern_indicators = 0
                            total_indicators = 0
                            
                            # 1. 응답 시간 기반 HTTP/2 가능성 (새로 추가된 지표)
                            http2_likely = self.headers.get('x-http2-likely', 'false').lower() == 'true'
                            if http2_likely:
                                modern_indicators += 1
                            total_indicators += 1
                            
                            # 2. 현대적인 서버 소프트웨어 확인 (개선됨)
                            modern_server = self.headers.get('x-modern-server', 'false').lower() == 'true'
                            if modern_server:
                                modern_indicators += 1
                            total_indicators += 1
                            
                            # 3. 보안 헤더 확인 (개선됨)
                            security_headers_count = int(self.headers.get('x-security-headers-count', '0'))
                            if security_headers_count >= 2:  # 2개 이상의 보안 헤더
                                modern_indicators += 1
                            total_indicators += 1
                            
                            # 4. 압축 지원 확인 (HTTP/2는 압축을 지원)
                            compression_supported = any(comp in self.headers.get('content-encoding', '').lower() 
                                                      for comp in ['gzip', 'br', 'deflate'])
                            if compression_supported:
                                modern_indicators += 1
                            total_indicators += 1
                            
                            # 5. Alt-Svc 헤더 확인 (HTTP/3 지원 가능성)
                            alt_svc = self.headers.get('alt-svc', '').lower()
                            if 'h3' in alt_svc or 'http/3' in alt_svc:
                                modern_indicators += 1
                            total_indicators += 1
                            
                            # 지원 가능성 계산
                            support_probability = (modern_indicators / total_indicators) * 100 if total_indicators > 0 else 0
                            
                            # 더 정확한 메시지와 점수
                            if support_probability >= 80:
                                result.update({"status": "pass", "message": f"HTTP/2가 지원될 가능성이 매우 높습니다 ({support_probability:.0f}% 확률). 현대적인 서버 설정과 빠른 응답이 확인됩니다.", "score": 95})
                            elif support_probability >= 60:
                                result.update({"status": "pass", "message": f"HTTP/2가 지원될 가능성이 높습니다 ({support_probability:.0f}% 확률). 일부 현대적인 설정이 확인됩니다.", "score": 85})
                            elif support_probability >= 40:
                                result.update({"status": "warning", "message": f"HTTP/2 지원 가능성이 있습니다 ({support_probability:.0f}% 확률). HTTPS를 사용하고 있지만 서버 설정이 제한적일 수 있습니다.", "score": 70})
                            else:
                                result.update({"status": "warning", "message": f"HTTP/2 지원 여부를 명확히 확인할 수 없습니다 ({support_probability:.0f}% 확률). HTTPS를 사용하고 있지만 서버 설정이 구식일 수 있습니다.", "score": 50})
                        else:
                            result.update({"status": "fail", "message": "HTTP/1.1을 사용하고 있습니다. HTTPS로 업그레이드하면 HTTP/2 지원이 가능할 수 있습니다.", "score": 30})
                elif "이미지 최적화" in check_title:
                    if basic_metrics:
                        image_size_kb = basic_metrics.get('imageSizeKB', 0)
                        total_size_kb = basic_metrics.get('totalSizeKB', 1)
                        image_ratio = (image_size_kb / total_size_kb) * 100 if total_size_kb > 0 else 0
                        if image_ratio <= 50:
                            result.update({"status": "pass", "message": f"이미지 크기가 전체의 {image_ratio:.1f}%로 적절합니다.", "score": 100})
                        elif image_ratio <= 70:
                            result.update({"status": "warning", "message": f"이미지 크기가 전체의 {image_ratio:.1f}%로 다소 큽니다.", "score": 70})
                        else:
                            result.update({"message": f"이미지 크기가 전체의 {image_ratio:.1f}%로 너무 큽니다 (50% 이하 권장).", "score": 30})
                    else:
                        result.update({"message": "이미지 크기 정보를 가져올 수 없습니다."})
                else:
                    result.update({"status": "pending", "message": "분석 미지원"})
            except Exception as e:
                result.update({"message": f"알 수 없는 오류: {e}"})
            results.append(result)
        return results

    async def _test_http2_connection(self, host: str) -> bool:
        """실제 HTTP/2 연결을 테스트합니다 (100% 확실한 방법)."""
        try:
            import socket
            import ssl
            
            # SSL 컨텍스트 생성
            context = ssl.create_default_context()
            context.set_alpn_protocols(['h2', 'http/1.1'])
            
            # 소켓 연결
            with socket.create_connection((host, 443), timeout=15) as sock:
                with context.wrap_socket(sock, server_hostname=host) as ssock:
                    # ALPN 협상 결과 확인
                    protocol = ssock.selected_alpn_protocol()
                    return protocol == 'h2'
        except Exception as e:
            print(f"HTTP/2 연결 테스트 실패: {e}")
            return False
    
    async def _perform_ai_checks(self, ai_analyzer) -> Dict:
        try:
            # (변경) 범용 수집 메소드 사용: E-E-A-T 전용 수집 로직 제거됨
            website_data = await ai_analyzer._collect_website_data(self.url)
            ai_result = await ai_analyzer.analyze_web_performance(self.url, website_data)
            return ai_result
        except Exception as e:
            print(f"❌ 웹 성능 AI 분석 오류: {e}")
            return {
                "error": f"AI 분석 중 오류 발생: {str(e)}",
                "web_performance_score": 0.0,
                "web_performance_percentage": 0.0,
                "performance_metrics": {},
                "strengths": [],
                "weaknesses": [],
                "priority_recommendations": [],
                "expected_improvement": ""
            }