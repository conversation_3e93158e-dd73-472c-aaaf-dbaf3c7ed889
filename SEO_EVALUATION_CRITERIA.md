# SEO 평가 기준 문서

## 목차

1. [URL 및 콘텐츠 표준화](#url-및-콘텐츠-표준화)
2. [웹 성능 및 기술적 최적화](#웹-성능-및-기술적-최적화)

---

## URL 및 콘텐츠 표준화

### 1. URL 길이 및 형식

**평가 항목**: 짧은 영문-kebab URL

**판정 기준**:

- **초록 (pass) - 100점**: URL 길이가 60자 이하이고 kebab-case 형식 사용
- **노랑 (warning) - 60점**: URL 길이가 60-100자이거나 형식이 일부 부적절
- **빨강 (fail) - 20점**: URL 길이가 100자 초과이거나 형식이 부적절

**권장사항**:

- URL 길이: 60자 이하
- 형식: kebab-case (예: `/my-article-title`)
- 특수문자 사용 금지

### 2. 중복 파라미터 제거

**평가 항목**: 중복 파라미터 제거

**판정 기준**:

- **초록 (pass) - 100점**: 중복 파라미터 없음
- **노랑 (warning) - 60점**: 일부 중복 파라미터 존재
- **빨강 (fail) - 20점**: 다수의 중복 파라미터 존재

**권장사항**:

- 동일한 파라미터가 반복되지 않도록 설정
- URL 정규화 적용

### 3. Self-Canonical

**평가 항목**: Self-Canonical 설정

**판정 기준**:

- **초록 (pass) - 100점**: canonical 태그가 현재 URL을 정확히 가리킴
- **노랑 (warning) - 60점**: canonical 태그가 있지만 일부 부정확
- **빨강 (fail) - 20점**: canonical 태그가 없거나 잘못 설정됨

**권장사항**:

- 모든 페이지에 canonical 태그 설정
- canonical URL이 현재 페이지 URL과 일치

### 4. Canonical 체인

**평가 항목**: Canonical 체인 확인

**판정 기준**:

- **초록 (pass) - 100점**: canonical 체인 없음 (정상)
- **노랑 (warning) - 60점**: 2단계 canonical 체인
- **빨강 (fail) - 20점**: 3단계 이상 canonical 체인

**권장사항**:

- canonical 체인 최소화
- 직접적인 canonical 설정 권장

### 5. 교차 도메인 정합

**평가 항목**: 교차 도메인 canonical 정합

**판정 기준**:

- **초록 (pass) - 100점**: 교차 도메인 canonical 없음 (정상)
- **노랑 (warning) - 60점**: 일부 교차 도메인 canonical 존재
- **빨강 (fail) - 20점**: 다수의 교차 도메인 canonical 존재

**권장사항**:

- 같은 도메인 내에서 canonical 설정
- 교차 도메인 canonical 최소화

### 6. HTTP vs HTTPS 혼재

**평가 항목**: HTTP/HTTPS 혼재 사용

**판정 기준**:

- **초록 (pass) - 100점**: HTTPS만 사용
- **노랑 (warning) - 60점**: 일부 HTTP 리소스 존재
- **빨강 (fail) - 20점**: 다수의 HTTP 리소스 존재

**권장사항**:

- 모든 리소스를 HTTPS로 제공
- HTTP에서 HTTPS로 리다이렉트 설정

### 7. URL 매개변수 정책

**평가 항목**: URL 매개변수 정책

**판정 기준**:

- **초록 (pass) - 100점**: 매개변수 정책이 적절히 설정됨
- **노랑 (warning) - 60점**: 일부 매개변수 정책 부족
- **빨강 (fail) - 20점**: 매개변수 정책이 없거나 부적절

**권장사항**:

- 불필요한 매개변수 제거
- 중요한 매개변수만 유지

### 8. 언어/지역 세그먼트

**평가 항목**: 언어/지역 세그먼트 설정

**판정 기준**:

- **초록 (pass) - 100점**:
  - 언어/지역 세그먼트가 올바르게 설정됨
  - 단일 언어 사이트로 세그먼트가 없는 경우 (정상)
- **빨강 (fail) - 20점**: 다국어 사이트인데 언어 세그먼트가 설정되지 않음

**권장사항**:

- 다국어 사이트: `/en/`, `/ko/` 등 언어 코드 사용
- 단일 언어 사이트: 세그먼트 불필요

### 9. 리다이렉트 홉

**평가 항목**: 리다이렉트 홉 확인

**판정 기준**:

- **노랑 (warning) - 50점**: 리다이렉트 홉 확인 필요
- **빨강 (fail) - 0점**: 분석 오류

**권장사항**:

- 리다이렉트 체인 최소화
- 직접적인 리다이렉트 설정

### 10. 콘텐츠 해시 중복

**평가 항목**: 해시 중복 ≤10%

**판정 기준**:

- **초록 (pass) - 100점**: 중복률 10% 이하
- **초록 (pass) - 70점**: 현재 페이지만으로 분석 (기본적으로 중복 없음)
- **회색 (pending) - 0점**: 콘텐츠 수집 필요
- **빨강 (fail) - 20-50점**: 중복률 10% 초과

**권장사항**:

- 콘텐츠 중복률 10% 이하 유지
- 고유한 콘텐츠 제공

### 11. 제목 중복

**평가 항목**: Title 중복 ≤5%

**판정 기준**:

- **초록 (pass) - 100점**: 중복률 5% 이하
- **초록 (pass) - 70점**: 현재 페이지만으로 분석 (기본적으로 중복 없음)
- **회색 (pending) - 0점**: 제목 수집 필요
- **빨강 (fail) - 20-50점**: 중복률 5% 초과

**권장사항**:

- 제목 중복률 5% 이하 유지
- 고유한 제목 작성

### 12. 메타 설명 중복

**평가 항목**: Meta Description 중복 ≤5%

**판정 기준**:

- **초록 (pass) - 100점**: 중복률 5% 이하
- **초록 (pass) - 70점**: 현재 페이지만으로 분석 (기본적으로 중복 없음)
- **회색 (pending) - 0점**: 메타 설명 수집 필요
- **빨강 (fail) - 20-50점**: 중복률 5% 초과

**권장사항**:

- 메타 설명 중복률 5% 이하 유지
- 고유한 메타 설명 작성

### 13. Near-Duplicate TF-IDF

**평가 항목**: Near-Duplicate TF-IDF 분석

**판정 기준**:

- **초록 (pass) - 100점**: 유사도 80% 이하
- **노랑 (warning) - 60점**: 유사도 80-90%
- **빨강 (fail) - 20점**: 유사도 90% 초과

**권장사항**:

- 콘텐츠 유사도 80% 이하 유지
- 고유한 콘텐츠 제공

### 14. Thin 콘텐츠

**평가 항목**: Thin 단어수 확인

**판정 기준**:

- **초록 (pass) - 100점**: 300단어 이상
- **노랑 (warning) - 60점**: 150-300단어
- **빨강 (fail) - 20점**: 150단어 미만

**권장사항**:

- 페이지당 최소 300단어 이상
- 의미있는 콘텐츠 제공

### 15. Facet 파라미터 Noindex

**평가 항목**: Facet 파라미터 Noindex 설정

**판정 기준**:

- **초록 (pass) - 100점**: Facet 파라미터에 noindex 설정됨
- **노랑 (warning) - 60점**: 일부 Facet 파라미터에 noindex 설정
- **빨강 (fail) - 20점**: Facet 파라미터에 noindex 설정되지 않음

**권장사항**:

- Facet 파라미터 페이지에 noindex 설정
- 검색엔진 크롤링 최적화

### 16. Syndication Cross-Domain

**평가 항목**: Syndication Cross-Domain Canonical

**판정 기준**:

- **초록 (pass) - 100점**: Cross-Domain canonical 없음 (정상)
- **노랑 (warning) - 60점**: 일부 Cross-Domain canonical 존재
- **빨강 (fail) - 20점**: 다수의 Cross-Domain canonical 존재

**권장사항**:

- 같은 도메인 내에서 canonical 설정
- Cross-Domain canonical 최소화

### 17. Session ID 제거

**평가 항목**: Session ID 제거

**판정 기준**:

- **초록 (pass) - 100점**: Session ID 없음
- **노랑 (warning) - 60점**: 일부 Session ID 존재
- **빨강 (fail) - 20점**: 다수의 Session ID 존재

**권장사항**:

- URL에서 Session ID 제거
- 쿠키나 세션 스토리지 사용

---

## 웹 성능 및 기술적 최적화

### 1. LCP (Largest Contentful Paint)

**평가 항목**: LCP ≤2.5초

**판정 기준**:

- **초록 (pass) - 100점**: 2.5초 이하
- **노랑 (warning) - 60점**: 2.5-4초
- **빨강 (fail) - 30점**: 4초 초과

**권장사항**:

- 이미지 최적화
- 서버 응답 시간 개선
- CSS/JS 최적화

### 2. INP (Interaction to Next Paint)

**평가 항목**: INP 최적화 (TBT 기반)

**판정 기준**:

- **초록 (pass) - 100점**: TBT 200ms 이하
- **노랑 (warning) - 60점**: TBT 200-600ms
- **빨강 (fail) - 30점**: TBT 600ms 초과

**권장사항**:

- JavaScript 최적화
- 이벤트 핸들러 최적화
- 메인 스레드 블로킹 최소화

### 3. CLS (Cumulative Layout Shift)

**평가 항목**: CLS <0.1

**판정 기준**:

- **초록 (pass) - 100점**: 0.1 미만
- **노랑 (warning) - 60점**: 0.1-0.25
- **빨강 (fail) - 30점**: 0.25 초과

**권장사항**:

- 이미지 크기 명시
- 광고 공간 사전 할당
- 동적 콘텐츠 로딩 최적화

### 4. TTFB (Time to First Byte)

**평가 항목**: TTFB ≤0.8초

**판정 기준**:

- **초록 (pass) - 100점**: 0.8초 이하
- **노랑 (warning) - 60점**: 0.8-1.2초
- **빨강 (fail) - 40점**: 1.2초 초과

**권장사항**:

- 서버 응답 시간 개선
- CDN 사용
- 데이터베이스 쿼리 최적화

### 5. 모바일 친화성

**평가 항목**: Viewport 설정

**판정 기준**:

- **초록 (pass) - 100점**: Viewport 메타 태그 올바르게 설정
- **빨강 (fail) - 20점**: Viewport 설정 부적절

**권장사항**:

- `<meta name="viewport" content="width=device-width, initial-scale=1">` 설정
- 반응형 디자인 적용

### 6. 페이지 크기

**평가 항목**: 페이지 크기 최적화

**판정 기준**:

- **초록 (pass) - 100점**: 500KB 이하
- **노랑 (warning) - 70점**: 500KB-1MB
- **빨강 (fail) - 30점**: 1MB 초과

**권장사항**:

- 이미지 압축
- CSS/JS 최소화
- 불필요한 리소스 제거

### 7. FCP (First Contentful Paint)

**평가 항목**: FCP 최적화

**판정 기준**:

- **초록 (pass) - 100점**: 1.8초 이하
- **노랑 (warning) - 60점**: 1.8-3초
- **빨강 (fail) - 30점**: 3초 초과

**권장사항**:

- CSS 최적화
- 폰트 로딩 최적화
- 중요 리소스 우선 로딩

### 8. HTTP/2 지원

**평가 항목**: HTTP/2 프로토콜 지원

**판정 기준**:

- **초록 (pass) - 100점**: HTTP/2 지원
- **노랑 (warning) - 60점**: HTTP/1.1 사용
- **빨강 (fail) - 20점**: HTTP/1.0 이하 사용

**권장사항**:

- HTTP/2 프로토콜 사용
- 서버 설정 최적화

### 9. 이미지 최적화

**평가 항목**: 이미지 최적화 상태

**판정 기준**:

- **초록 (pass) - 100점**: 모든 이미지 최적화됨
- **노랑 (warning) - 60점**: 일부 이미지 최적화 필요
- **빨강 (fail) - 20점**: 대부분 이미지 최적화 필요

**권장사항**:

- WebP 형식 사용
- 적절한 이미지 크기
- 지연 로딩 적용

### 10. JavaScript 최적화

**평가 항목**: JavaScript 로딩 최적화

**판정 기준**:

- **초록 (pass) - 100점**: JavaScript 최적화됨
- **노랑 (warning) - 60점**: 일부 JavaScript 최적화 필요
- **빨강 (fail) - 20점**: JavaScript 최적화 필요

**권장사항**:

- JavaScript 번들링
- 코드 분할 적용
- 불필요한 스크립트 제거

### 11. CSS 최적화

**평가 항목**: CSS 로딩 최적화

**판정 기준**:

- **초록 (pass) - 100점**: CSS 최적화됨
- **노랑 (warning) - 60점**: 일부 CSS 최적화 필요
- **빨강 (fail) - 20점**: CSS 최적화 필요

**권장사항**:

- CSS 최소화
- 중요 CSS 인라인 로딩
- 미디어 쿼리 최적화

### 12. 캐싱 설정

**평가 항목**: 브라우저 캐싱 설정

**판정 기준**:

- **초록 (pass) - 100점**: 적절한 캐싱 설정
- **노랑 (warning) - 60점**: 일부 캐싱 설정 필요
- **빨강 (fail) - 20점**: 캐싱 설정 부족

**권장사항**:

- 정적 리소스 장기 캐싱
- 동적 콘텐츠 적절한 캐싱
- ETag 설정

### 13. Gzip 압축

**평가 항목**: Gzip 압축 설정

**판정 기준**:

- **초록 (pass) - 100점**: Gzip 압축 활성화
- **노랑 (warning) - 60점**: 일부 리소스 압축 필요
- **빨강 (fail) - 20점**: 압축 설정 없음

**권장사항**:

- 텍스트 기반 리소스 Gzip 압축
- Brotli 압축 고려

### 14. 보안 헤더

**평가 항목**: 보안 헤더 설정

**판정 기준**:

- **초록 (pass) - 100점**: 주요 보안 헤더 설정됨
- **노랑 (warning) - 60점**: 일부 보안 헤더 설정
- **빨강 (fail) - 20점**: 보안 헤더 설정 부족

**권장사항**:

- HSTS 헤더 설정
- CSP 헤더 설정
- X-Frame-Options 설정

---

## 상태 코드 설명

### 상태별 의미

- **초록 (pass)**: 기준을 만족하여 양호한 상태
- **노랑 (warning)**: 일부 개선이 필요한 상태
- **빨강 (fail)**: 즉시 개선이 필요한 상태
- **회색 (pending)**: 분석을 위한 데이터가 부족한 상태

### 점수 기준

- **100점**: 완벽한 상태
- **70-90점**: 양호한 상태
- **50-70점**: 개선 필요
- **20-50점**: 개선 필요 (높음)
- **0-20점**: 즉시 개선 필요

---

## 참고사항

1. **데이터 수집**: 일부 항목은 사이트맵에서 URL을 수집하여 분석합니다.
2. **현재 페이지 분석**: 데이터 수집이 어려운 경우 현재 페이지만으로 기본 분석을 수행합니다.
3. **AI 분석**: 일부 항목은 AI 분석을 통해 추가 인사이트를 제공합니다.
4. **실시간 측정**: 성능 지표는 실제 측정값을 기반으로 합니다.

이 문서는 SEO 분석 도구의 평가 기준을 명확히 하여 사용자가 결과를 이해하고 개선 방향을 파악할 수 있도록 작성되었습니다.
