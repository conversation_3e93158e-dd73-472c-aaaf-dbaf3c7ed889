import google.generativeai as genai
import json
import os
from typing import Dict, List
import re
import time

class AIAnalyzer:
    def __init__(self):
        self.api_keys = self._load_api_keys()
        if not self.api_keys:
            raise ValueError("GOOGLE_API_KEY 환경 변수가 설정되지 않았습니다.")
        
        self.current_key_index = 0
        self.model = None
        self._configure_current_api_key()
        
        # API 키별 사용 통계
        self.key_usage_stats = {key: {'success': 0, 'failure': 0, 'last_used': None} for key in self.api_keys}

    def _load_api_keys(self) -> List[str]:
        """환경 변수에서 API 키들을 로드합니다."""
        api_keys = []
        
        # 메인 API 키
        main_key = os.getenv("GOOGLE_API_KEY")
        if main_key:
            api_keys.append(main_key)
        
        # 스페어 API 키들 (GOOGLE_API_KEY_1, GOOGLE_API_KEY_2, ...)
        i = 1
        while True:
            spare_key = os.getenv(f"GOOGLE_API_KEY_{i}")
            if not spare_key:
                break
            api_keys.append(spare_key)
            i += 1
        
        # 쉼표로 구분된 여러 키 (GOOGLE_API_KEYS)
        combined_keys = os.getenv("GOOGLE_API_KEYS")
        if combined_keys:
            keys_from_env = [key.strip() for key in combined_keys.split(",") if key.strip()]
            api_keys.extend(keys_from_env)
        
        # 중복 제거
        unique_keys = list(dict.fromkeys(api_keys))
        print(f"로드된 API 키 개수: {len(unique_keys)}")
        return unique_keys

    def _configure_current_api_key(self):
        """현재 선택된 API 키로 Gemini를 설정합니다."""
        if self.current_key_index >= len(self.api_keys):
            raise ValueError("모든 API 키가 소진되었습니다.")
        
        current_key = self.api_keys[self.current_key_index]
        genai.configure(api_key=current_key)
        self.model = genai.GenerativeModel('gemini-2.0-flash')
        print(f"API 키 {self.current_key_index + 1}/{len(self.api_keys)} 사용 중")

    def _switch_to_next_api_key(self) -> bool:
        """다음 API 키로 전환합니다."""
        self.current_key_index += 1
        if self.current_key_index >= len(self.api_keys):
            print("모든 API 키가 소진되었습니다.")
            return False
        
        print(f"API 키 {self.current_key_index + 1}/{len(self.api_keys)}로 전환")
        self._configure_current_api_key()
        print(f"새로운 모델 인스턴스 생성 완료")
        return True

    def _is_api_error_retryable(self, error: Exception) -> bool:
        """API 오류가 재시도 가능한지 확인합니다."""
        error_str = str(error).lower()
        
        # 재시도 가능한 오류들
        retryable_errors = [
            'quota exceeded',
            'rate limit',
            'quota',
            'limit exceeded',
            'too many requests',
            'resource exhausted',
            'permission denied',
            'invalid api key',
            'authentication',
            'api key'
        ]
        
        return any(retry_error in error_str for retry_error in retryable_errors)

    async def _make_api_call_with_retry(self, api_call_func, *args, **kwargs):
        """API 호출을 재시도 로직과 함께 수행합니다."""
        max_retries = len(self.api_keys)
        retry_count = 0
        
        while retry_count < max_retries:
            try:
                # API 키 사용 통계 업데이트
                current_key = self.api_keys[self.current_key_index]
                self.key_usage_stats[current_key]['last_used'] = time.time()
                
                # 현재 모델로 API 호출 수행
                result = await self.model.generate_content_async(*args, **kwargs)
                
                # 성공 시 통계 업데이트
                self.key_usage_stats[current_key]['success'] += 1
                return result
                
            except Exception as e:
                # 실패 시 통계 업데이트
                current_key = self.api_keys[self.current_key_index]
                self.key_usage_stats[current_key]['failure'] += 1
                
                print(f"API 호출 실패 (키 {self.current_key_index + 1}): {e}")
                
                # 재시도 가능한 오류인지 확인
                if self._is_api_error_retryable(e):
                    print(f"재시도 가능한 오류로 판단됨: {str(e)[:100]}...")
                    if self._switch_to_next_api_key():
                        retry_count += 1
                        print(f"재시도 {retry_count}/{max_retries} 시도 중...")
                        continue
                    else:
                        print("사용 가능한 API 키가 없습니다.")
                        raise e
                else:
                    # 재시도 불가능한 오류 (예: 잘못된 요청 형식)
                    print(f"재시도 불가능한 오류입니다: {str(e)[:100]}...")
                    raise e
        
        # 모든 재시도 실패
        raise Exception("모든 API 키로 시도했지만 실패했습니다.")

    def get_api_key_status(self) -> Dict:
        """현재 API 키들의 상태를 반환합니다."""
        return {
            'current_key_index': self.current_key_index,
            'total_keys': len(self.api_keys),
            'usage_stats': self.key_usage_stats
        }

    def _create_prompt(self, analysis_result: Dict) -> str:
        analysis_str = json.dumps(analysis_result, indent=2, ensure_ascii=False)
        
        prompt = f"""
당신은 세계 최고 수준의 SEO 컨설턴트입니다. 주어진 SEO 분석 결과를 바탕으로, 전문가적이고 실행 가능한 심층 분석 보고서를 작성해야 합니다.

**분석 결과:**
```json
{analysis_str}
```

**보고서 작성 지침:**
주어진 분석 결과를 해석하여, 다음 항목들을 포함한 전문가 보고서를 JSON 형식으로 작성해주십시오. 특히 '분석 미지원'으로 표시된 항목들을 중점적으로 추론하고 평가해야 합니다.

1.  **URL 및 콘텐츠 표준화 (추론)**: URL 구조의 의미성, 콘텐츠 중복 가능성 등을 `url_content_standardization` 섹션 결과를 바탕으로 평가하고, '분석 미지원' 항목에 대한 의견을 제시하십시오.
2.  **사이트 구조 및 사용자 경험 (추론)**: 내부 링크 구조, 탐색 용이성 등을 `site_structure_ux` 섹션 결과를 바탕으로 평가하고, '분석 미지원' 항목에 대한 의견을 제시하십시오.
3.  **콘텐츠 전문성 및 권위성 (E-E-A-T 추론)**: 페이지의 콘텐츠를 바탕으로 `content_expertise_authority` 섹션의 항목들(예: 저자 정보, 신뢰성)을 평가하고, '분석 미지원' 항목에 대한 의견을 제시하십시오.
4.  **전반적인 요약 (overall_summary)**: 위 내용을 포함한 모든 분석 결과를 종합하여 사이트의 현재 SEO 상태를 2-3 문장으로 요약합니다.
5.  **SEO 레벨 (seo_level)**: "초급", "중급", "고급", "전문가" 중 하나로 사이트의 SEO 수준을 평가합니다.
6.  **강점 (strengths)**: 'pass'된 항목 중 가장 돋보이는 강점 2-3개를 설명합니다.
7.  **우선순위 높은 추천 (priority_recommendations)**: 가장 시급하고 효과가 클 것으로 예상되는 개선 항목 3-5개를 'fail'된 항목과 AI의 추론적 평가를 바탕으로 제시합니다. 각 항목은 다음 JSON 형식을 따라야 합니다:
    - `priority`: "높음", "중간", "낮음"
    - `title`: 추천 항목의 제목
    - `description`: 구체적인 실행 방법
    - `category`: 관련 SEO 영역 (예: "콘텐츠", "사이트 구조")
    - `expected_impact`: 예상되는 긍정적 효과
8.  **예상 점수 향상 (expected_score_improvement)**: 추천 사항 적용 시 예상되는 전체 점수 향상 범위를 제시합니다.
9.  **예상 실행 기간 (implementation_timeline)**: 추천 사항 적용에 필요한 예상 기간을 제시합니다.

**최종 JSON 출력 형식:**
```json
{{
  "overall_summary": "...",
  "seo_level": "...",
  "strengths": ["...", "..."],
  "priority_recommendations": [
    {{
      "priority": "...", "title": "...", "description": "...", "category": "...", "expected_impact": "..."
    }}
  ],
  "technical_insights": ["...", "..."],
  "content_insights": ["...", "..."],
  "expected_score_improvement": "...",
  "implementation_timeline": "..."
}}
```
"""
        return prompt

    def _create_item_specific_prompt(self, failed_checks: list) -> str:
        """실패한 개별 항목들에 대한 구체적인 해결책을 요청하는 프롬프트를 생성합니다."""
        items_str = ""
        for check in failed_checks:
            items_str += f"- ID: {check['id']}, 항목: {check['title']}\n  결과: {check['message']}\n"

        prompt = f"""
당신은 세계 최고 수준의 SEO 컨설턴트입니다. 다음은 SEO 분석에서 '실패' 또는 '주의' 판정을 받은 항목들의 목록입니다. 각 항목에 대해, 초보자도 이해하고 따라 할 수 있도록 구체적인 해결 방안과 예상 효과를 제안해주십시오.

**분석 결과:**
{items_str}

**요청 사항:**
각 항목에 대한 해결책을 JSON 형식의 리스트로 반환해주십시오. 각 JSON 객체는 다음 세 개의 키를 가져야 합니다:
- `id`: 위에서 제공된 ID를 그대로 사용
- `suggestion`: 구체적인 해결 방안 (1-2문장)
- `expected_impact`: 예상되는 구체적인 효과 (예: "검색 결과 노출 개선 및 클릭률 증가", "페이지 로딩 속도 향상 및 사용자 경험 개선" 등)

**출력 예시:**
```json
[
  {{
    "id": 5,
    "suggestion": "페이지에 rel='canonical' 태그를 추가하여 중복 콘텐츠 문제를 해결하세요. <head> 섹션에 <link rel='canonical' href='현재페이지URL' /> 태그를 삽입하십시오.",
    "expected_impact": "검색 엔진 최적화 및 순위 향상"
  }},
  {{
    "id": 8,
    "suggestion": "robots.txt 파일이 검색엔진에 의해 접근 가능하도록, 서버 루트 디렉토리에 파일을 업로드하고 권한을 확인하십시오.",
    "expected_impact": "검색 엔진 크롤링 효율성 증대 및 색인 개선"
  }}
]
```

**중요:** 
- 각 제안의 `id`는 반드시 위에서 제공된 ID와 정확히 일치해야 합니다.
- **중요**: 각 ID에 대해 하나의 제안만 생성하세요. 중복된 ID의 제안을 만들지 마세요.
- **절대 중복 금지**: 같은 ID나 비슷한 내용의 제안을 여러 번 생성하지 마세요.
- `expected_impact`는 구체적이고 실용적인 효과를 명시해야 합니다 (예: "검색 결과 클릭률 증가", "페이지 로딩 속도 향상", "모바일 사용자 경험 개선" 등).
"""
        return prompt

    async def get_suggestions_for_failed_checks(self, failed_checks: list) -> list:
        """실패한 항목 목록을 받아 AI에게 해결책을 요청하고, 그 결과를 반환합니다."""
        if not failed_checks:
            return []
        
        # 실패한 항목이 너무 많으면 상위 5개만 처리 (속도 개선)
        limited_checks = failed_checks[:5] if len(failed_checks) > 5 else failed_checks
        
        prompt = self._create_item_specific_prompt(limited_checks)
        try:
            response = await self._make_api_call_with_retry(None, prompt)
            suggestions = self._parse_suggestions_response(response.text, limited_checks)
            return suggestions
        except Exception as e:
            print(f"개별 항목 AI 제안 생성 중 오류: {e}")
            return self._get_default_suggestions(limited_checks)

    async def combine_analysis_with_ai_insights(self, analysis_result: Dict) -> Dict:
        prompt = self._create_prompt(analysis_result)
        try:
            response = await self._make_api_call_with_retry(None, prompt)
            ai_insights = self._parse_main_report_response(response.text)
        except Exception as e:
            print(f"AI 분석 중 오류 발생: {e}")
            ai_insights = self._get_default_insights()

        analysis_result["ai_analysis"] = ai_insights
        return analysis_result

    def _parse_main_report_response(self, response_text: str) -> Dict:
        """AI가 생성한 메인 분석 보고서(JSON 객체)를 파싱합니다."""
        try:
            json_match = re.search(r"```json\s*([\s\S]*?)\s*```", response_text)
            if json_match:
                json_content = json_match.group(1)
            else:
                json_content = response_text
            
            parsed_data = json.loads(json_content)

            if not isinstance(parsed_data, dict):
                 print(f"AI 메인 리포트가 딕셔너리 형식이 아님: {parsed_data}")
                 return self._get_default_insights(error="AI response was not a dictionary.")

            return self._normalize_ai_insights(parsed_data)
        except json.JSONDecodeError as e:
            print(f"AI 응답 JSON 파싱 실패: {e}")
            return self._get_default_insights(error=str(e))

    def _parse_suggestions_response(self, response_text: str, failed_checks: list) -> list:
        """AI가 생성한 항목별 제안(JSON 리스트)을 파싱합니다."""
        try:
            json_match = re.search(r"```json\s*([\s\S]*?)\s*```", response_text)
            if json_match:
                json_content = json_match.group(1).strip()
            else:
                json_content = response_text.strip()
            
            # 응답이 비어있는 경우 처리
            if not json_content:
                return []

            parsed_data = json.loads(json_content)

            if isinstance(parsed_data, list):
                # 각 항목의 유효성 검사 및 ID 안전 변환
                valid_suggestions = []
                for item in parsed_data:
                    if isinstance(item, dict) and 'id' in item and 'suggestion' in item:
                        try:
                            # ID를 안전하게 숫자로 변환
                            item_id = int(item['id'])
                            suggestion_data = {
                                'id': item_id,
                                'suggestion': str(item['suggestion'])
                            }
                            
                            # expected_impact 필드가 있으면 추가
                            if 'expected_impact' in item:
                                suggestion_data['expected_impact'] = str(item['expected_impact'])
                            
                            valid_suggestions.append(suggestion_data)
                        except (ValueError, TypeError):
                            print(f"잘못된 ID 형식: {item['id']}")
                            continue
                
                # 중복 제거: 같은 ID와 내용의 제안이 여러 개 있으면 첫 번째 것만 유지
                unique_suggestions = []
                seen_ids = set()
                seen_contents = set()
                
                for suggestion in valid_suggestions:
                    suggestion_content = suggestion.get('suggestion', '').strip()
                    suggestion_id = suggestion['id']
                    
                    # ID와 내용 모두 중복 체크
                    content_key = f"{suggestion_id}:{suggestion_content}"
                    
                    if suggestion_id not in seen_ids and content_key not in seen_contents:
                        unique_suggestions.append(suggestion)
                        seen_ids.add(suggestion_id)
                        seen_contents.add(content_key)
                    else:
                        print(f"중복 제안 제거: ID {suggestion_id}, 내용: {suggestion_content[:50]}...")
                
                # AI가 응답하지 않은 항목들을 위한 기본 제안 생성
                responded_ids = {s['id'] for s in unique_suggestions}
                missing_suggestions = []
                
                for check in failed_checks:
                    if check['id'] not in responded_ids:
                        missing_suggestions.append({
                            'id': check['id'],
                            'suggestion': f"{check['title']} 항목을 개선하기 위해 전문가의 도움을 받거나 추가 분석이 필요합니다.",
                            'expected_impact': "전문가 상담 후 구체적 효과 예측 가능"
                        })
                
                # 모든 제안을 합쳐서 반환
                all_suggestions = unique_suggestions + missing_suggestions
                return all_suggestions
            else:
                print(f"AI 제안 응답이 리스트 형식이 아님: {parsed_data}")
                # AI 응답이 잘못된 경우 기본 제안 생성
                return [{'id': check['id'], 'suggestion': f"{check['title']} 항목을 개선하기 위해 전문가의 도움을 받거나 추가 분석이 필요합니다.", 'expected_impact': "전문가 상담 후 구체적 효과 예측 가능"} for check in failed_checks]
        except json.JSONDecodeError as e:
            print(f"AI 제안 응답 JSON 파싱 실패: {e}")
            # JSON 파싱 실패 시 기본 제안 생성
            return [{'id': check['id'], 'suggestion': f"{check['title']} 항목을 개선하기 위해 전문가의 도움을 받거나 추가 분석이 필요합니다.", 'expected_impact': "전문가 상담 후 구체적 효과 예측 가능"} for check in failed_checks]
        except Exception as e:
            print(f"AI 제안 파싱 중 알 수 없는 오류: {e}")
            # 기타 오류 시 기본 제안 생성
            return [{'id': check['id'], 'suggestion': f"{check['title']} 항목을 개선하기 위해 전문가의 도움을 받거나 추가 분석이 필요합니다.", 'expected_impact': "전문가 상담 후 구체적 효과 예측 가능"} for check in failed_checks]

    def _normalize_ai_insights(self, ai_data: Dict) -> Dict:
        defaults = self._get_default_insights()
        
        # 필드 이름이 다른 경우를 대비해 매핑
        if 'timeline' in ai_data:
            ai_data['implementation_timeline'] = ai_data.pop('timeline')
        if 'summary' in ai_data:
            ai_data['overall_summary'] = ai_data.pop('summary')
        if 'expected_improvement' in ai_data:
            ai_data['expected_score_improvement'] = ai_data.pop('expected_improvement')
        
        # 모든 필수 필드에 대해 기본값 설정
        for key, value in defaults.items():
            ai_data.setdefault(key, value)
        
        # priority_recommendations의 구조 안정화
        if not isinstance(ai_data.get('priority_recommendations'), list):
            ai_data['priority_recommendations'] = []

        return ai_data

    def _get_default_insights(self, error: str = None) -> Dict:
        summary = "AI 분석 중 오류가 발생했습니다. 잠시 후 다시 시도해주세요."
        if error:
            summary += f" (오류: {error})"
            
        return {
            "overall_summary": summary,
            "seo_level": "분석 실패",
            "strengths": [],
            "priority_recommendations": [],
            "technical_insights": [],
            "content_insights": [],
            "expected_score_improvement": "N/A",
            "implementation_timeline": "N/A"
        }

    def _get_default_suggestions(self, failed_checks: list) -> list:
        """AI 분석 실패 시 기본 제안을 생성합니다."""
        suggestions = []
        for check in failed_checks:
            suggestions.append({
                'id': check['id'],
                'suggestion': f"{check['title']} 항목을 개선하기 위해 전문가의 도움을 받거나 추가 분석이 필요합니다.",
                'expected_impact': "전문가 상담 후 구체적 효과 예측 가능"
            })
        return suggestions