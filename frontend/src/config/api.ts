// API 설정
const API_CONFIG = {
  // 환경에 따른 API URL 설정
  BASE_URL:
    process.env.REACT_APP_API_URL ||
    (process.env.NODE_ENV === "production"
      ? "/api" // Vercel 서버리스 함수 사용 (HTTPS → HTTP 프록시)
      : "http://localhost:8000"),

  // 개발/배포 환경 구분
  IS_DEVELOPMENT:
    process.env.REACT_APP_ENVIRONMENT === "development" ||
    process.env.NODE_ENV === "development",

  // API 엔드포인트
  ENDPOINTS: {
    ANALYZE: "/analyze",
    HEALTH: "/",
  },

  // 요청 설정
  TIMEOUT: 30000, // 30초

  // 헤더 설정
  HEADERS: {
    "Content-Type": "application/json",
  },
};

// API URL 빌더
export const getApiUrl = (endpoint: string): string => {
  return `${API_CONFIG.BASE_URL}${endpoint}`;
};

// 환경 정보 출력 (개발 모드에서만)
if (API_CONFIG.IS_DEVELOPMENT) {
  console.log("🔧 API 설정:", {
    baseUrl: API_CONFIG.BASE_URL,
    environment: process.env.REACT_APP_ENVIRONMENT || process.env.NODE_ENV,
    isDevelopment: API_CONFIG.IS_DEVELOPMENT,
  });
}

export default API_CONFIG;
