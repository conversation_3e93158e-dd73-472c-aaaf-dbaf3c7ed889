import React from "react";
import { useLocation, useNavigate } from "react-router-dom";

import styles from "./ErrorPage.module.scss";
import classNames from "classnames/bind";

const cx = classNames.bind(styles);

const ErrorPage: React.FC = () => {
  const location = useLocation() as any;
  const navigate = useNavigate();
  const message = location?.state?.message || "오류가 발생했습니다.";

  return (
    <div className={cx("error-page")}>
      <h2>오류 발생</h2>
      <p>{message}</p>
      <button onClick={() => navigate("/")} className={cx("home-button")}>
        홈으로 돌아가기
      </button>
    </div>
  );
};

export default ErrorPage;
