version: "3.8"

services:
  seo-backend:
    build: .
    ports:
      - "8000:8000"
    environment:
      - GOOGLE_API_KEY=${GOOGLE_API_KEY}
      - GOOGLE_API_KEY_1=${GOOGLE_API_KEY_1:-}
      - GOOGLE_API_KEY_2=${GOOGLE_API_KEY_2:-}
      - GOOGLE_API_KEY_3=${GOOGLE_API_KEY_3:-}
      - GOOGLE_API_KEYS=${GOOGLE_API_KEYS:-}
      - ENVIRONMENT=production
    restart: unless-stopped
    volumes:
      - ./logs:/app/logs # 로그 저장용
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/conf.d/default.conf
      - /etc/letsencrypt:/etc/letsencrypt:ro # Let's Encrypt 인증서
      - /var/www/certbot:/var/www/certbot:ro # Certbot webroot
    depends_on:
      - seo-backend
    restart: unless-stopped
