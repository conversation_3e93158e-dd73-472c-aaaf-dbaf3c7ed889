import re
import hashlib
import urllib.parse
import time
from typing import Dict, List, Tuple
from bs4 import BeautifulSoup
import httpx
import aiohttp
import asyncio
from collections import Counter
import json
import gzip

class URLContentAnalyzer:
    """URL 및 콘텐츠 표준화 전용 분석기"""
    
    def __init__(self, url: str, soup: BeautifulSoup, headers: Dict):
        self.url = url
        self.soup = soup
        self.headers = headers
        self.domain = urllib.parse.urlparse(url).netloc
        self.collected_urls = []
        self.collected_titles = []
        self.collected_descriptions = []
        self.collected_contents = []
        
    async def analyze_url_content_standardization(self, checks_config: List[Dict]) -> List[Dict]:
        """URL 및 콘텐츠 표준화 항목들을 분석합니다."""
        results = []
        
        # 먼저 사이트의 URL들을 수집
        await self._collect_site_urls()
        
        for item in checks_config:
            result = item.copy()
            result.update({"status": "fail", "message": "분석 중 오류", "score": 0})
            check_title = item.get("title", "").lower()
            
            try:
                if "짧은 영문-kebab url" in check_title:
                    result = await self._check_url_length_and_format()
                elif "중복 파라미터 제거" in check_title:
                    result = await self._check_duplicate_parameters()
                elif "self-canonical" in check_title:
                    result = await self._check_self_canonical()
                elif "canonical 체인" in check_title:
                    result = await self._check_canonical_chains()
                elif "교차 도메인 정합" in check_title:
                    result = await self._check_cross_domain_canonical()
                elif "http vs https 혼재" in check_title:
                    result = await self._check_http_https_mixed()
                elif "매개변수 정책" in check_title:
                    result = await self._check_url_parameters()
                elif "언어/지역 세그먼트" in check_title:
                    result = await self._check_language_segments()
                elif "redirect hop" in check_title:
                    result = await self._check_redirect_hops()
                elif "해시 중복" in check_title:
                    result = await self._check_content_hash_duplicates()
                elif "title 중복" in check_title:
                    result = await self._check_title_duplicates()
                elif "meta description 중복" in check_title:
                    result = await self._check_description_duplicates()
                elif "near-duplicate tf-idf" in check_title:
                    result = await self._check_near_duplicate_content()
                elif "thin 단어수" in check_title:
                    result = await self._check_thin_content()
                elif "facet 파라미터 noindex" in check_title:
                    result = await self._check_facet_parameters()

                elif "syndication cross-domain" in check_title:
                    result = await self._check_syndication_canonical()
                elif "sessionid 제거" in check_title:
                    result = await self._check_session_id_removal()
                else:
                    result.update({"status": "pending", "message": "분석 미지원"})
                    
            except Exception as e:
                result.update({"message": f"분석 오류: {str(e)}"})
            
            results.append(result)
        
        return results
    
    async def _collect_site_urls(self):
        """사이트맵을 기준으로 대표 URL들을 수집합니다."""
        try:
            # 1. 사이트맵 URL들 추출
            sitemap_urls = await self._extract_sitemap_urls()
            print(f"🗺️ 사이트맵에서 {len(sitemap_urls)}개 URL 발견")
            
            # 2. 대표 URL들 필터링 (중복 및 얇은 콘텐츠 분석용)
            representative_urls = self._filter_representative_urls(sitemap_urls)
            print(f"📋 대표 URL {len(representative_urls)}개 선정")
            
            # 3. 선정된 URL들 크롤링하여 데이터 수집
            await self._crawl_representative_urls(representative_urls)
            
            print(f"📊 수집된 데이터: URL {len(self.collected_urls)}개, 제목 {len(self.collected_titles)}개, 설명 {len(self.collected_descriptions)}개, 콘텐츠 {len(self.collected_contents)}개")
            
        except Exception as e:
            print(f"URL 수집 오류: {e}")
            # 오류 발생 시 현재 페이지만 사용
            self._collect_current_page_data()
    
    async def _extract_sitemap_urls(self) -> List[str]:
        """사이트맵에서 URL들을 추출합니다."""
        sitemap_urls = []
        start_time = time.time()
        max_processing_time = 180  # 3분 제한
        
        try:
            # robots.txt에서 사이트맵 찾기 (프로토콜/호스트 변형 시도)
            host_variants = set()
            host_variants.add(self.domain)
            if not self.domain.startswith('www.'):
                host_variants.add(f"www.{self.domain}")
            if self.domain.startswith('www.'):
                host_variants.add(self.domain[4:])
            robots_candidates = []
            for host in host_variants:
                robots_candidates.append(f"https://{host}/robots.txt")
                robots_candidates.append(f"http://{host}/robots.txt")
            print(f"🔍 robots.txt 후보 확인 중: {', '.join(robots_candidates)}")
            
            async with httpx.AsyncClient(follow_redirects=True, headers={"User-Agent": "xe-seo-analyzer/1.0"}) as client:
                for robots_url in robots_candidates:
                    try:
                        response = await client.get(robots_url, timeout=10)
                        if response.status_code == 200:
                            robots_content = response.text
                            print(f"✅ robots.txt 접근 성공: {robots_url}")
                            for line in robots_content.split('\n'):
                                if 'sitemap:' in line.lower():
                                    # 'Sitemap: <url>' 형태에서 URL 안전 추출
                                    m = re.search(r"(?i)sitemap:\s*(\S+)", line)
                                    sitemap_raw = m.group(1).strip() if m else line.split(':', 1)[1].strip()
                                    # 상대경로 지원
                                    if not sitemap_raw.lower().startswith(('http://', 'https://')):
                                        sitemap_url = urllib.parse.urljoin(f"https://{self.domain}/", sitemap_raw)
                                    else:
                                        sitemap_url = sitemap_raw
                                    print(f"🗺️ robots.txt에서 사이트맵 발견: {sitemap_url}")
                                    urls = await self._parse_sitemap(sitemap_url)
                                    sitemap_urls.extend(urls)
                                    # 타임아웃 체크
                                    if time.time() - start_time > max_processing_time:
                                        print(f"⚠️ 사이트맵 처리 시간 초과 ({max_processing_time}초). 중단합니다.")
                                        break
                        else:
                            print(f"❌ robots.txt 접근 실패 (상태 코드: {response.status_code}): {robots_url}")
                    except Exception as e:
                        print(f"❌ robots.txt 요청 오류: {robots_url} -> {e}")
            
            # 일반적인 사이트맵 위치들도 확인
            common_sitemaps = []
            sitemap_paths = [
                "/sitemap.xml",
                "/sitemap.xml.gz",
                "/sitemap_index.xml",
                "/sitemap_index.xml.gz",
                "/sitemap-index.xml",
                "/sitemap-index.xml.gz",
                "/sitemap/sitemap.xml",
                "/sitemap/sitemap.xml.gz",
                "/sitemap/sitemap_index.xml",
                "/sitemap/sitemap_index.xml.gz",
                "/sitemaps/sitemap.xml",
                "/sitemaps/sitemap.xml.gz",
                "/sitemaps/sitemap_index.xml",
                "/sitemaps/sitemap_index.xml.gz"
            ]
            for host in host_variants:
                for path in sitemap_paths:
                    common_sitemaps.append(f"https://{host}{path}")
                    common_sitemaps.append(f"http://{host}{path}")
            
            print(f"🔍 일반적인 사이트맵 위치 확인 중...")
            for sitemap_url in common_sitemaps:
                # 타임아웃 체크
                if time.time() - start_time > max_processing_time:
                    print(f"⚠️ 사이트맵 처리 시간 초과 ({max_processing_time}초). 중단합니다.")
                    break
                    
                try:
                    print(f"  - 확인 중: {sitemap_url}")
                    urls_from_sitemap = await self._parse_sitemap(sitemap_url)
                    if urls_from_sitemap:
                        print(f"  ✅ {sitemap_url}에서 {len(urls_from_sitemap)}개 URL 발견")
                        sitemap_urls.extend(urls_from_sitemap)
                    else:
                        print(f"  ❌ {sitemap_url}에서 URL을 찾지 못함")
                except Exception as e:
                    print(f"  ❌ {sitemap_url} 접근 실패: {e}")
                    continue
            
            unique_urls = list(set(sitemap_urls))
            
            # URL 수 제한 (너무 많은 URL이 수집되는 것을 방지)
            if len(unique_urls) > 1000:
                print(f"⚠️ URL 수가 너무 많습니다 ({len(unique_urls)}개). 처음 1000개만 사용합니다.")
                unique_urls = unique_urls[:1000]
            
            print(f"📊 총 {len(unique_urls)}개 고유 URL 발견")
            return unique_urls
            
        except Exception as e:
            print(f"❌ 사이트맵 추출 오류: {e}")
            return []
    
    async def _parse_sitemap(self, sitemap_url: str, processed_sitemaps: set = None, depth: int = 0) -> List[str]:
        """사이트맵을 파싱하여 URL들을 추출합니다."""
        # 무한 루프 방지를 위한 재귀 깊이 제한
        if depth > 3:  # 최대 3단계까지만 재귀
            print(f"    ⚠️ 사이트맵 재귀 깊이 제한 도달 (depth: {depth})")
            return []
        
        # 이미 처리된 사이트맵인지 확인
        if processed_sitemaps is None:
            processed_sitemaps = set()
        
        if sitemap_url in processed_sitemaps:
            print(f"    ⚠️ 이미 처리된 사이트맵 건너뛰기: {sitemap_url}")
            return []
        
        processed_sitemaps.add(sitemap_url)
        urls = []
        
        try:
            async with httpx.AsyncClient(follow_redirects=True, headers={"User-Agent": "xe-seo-analyzer/1.0"}) as client:
                response = await client.get(sitemap_url, timeout=10)
                if response.status_code == 200:
                    raw_bytes = response.content
                    ct = response.headers.get('Content-Type', '').lower()
                    content_is_gzip = sitemap_url.lower().endswith('.gz') or ('application/x-gzip' in ct or 'application/gzip' in ct)
                    if content_is_gzip:
                        try:
                            raw_bytes = gzip.decompress(raw_bytes)
                            print(f"  🗜️ gzip 압축 해제: {sitemap_url}")
                        except Exception as e:
                            print(f"  ⚠️ gzip 해제 실패: {e}")
                    try:
                        content = raw_bytes.decode('utf-8', errors='replace')
                    except Exception:
                        content = response.text
                    print(f"  📄 {sitemap_url} 파싱 중... (depth: {depth})")
                    
                    # XML 파싱
                    soup = BeautifulSoup(content, 'xml')
                    
                    # URL 태그들 찾기
                    url_tags = soup.find_all('url')
                    print(f"    - URL 태그 {len(url_tags)}개 발견")
                    
                    for url_tag in url_tags:
                        loc_tag = url_tag.find('loc')
                        if loc_tag:
                            url = loc_tag.get_text().strip()
                            if self._is_same_domain(url):
                                urls.append(url)
                    
                    # sitemap 태그들도 확인 (sitemap index인 경우) - 무한 루프 방지
                    sitemap_tags = soup.find_all('sitemap')
                    if sitemap_tags and depth < 2:  # 최대 2단계까지만 하위 사이트맵 처리
                        print(f"    - 사이트맵 인덱스 발견: {len(sitemap_tags)}개 하위 사이트맵")
                        sub_sitemap_count = 0
                        for sitemap_tag in sitemap_tags[:5]:  # 최대 5개 하위 사이트맵만 처리
                            loc_tag = sitemap_tag.find('loc')
                            if loc_tag:
                                sub_sitemap_url = loc_tag.get_text().strip()
                                if sub_sitemap_url not in processed_sitemaps:  # 중복 처리 방지
                                    print(f"      - 하위 사이트맵 처리: {sub_sitemap_url}")
                                    sub_urls = await self._parse_sitemap(sub_sitemap_url, processed_sitemaps, depth + 1)
                                    urls.extend(sub_urls)
                                    sub_sitemap_count += 1
                                    if sub_sitemap_count >= 3:  # 최대 3개까지만 처리
                                        print(f"      ⚠️ 하위 사이트맵 처리 제한 도달 (3개)")
                                        break
                                else:
                                    print(f"      ⚠️ 이미 처리된 하위 사이트맵 건너뛰기: {sub_sitemap_url}")
                    elif sitemap_tags and depth >= 2:
                        print(f"    ⚠️ 재귀 깊이 제한으로 하위 사이트맵 처리 건너뛰기 (depth: {depth})")
                    
                    print(f"    ✅ {sitemap_url}에서 {len(urls)}개 URL 추출")
                else:
                    print(f"    ❌ {sitemap_url} 접근 실패 (상태 코드: {response.status_code})")
            
            return urls
            
        except Exception as e:
            print(f"    ❌ 사이트맵 파싱 오류 ({sitemap_url}): {e}")
            return []
    
    def _filter_representative_urls(self, urls: List[str]) -> List[str]:
        """대표 URL들을 필터링합니다."""
        if not urls:
            return []
        
        # URL 패턴별로 분류
        url_categories = {}
        
        for url in urls:
            parsed = urllib.parse.urlparse(url)
            path = parsed.path
            
            # 카테고리별로 분류
            if '/blog/' in path or '/news/' in path or '/article/' in path:
                category = 'blog'
            elif '/product/' in path or '/item/' in path or '/shop/' in path:
                category = 'product'
            elif '/service/' in path or '/solution/' in path:
                category = 'service'
            elif '/about/' in path or '/company/' in path:
                category = 'about'
            elif '/contact/' in path or '/support/' in path:
                category = 'contact'
            else:
                category = 'other'
            
            if category not in url_categories:
                url_categories[category] = []
            url_categories[category].append(url)
        
        # 각 카테고리에서 대표 URL 선택 (최대 5개씩)
        representative_urls = []
        for category, category_urls in url_categories.items():
            # 각 카테고리에서 최대 5개 선택
            selected_urls = category_urls[:5]
            representative_urls.extend(selected_urls)
        
        # 전체 URL이 20개를 넘지 않도록 조정
        if len(representative_urls) > 20:
            representative_urls = representative_urls[:20]
        
        return representative_urls
    
    async def _crawl_representative_urls(self, urls: List[str]):
        """대표 URL들을 크롤링하여 데이터를 수집합니다."""
        if not urls:
            print("⚠️ 크롤링할 URL이 없습니다.")
            return
            
        print(f"🕷️ {len(urls)}개 URL 크롤링 시작...")
        semaphore = asyncio.Semaphore(5)  # 동시 요청 제한
        
        successful_crawls = 0
        failed_crawls = 0
        
        async def crawl_single_url(url: str):
            nonlocal successful_crawls, failed_crawls
            async with semaphore:
                try:
                    async with httpx.AsyncClient() as client:
                        response = await client.get(url, timeout=10)
                        if response.status_code == 200:
                            soup = BeautifulSoup(response.text, 'html.parser')
                            
                            # 제목 수집
                            title_tag = soup.find('title')
                            if title_tag:
                                title = title_tag.get_text().strip()
                                if title and len(title) > 10:  # 의미있는 제목만
                                    self.collected_titles.append(title)
                            
                            # 메타 설명 수집
                            meta_desc = soup.find('meta', attrs={'name': 'description'})
                            if meta_desc:
                                desc = meta_desc.get('content', '').strip()
                                if desc and len(desc) > 20:  # 의미있는 설명만
                                    self.collected_descriptions.append(desc)
                            
                            # 콘텐츠 수집
                            main_content = soup.find('main') or soup.find('article') or soup.find('div', class_='content')
                            if main_content:
                                content = main_content.get_text().strip()
                            else:
                                body = soup.find('body')
                                content = body.get_text().strip() if body else ""
                            
                            if content and len(content) > 100:  # 의미있는 콘텐츠만
                                self.collected_contents.append(content)
                            
                            self.collected_urls.append(url)
                            successful_crawls += 1
                            print(f"  ✅ {url} 크롤링 성공")
                            
                except Exception as e:
                    failed_crawls += 1
                    print(f"  ❌ URL 크롤링 오류 ({url}): {e}")
        
        # 병렬로 크롤링
        tasks = [crawl_single_url(url) for url in urls]
        await asyncio.gather(*tasks, return_exceptions=True)
        
        # 중복 제거
        self.collected_urls = list(set(self.collected_urls))
        self.collected_titles = list(set(self.collected_titles))
        self.collected_descriptions = list(set(self.collected_descriptions))
        self.collected_contents = list(set(self.collected_contents))
        
        print(f"📊 크롤링 완료: 성공 {successful_crawls}개, 실패 {failed_crawls}개")
        print(f"📊 수집된 데이터: 제목 {len(self.collected_titles)}개, 설명 {len(self.collected_descriptions)}개, 콘텐츠 {len(self.collected_contents)}개")
    
    def _collect_current_page_data(self):
        """현재 페이지의 데이터만 수집합니다."""
        if self.soup:
            # 제목과 메타 설명 수집
            title_tag = self.soup.find('title')
            if title_tag:
                title = title_tag.get_text().strip()
                if title:
                    self.collected_titles.append(title)
            
            meta_desc = self.soup.find('meta', attrs={'name': 'description'})
            if meta_desc:
                desc = meta_desc.get('content', '').strip()
                if desc:
                    self.collected_descriptions.append(desc)
            
            # 콘텐츠 수집
            main_content = self.soup.find('main') or self.soup.find('article') or self.soup.find('div', class_='content')
            if main_content:
                content = main_content.get_text().strip()
            else:
                body = self.soup.find('body')
                content = body.get_text().strip() if body else ""
            
            if content:
                self.collected_contents.append(content)
            
            self.collected_urls.append(self.url)
    
    def _is_same_domain(self, url: str) -> bool:
        """URL이 같은 도메인인지 확인합니다."""
        try:
            def normalize(host: str) -> str:
                if host.startswith('www.'):
                    return host[4:]
                return host
            parsed = urllib.parse.urlparse(url)
            return normalize(parsed.netloc) == normalize(self.domain)
        except:
            return False
    
    async def _check_url_length_and_format(self) -> Dict:
        """URL 길이와 형식을 확인합니다."""
        try:
            parsed = urllib.parse.urlparse(self.url)
            path = parsed.path
            
            # 길이 체크 (115자 이하)
            if len(self.url) <= 115:
                length_score = 100
            elif len(self.url) <= 150:
                length_score = 70
            else:
                length_score = 30
            
            # 형식 체크 (소문자, kebab-case)
            is_lowercase = self.url.lower() == self.url
            has_kebab_format = re.search(r'[a-z0-9]+(?:-[a-z0-9]+)*', path) is not None
            
            format_score = 100 if (is_lowercase and has_kebab_format) else 50
            
            total_score = (length_score + format_score) // 2
            
            if total_score >= 80:
                status = "pass"
                message = "URL이 권장 형식을 따릅니다."
            elif total_score >= 60:
                status = "warning"
                message = "URL 형식이 부분적으로 개선이 필요합니다."
            else:
                status = "fail"
                message = "URL 형식 개선이 필요합니다."
            
            return {
                "status": status,
                "message": message,
                "score": total_score
            }
        except Exception as e:
            return {
                "status": "fail",
                "message": f"URL 분석 오류: {str(e)}",
                "score": 0
            }
    
    async def _check_duplicate_parameters(self) -> Dict:
        """중복 파라미터를 확인합니다."""
        try:
            parsed = urllib.parse.urlparse(self.url)
            query_params = urllib.parse.parse_qs(parsed.query)
            
            # 중복 파라미터 체크
            duplicate_params = []
            for param, values in query_params.items():
                if len(values) > 1:
                    duplicate_params.append(param)
            
            if not duplicate_params:
                return {
                    "status": "pass",
                    "message": "중복 파라미터가 없습니다.",
                    "score": 100
                }
            else:
                return {
                    "status": "fail",
                    "message": f"중복 파라미터 발견: {', '.join(duplicate_params)}",
                    "score": 30
                }
        except Exception as e:
            return {
                "status": "fail",
                "message": f"파라미터 분석 오류: {str(e)}",
                "score": 0
            }
    
    async def _check_self_canonical(self) -> Dict:
        """Self-canonical 태그를 확인합니다."""
        try:
            # 더 유연한 canonical 태그 검색
            canonical_tag = self.soup.find('link', rel=lambda x: x and 'canonical' in x.lower())
            if canonical_tag and canonical_tag.get('href'):
                canonical_url = urllib.parse.urljoin(self.url, canonical_tag['href'])
                normalized_canonical = self._normalize_url(canonical_url)
                normalized_current = self._normalize_url(self.url)
                
                if normalized_canonical == normalized_current:
                    return {
                        "status": "pass",
                        "message": "Self-canonical이 올바르게 설정되었습니다.",
                        "score": 100
                    }
                else:
                    return {
                        "status": "fail",
                        "message": f"Canonical URL이 현재 페이지와 다릅니다: {canonical_url}",
                        "score": 30
                    }
            else:
                return {
                    "status": "fail",
                    "message": "Canonical 태그가 없습니다.",
                    "score": 0
                }
        except Exception as e:
            return {
                "status": "fail",
                "message": f"Canonical 분석 오류: {str(e)}",
                "score": 0
            }
    
    async def _check_canonical_chains(self) -> Dict:
        """Canonical 체인을 확인합니다."""
        try:
            # 현재 페이지의 canonical 태그 확인
            canonical_tag = self.soup.find('link', rel=lambda x: x and 'canonical' in x.lower())
            if not canonical_tag or not canonical_tag.get('href'):
                return {
                    "status": "pass",
                    "message": "Canonical 태그가 없습니다.",
                    "score": 100
                }
            
            canonical_url = urllib.parse.urljoin(self.url, canonical_tag['href'])
            
            # 현재 URL과 canonical URL이 같으면 체인 없음
            if canonical_url == self.url:
                return {
                    "status": "pass",
                    "message": "Self-canonical이므로 체인이 없습니다.",
                    "score": 100
                }
            
            # canonical URL로 이동해서 체인 확인
            try:
                import aiohttp
                async with aiohttp.ClientSession() as session:
                    async with session.get(canonical_url, headers=self.headers, timeout=10) as response:
                        if response.status == 200:
                            content = await response.text()
                            canonical_soup = BeautifulSoup(content, 'html.parser')
                            
                            # canonical URL의 canonical 태그 확인
                            target_canonical = canonical_soup.find('link', rel=lambda x: x and 'canonical' in x.lower())
                            if target_canonical and target_canonical.get('href'):
                                target_canonical_url = urllib.parse.urljoin(canonical_url, target_canonical['href'])
                                
                                # 체인 확인: A -> B -> C 형태인지 확인
                                if target_canonical_url != canonical_url:
                                    return {
                                        "status": "fail",
                                        "message": f"Canonical 체인 발견: {self.url} -> {canonical_url} -> {target_canonical_url}",
                                        "score": 0
                                    }
                                else:
                                    return {
                                        "status": "pass",
                                        "message": f"Canonical 체인이 없습니다. 최종 URL: {canonical_url}",
                                        "score": 100
                                    }
                            else:
                                return {
                                    "status": "pass",
                                    "message": f"Canonical 체인이 없습니다. 최종 URL: {canonical_url}",
                                    "score": 100
                                }
                        else:
                            return {
                                "status": "warning",
                                "message": f"Canonical URL에 접근할 수 없습니다: {canonical_url}",
                                "score": 50
                            }
            except Exception as e:
                return {
                    "status": "warning",
                    "message": f"Canonical 체인 확인 중 오류: {str(e)}",
                    "score": 50
                }
            
        except Exception as e:
            return {
                "status": "fail",
                "message": f"Canonical 체인 분석 오류: {str(e)}",
                "score": 0
            }
    
    async def _check_cross_domain_canonical(self) -> Dict:
        """교차 도메인 canonical을 확인합니다."""
        try:
            canonical_tag = self.soup.find('link', rel=lambda x: x and 'canonical' in x.lower())
            if canonical_tag and canonical_tag.get('href'):
                canonical_url = canonical_tag['href']
                canonical_domain = urllib.parse.urlparse(canonical_url).netloc
                
                # 일반 권장사항: 동일 도메인 canonical 권장, 교차 도메인은 주의
                if canonical_domain and canonical_domain == self.domain:
                    return {
                        "status": "pass",
                        "message": "canonical이 동일 도메인으로 올바르게 설정되었습니다.",
                        "score": 100
                    }
                elif canonical_domain and canonical_domain != self.domain:
                    return {
                        "status": "warning",
                        "message": f"교차 도메인 canonical 감지({canonical_domain}). 특수한 syndication 케이스가 아니라면 동일 도메인 canonical을 권장합니다.",
                        "score": 60
                    }
            
            return {
                "status": "pass",
                "message": "교차 도메인 canonical이 감지되지 않았습니다(정상).",
                "score": 100
            }
        except Exception as e:
            return {
                "status": "fail",
                "message": f"교차 도메인 canonical 분석 오류: {str(e)}",
                "score": 0
            }
    
    async def _check_http_https_mixed(self) -> Dict:
        """HTTP/HTTPS 혼재를 확인합니다."""
        try:
            # 현재 URL이 HTTPS인지 확인
            is_https = self.url.startswith('https://')
            
            # 페이지 내 리소스들의 프로토콜 확인
            mixed_content = []
            
            # 이미지, 스크립트, CSS, 프레임/미디어 등 확인 (상대경로/프로토콜 상대 포함 절대화)
            check_tags = ['img', 'script', 'link', 'iframe', 'audio', 'video', 'source', 'object', 'embed']
            for tag in self.soup.find_all(check_tags):
                src = tag.get('src') or tag.get('href') or tag.get('data')
                if not src:
                    continue
                try:
                    # 절대화: 상대경로와 // 형태 처리
                    absolute_url = urllib.parse.urljoin(self.url, src)
                    parsed = urllib.parse.urlparse(absolute_url)
                    if is_https and parsed.scheme == 'http':
                        mixed_content.append(absolute_url)
                except Exception:
                    continue
            
            # HTTPS 페이지인 경우 HTTP에서 HTTPS로의 리디렉션 확인
            if is_https:
                http_url = self.url.replace('https://', 'http://')
                redirect_status = await self._check_http_to_https_redirect(http_url)
                
                if mixed_content:
                    return {
                        "status": "fail",
                        "message": f"HTTPS 페이지에 HTTP 리소스가 {len(mixed_content)}개 있습니다. {redirect_status}",
                        "score": 20
                    }
                else:
                    return {
                        "status": "pass",
                        "message": f"HTTPS 페이지가 올바르게 설정되었습니다. {redirect_status}",
                        "score": 100
                    }
            else:
                # HTTP 페이지인 경우 HTTPS로의 리디렉션 확인
                https_url = self.url.replace('http://', 'https://')
                redirect_status = await self._check_http_to_https_redirect(self.url)
                
                return {
                    "status": "warning",
                    "message": f"HTTP 페이지입니다. HTTPS로 업그레이드를 권장합니다. {redirect_status}",
                    "score": 40
                }
        except Exception as e:
            return {
                "status": "fail",
                "message": f"HTTP/HTTPS 분석 오류: {str(e)}",
                "score": 0
            }
    
    async def _check_url_parameters(self) -> Dict:
        """URL 파라미터 정책을 확인합니다."""
        try:
            parsed = urllib.parse.urlparse(self.url)
            query_params = urllib.parse.parse_qs(parsed.query)
            
            # 일반적인 추적/세션 파라미터들
            tracking_params = ['utm_', 'fbclid', 'gclid', 'msclkid', 'ref', 'source']
            session_params = ['session', 'sid', 'id', 'token']
            
            problematic_params = []
            for param in query_params.keys():
                if any(tracking in param.lower() for tracking in tracking_params):
                    problematic_params.append(f"추적 파라미터: {param}")
                elif any(session in param.lower() for session in session_params):
                    problematic_params.append(f"세션 파라미터: {param}")
            
            if not problematic_params:
                return {
                    "status": "pass",
                    "message": "URL 파라미터가 적절합니다.",
                    "score": 100
                }
            else:
                return {
                    "status": "warning",
                    "message": f"개선이 필요한 파라미터: {', '.join(problematic_params)}",
                    "score": 60
                }
        except Exception as e:
            return {
                "status": "fail",
                "message": f"URL 파라미터 분석 오류: {str(e)}",
                "score": 0
            }
    
    async def _check_language_segments(self) -> Dict:
        """언어/지역 세그먼트를 확인합니다."""
        try:
            path = urllib.parse.urlparse(self.url).path
            
            # 언어/지역 패턴 확인
            language_patterns = [
                r'/[a-z]{2}(?:-[a-z]{2})?/',  # /en/, /en-us/
                r'/[a-z]{2}_[a-z]{2}/',      # /en_us/
            ]
            
            has_language_segment = any(re.search(pattern, path) for pattern in language_patterns)
            
            # 다국어 사이트 여부 확인 (hreflang 태그, 언어 선택기 등)
            is_multilingual = False
            
            # hreflang 태그 확인
            hreflang_tags = self.soup.find_all('link', attrs={'hreflang': True})
            if hreflang_tags:
                is_multilingual = True
            
            # 언어 선택기 확인 (일반적인 언어 선택 패턴)
            language_selectors = [
                '.language-selector', '.lang-switcher', '.locale-selector',
                '[class*="language"]', '[class*="lang"]', '[class*="locale"]'
            ]
            for selector in language_selectors:
                if self.soup.select(selector):
                    is_multilingual = True
                    break
            
            # 언어 관련 텍스트 확인
            language_keywords = ['language', 'lang', '언어', 'language selector', 'select language']
            page_text = self.soup.get_text().lower()
            if any(keyword in page_text for keyword in language_keywords):
                is_multilingual = True
            
            if has_language_segment:
                return {
                    "status": "pass",
                    "message": "언어/지역 세그먼트가 올바르게 설정되었습니다.",
                    "score": 100
                }
            elif is_multilingual:
                # 다국어 사이트인데 언어 세그먼트가 없는 경우 - 빨강
                return {
                    "status": "fail",
                    "message": "다국어 사이트인데 언어/지역 세그먼트가 설정되지 않았습니다. URL에 언어 코드를 추가하세요 (예: /en/, /ko/).",
                    "score": 20
                }
            else:
                # 단일 언어 사이트인 경우 - 초록 (정상)
                return {
                    "status": "pass",
                    "message": "단일 언어 사이트로 언어/지역 세그먼트가 설정되지 않은 것은 정상입니다.",
                    "score": 100
                }
        except Exception as e:
            return {
                "status": "fail",
                "message": f"언어 세그먼트 분석 오류: {str(e)}",
                "score": 0
            }
    
    async def _check_redirect_hops(self) -> Dict:
        """리다이렉트 홉을 실제 요청으로 계산합니다."""
        try:
            import urllib.parse as _urlparse
            max_hops = 10
            hops = 0
            visited = set()
            chain = [self.url]
            current_url = self.url

            async with aiohttp.ClientSession() as session:
                while hops < max_hops:
                    try:
                        # 우선 HEAD 요청, 405이면 GET으로 재시도
                        resp = await session.head(current_url, headers=self.headers, timeout=10, allow_redirects=False)
                        if resp.status == 405:
                            resp = await session.get(current_url, headers=self.headers, timeout=10, allow_redirects=False)
                    except Exception:
                        # 네트워크 오류 시 실패 처리
                        return {
                            "status": "fail",
                            "message": f"요청 실패로 리다이렉트 확인 불가: {current_url}",
                            "score": 0
                        }

                    if resp.status in [301, 302, 303, 307, 308]:
                        location = resp.headers.get('Location', '')
                        if not location:
                            break
                        next_url = _urlparse.urljoin(current_url, location)
                        # 순환 방지
                        if next_url in visited:
                            chain.append(next_url)
                            hops += 1
                            break
                        visited.add(current_url)
                        chain.append(next_url)
                        current_url = next_url
                        hops += 1
                        continue
                    # 리다이렉트 아님 → 종료
                    break

            # 판정
            if hops <= 1:
                return {
                    "status": "pass",
                    "message": f"리다이렉트 홉 {hops}회로 적절합니다. 최종 도착: {current_url}",
                    "score": 100
                }
            elif hops == 2:
                return {
                    "status": "warning",
                    "message": f"리다이렉트 홉이 2회입니다. 체인: {' → '.join(chain[:4])}{' → …' if len(chain) > 4 else ''}",
                    "score": 60
                }
            else:
                return {
                    "status": "fail",
                    "message": f"리다이렉트 홉이 {hops}회로 과도합니다. 체인: {' → '.join(chain[:6])}{' → …' if len(chain) > 6 else ''}",
                    "score": 20
                }
        except Exception as e:
            return {
                "status": "fail",
                "message": f"리다이렉트 홉 분석 오류: {str(e)}",
                "score": 0
            }
    
    async def _check_content_hash_duplicates(self) -> Dict:
        """콘텐츠 해시 중복을 확인합니다."""
        try:
            if not self.collected_contents:
                # 현재 페이지 콘텐츠로 기본 분석 수행
                current_content = self.soup.get_text() if self.soup else ""
                if current_content:
                    # 현재 페이지 콘텐츠만으로 기본 분석
                    content_hash = hashlib.sha256(current_content.encode('utf-8')).hexdigest()
                    return {
                        "status": "pass",
                        "message": "현재 페이지 콘텐츠만으로 분석. 추가 페이지 수집이 필요하지만 기본적으로 중복 없음.",
                        "score": 70
                    }
                else:
                    return {
                        "status": "pending",
                        "message": "콘텐츠 수집이 필요합니다. 사이트맵에서 URL을 수집하여 분석을 완료하세요.",
                        "score": 0
                    }
            
            # 콘텐츠 해시 계산
            content_hashes = []
            for content in self.collected_contents:
                hash_obj = hashlib.sha256(content.encode('utf-8'))
                content_hashes.append(hash_obj.hexdigest())
            
            # 중복 해시 확인
            hash_counter = Counter(content_hashes)
            duplicate_hashes = [h for h, count in hash_counter.items() if count > 1]
            
            duplicate_ratio = len(duplicate_hashes) / len(content_hashes) if content_hashes else 0
            
            if duplicate_ratio <= 0.1:  # 10% 이하
                return {
                    "status": "pass",
                    "message": f"콘텐츠 중복률이 {duplicate_ratio:.1%}로 적절합니다.",
                    "score": 100
                }
            else:
                return {
                    "status": "fail",
                    "message": f"콘텐츠 중복률이 {duplicate_ratio:.1%}로 높습니다.",
                    "score": int(50 * (1 - duplicate_ratio))
                }
        except Exception as e:
            return {
                "status": "fail",
                "message": f"콘텐츠 해시 분석 오류: {str(e)}",
                "score": 0
            }
    
    async def _check_title_duplicates(self) -> Dict:
        """제목 중복을 확인합니다."""
        try:
            if not self.collected_titles:
                # 현재 페이지 제목으로 기본 분석 수행
                current_title = self.soup.find('title')
                if current_title and current_title.get_text().strip():
                    return {
                        "status": "pass",
                        "message": "현재 페이지 제목만으로 분석. 추가 페이지 수집이 필요하지만 기본적으로 중복 없음.",
                        "score": 70
                    }
                else:
                    return {
                        "status": "pending",
                        "message": "제목 수집이 필요합니다. 사이트맵에서 URL을 수집하여 분석을 완료하세요.",
                        "score": 0
                    }
            
            # 제목 중복 확인
            title_counter = Counter(self.collected_titles)
            duplicate_titles = [title for title, count in title_counter.items() if count > 1]
            
            duplicate_ratio = len(duplicate_titles) / len(self.collected_titles) if self.collected_titles else 0
            
            if duplicate_ratio <= 0.05:  # 5% 이하
                return {
                    "status": "pass",
                    "message": f"제목 중복률이 {duplicate_ratio:.1%}로 적절합니다.",
                    "score": 100
                }
            else:
                return {
                    "status": "fail",
                    "message": f"제목 중복률이 {duplicate_ratio:.1%}로 높습니다.",
                    "score": int(50 * (1 - duplicate_ratio))
                }
        except Exception as e:
            return {
                "status": "fail",
                "message": f"제목 중복 분석 오류: {str(e)}",
                "score": 0
            }
    
    async def _check_description_duplicates(self) -> Dict:
        """메타 설명 중복을 확인합니다."""
        try:
            if not self.collected_descriptions:
                # 현재 페이지 메타 설명으로 기본 분석 수행
                current_desc = self.soup.find('meta', attrs={'name': 'description'})
                if current_desc and current_desc.get('content', '').strip():
                    return {
                        "status": "pass",
                        "message": "현재 페이지 메타 설명만으로 분석. 추가 페이지 수집이 필요하지만 기본적으로 중복 없음.",
                        "score": 70
                    }
                else:
                    return {
                        "status": "pending",
                        "message": "메타 설명 수집이 필요합니다. 사이트맵에서 URL을 수집하여 분석을 완료하세요.",
                        "score": 0
                    }
            
            # 메타 설명 중복 확인
            desc_counter = Counter(self.collected_descriptions)
            duplicate_descriptions = [desc for desc, count in desc_counter.items() if count > 1]
            
            duplicate_ratio = len(duplicate_descriptions) / len(self.collected_descriptions) if self.collected_descriptions else 0
            
            if duplicate_ratio <= 0.05:  # 5% 이하
                return {
                    "status": "pass",
                    "message": f"메타 설명 중복률이 {duplicate_ratio:.1%}로 적절합니다.",
                    "score": 100
                }
            else:
                return {
                    "status": "fail",
                    "message": f"메타 설명 중복률이 {duplicate_ratio:.1%}로 높습니다.",
                    "score": int(50 * (1 - duplicate_ratio))
                }
        except Exception as e:
            return {
                "status": "fail",
                "message": f"메타 설명 중복 분석 오류: {str(e)}",
                "score": 0
            }
    
    async def _check_near_duplicate_content(self) -> Dict:
        """외부 의존성 없이 자카드 유사도로 근사 중복 콘텐츠를 확인합니다."""
        try:
            if len(self.collected_contents) < 2:
                return {
                    "status": "warning",
                    "message": "콘텐츠가 충분하지 않습니다.",
                    "score": 50
                }

            def tokenize(text: str) -> set:
                tokens = re.findall(r"[A-Za-z가-힣0-9]+", text.lower())
                return set(t for t in tokens if len(t) >= 3)

            pairs = 0
            high_sim_pairs = 0

            token_sets = [tokenize(content) for content in self.collected_contents]

            for i in range(len(token_sets)):
                set_i = token_sets[i]
                for j in range(i + 1, len(token_sets)):
                    set_j = token_sets[j]
                    union_size = len(set_i | set_j)
                    if union_size == 0:
                        continue
                    sim = len(set_i & set_j) / union_size
                    pairs += 1
                    if sim > 0.9:
                        high_sim_pairs += 1

            duplicate_ratio = (high_sim_pairs / pairs) if pairs > 0 else 0

            if duplicate_ratio <= 0.1:  # 10% 이하
                return {
                    "status": "pass",
                    "message": f"근사 중복 콘텐츠 비율이 {duplicate_ratio:.1%}로 적절합니다.",
                    "score": 100
                }
            else:
                return {
                    "status": "fail",
                    "message": f"근사 중복 콘텐츠 비율이 {duplicate_ratio:.1%}로 높습니다.",
                    "score": int(50 * (1 - duplicate_ratio))
                }
        except Exception as e:
            return {
                "status": "fail",
                "message": f"근사 중복 콘텐츠 분석 오류: {str(e)}",
                "score": 0
            }
    
    async def _check_thin_content(self) -> Dict:
        """얇은 콘텐츠를 확인합니다."""
        try:
            if not self.collected_contents:
                return {
                    "status": "warning",
                    "message": "콘텐츠 수집이 필요합니다.",
                    "score": 50
                }
            
            # 단어 수 계산
            word_counts = []
            for content in self.collected_contents:
                words = content.split()
                word_counts.append(len(words))
            
            # 하위 20% 기준
            word_counts.sort()
            threshold_index = int(len(word_counts) * 0.2)
            threshold = word_counts[threshold_index] if threshold_index < len(word_counts) else 0
            
            thin_content_count = sum(1 for count in word_counts if count <= threshold)
            thin_content_ratio = thin_content_count / len(word_counts) if word_counts else 0
            
            if thin_content_ratio <= 0.15:  # 15% 이하
                return {
                    "status": "pass",
                    "message": f"얇은 콘텐츠 비율이 {thin_content_ratio:.1%}로 적절합니다.",
                    "score": 100
                }
            else:
                return {
                    "status": "fail",
                    "message": f"얇은 콘텐츠 비율이 {thin_content_ratio:.1%}로 높습니다.",
                    "score": int(50 * (1 - thin_content_ratio))
                }
        except Exception as e:
            return {
                "status": "fail",
                "message": f"얇은 콘텐츠 분석 오류: {str(e)}",
                "score": 0
            }
    
    async def _check_facet_parameters(self) -> Dict:
        """Facet 파라미터 noindex를 확인합니다."""
        try:
            # robots 태그 확인
            robots_tag = self.soup.find('meta', attrs={'name': 'robots'})
            if robots_tag:
                content = robots_tag.get('content', '').lower()
                if 'noindex' in content:
                    return {
                        "status": "pass",
                        "message": "Facet 페이지에 noindex가 설정되었습니다.",
                        "score": 100
                    }
            
            return {
                "status": "warning",
                "message": "Facet 파라미터 noindex 설정을 확인하세요.",
                "score": 50
            }
        except Exception as e:
            return {
                "status": "fail",
                "message": f"Facet 파라미터 분석 오류: {str(e)}",
                "score": 0
            }
    

    
    async def _check_syndication_canonical(self) -> Dict:
        """Syndication cross-domain canonical을 확인합니다."""
        try:
            canonical_tag = self.soup.find('link', rel=lambda x: x and 'canonical' in x.lower())
            if canonical_tag and canonical_tag.get('href'):
                canonical_url = canonical_tag['href']
                canonical_domain = urllib.parse.urlparse(canonical_url).netloc
                
                if canonical_domain != self.domain:
                    return {
                        "status": "pass",
                        "message": "Syndication canonical이 설정되었습니다.",
                        "score": 100
                    }
            
            return {
                "status": "warning",
                "message": "Syndication canonical 설정을 확인하세요.",
                "score": 50
            }
        except Exception as e:
            return {
                "status": "fail",
                "message": f"Syndication canonical 분석 오류: {str(e)}",
                "score": 0
            }
    
    async def _check_http_to_https_redirect(self, http_url: str) -> str:
        """HTTP에서 HTTPS로의 리디렉션을 확인합니다."""
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(http_url, headers=self.headers, timeout=10, allow_redirects=False) as response:
                    if response.status in [301, 302, 307, 308]:
                        location = response.headers.get('Location', '')
                        if location.startswith('https://'):
                            return "HTTP에서 HTTPS로 자동 리디렉션됩니다."
                        else:
                            return "리디렉션되지만 HTTPS가 아닙니다."
                    elif response.status == 200:
                        return "HTTP에서 HTTPS로 리디렉션되지 않습니다."
                    else:
                        return f"HTTP 접속 불가 (상태 코드: {response.status})"
        except Exception as e:
            return f"리디렉션 확인 중 오류: {str(e)}"

    async def _check_session_id_removal(self) -> Dict:
        """Session ID 제거를 확인합니다."""
        try:
            parsed = urllib.parse.urlparse(self.url)
            query_params = urllib.parse.parse_qs(parsed.query)
            
            # 세션 관련 파라미터 확인
            session_params = ['session', 'sid', 'sessionid', 'jsessionid', 'phpsessid']
            found_session_params = [param for param in query_params.keys() if any(session in param.lower() for session in session_params)]
            
            if not found_session_params:
                return {
                    "status": "pass",
                    "message": "Session ID가 URL에 없습니다.",
                    "score": 100
                }
            else:
                return {
                    "status": "fail",
                    "message": f"Session ID 파라미터 발견: {', '.join(found_session_params)}",
                    "score": 30
                }
        except Exception as e:
            return {
                "status": "fail",
                "message": f"Session ID 분석 오류: {str(e)}",
                "score": 0
            }
    
    def _normalize_url(self, url: str) -> str:
        """URL을 정규화합니다."""
        try:
            parsed = urllib.parse.urlparse(url)
            # 스키마, 도메인, 경로만 유지
            normalized = f"{parsed.scheme}://{parsed.netloc}{parsed.path}"
            return normalized.lower()
        except:
            return url.lower() 