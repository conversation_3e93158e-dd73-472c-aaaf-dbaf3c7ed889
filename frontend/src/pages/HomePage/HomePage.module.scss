@import "../../styles/mixin";

.home-page {
  min-height: calc(100vh - 60px);
  display: flex;
  flex-direction: column;
}

.hero {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 4rem 2rem;
  text-align: center;

  @include mobile {
    padding: 3rem 1.5rem;
  }
}

.hero-content {
  max-width: 800px;
  width: 100%;
  margin: 0 auto;
  text-align: center;
}

.title {
  font-size: 3.5rem;
  font-weight: 800;
  margin-bottom: 1.5rem;
  background: linear-gradient(135deg, #333 0%, #666 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  line-height: 1.2;

  @include mobile {
    font-size: 2rem;
  }
}

.subtitle {
  font-size: 1.3rem;
  color: rgba(0, 0, 0, 0.7);
  margin-bottom: 3rem;
  line-height: 1.6;

  @include mobile {
    font-size: 1rem;
    margin-bottom: 2rem;
  }

  br {
    @include mobile {
      display: none;
    }
  }
}

.error {
  margin-top: 1rem;
  padding: 1rem;
  background: rgba(220, 53, 69, 0.1);
  border: 1px solid rgba(220, 53, 69, 0.3);
  border-radius: 8px;
  color: #dc3545;

  p {
    margin: 0;
    font-weight: 500;
  }
}

.features {
  background: rgba(0, 0, 0, 0.02);
  border-top: 1px solid rgba(0, 0, 0, 0.1);
  padding: 4rem 2rem;
  @include mobile {
    padding: 3rem 1.5rem;
  }
}

.features-container {
  max-width: 1200px;
  margin: 0 auto;
}

.features-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: #333;
  margin-bottom: 3rem;
  text-align: center;
  @include mobile {
    font-size: 1.3rem;
    margin-bottom: 1.5rem;
  }
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 2rem;
  @include mobile {
    gap: 1rem;
  }
}

.feature-card {
  background: rgba(255, 255, 255, 0.8);
  padding: 2rem;
  border-radius: 16px;
  border: 1px solid rgba(0, 0, 0, 0.1);
  text-align: center;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  h3 {
    font-size: 1.3rem;
    font-weight: 600;
    color: #333;
    margin: 1rem 0;
    @include mobile {
      font-size: 1.05rem;
      margin: 0.7rem 0;
    }
  }
  p {
    color: rgba(0, 0, 0, 0.7);
    line-height: 1.6;
    margin: 0;
    font-size: 1rem;
    @include mobile {
      font-size: 0.92rem;
    }
  }
  @include mobile {
    padding: 1.5rem;
  }
}

.feature-icon {
  color: #667eea;
  width: 2.5rem;
  height: 2.5rem;
  margin-bottom: 0.7rem;
  @include mobile {
    width: 2rem;
    height: 2rem;
    margin-bottom: 0.5rem;
  }
}
