#!/bin/bash

# SSL 인증서 설정 스크립트
# 사용법: ./setup-ssl.sh your-domain.com

DOMAIN=$1

if [ -z "$DOMAIN" ]; then
    echo "사용법: $0 <도메인명>"
    echo "예시: $0 example.com"
    exit 1
fi

echo "=== SSL 인증서 설정을 시작합니다 ==="
echo "도메인: $DOMAIN"

# 1. nginx.conf에서 도메인 업데이트
echo "1. nginx.conf 도메인 업데이트..."
sed -i "s/your-domain.com/$DOMAIN/g" nginx.conf

# 2. 필요한 디렉토리 생성
echo "2. 필요한 디렉토리 생성..."
sudo mkdir -p /var/www/certbot

# 3. Docker 컨테이너 중단
echo "3. Docker 컨테이너 중단..."
docker-compose down

# 4. 임시 nginx 설정 파일 생성 (인증서 발급용)
echo "4. 임시 nginx 설정 생성..."
cat > temp-nginx.conf << EOF
upstream backend {
    server host.docker.internal:8000;
}

server {
    listen 80;
    server_name $DOMAIN;

    # Let's Encrypt 인증을 위한 경로
    location /.well-known/acme-challenge/ {
        root /var/www/certbot;
        try_files \$uri \$uri/ =404;
    }

    # 나머지 요청은 일시적으로 허용
    location / {
        return 200 'SSL 인증서 발급 중입니다...';
        add_header Content-Type text/plain;
    }
}
EOF

# 5. 임시 HTTP 서버 시작
echo "5. 임시 HTTP 서버 시작..."
docker run --rm -d \
  --name temp-nginx \
  -p 80:80 \
  -v $(pwd)/temp-nginx.conf:/etc/nginx/conf.d/default.conf \
  -v /var/www/certbot:/var/www/certbot \
  --add-host=host.docker.internal:host-gateway \
  nginx:alpine

# 컨테이너가 시작될 때까지 잠시 대기
sleep 5

# 6. SSL 인증서 발급
echo "6. SSL 인증서 발급 중..."
sudo certbot certonly \
  --webroot \
  --webroot-path=/var/www/certbot \
  --email admin@$DOMAIN \
  --agree-tos \
  --no-eff-email \
  -d $DOMAIN

# 7. 임시 nginx 컨테이너 중단 및 정리
echo "7. 임시 nginx 컨테이너 중단..."
docker stop temp-nginx
rm -f temp-nginx.conf

# 7. 인증서 발급 확인
if [ -f "/etc/letsencrypt/live/$DOMAIN/fullchain.pem" ]; then
    echo "✅ SSL 인증서가 성공적으로 발급되었습니다!"
    
    # 8. Docker Compose로 서비스 재시작
    echo "8. Docker Compose 서비스 재시작..."
    docker-compose up -d
    
    echo "✅ HTTPS 설정이 완료되었습니다!"
    echo "🌐 https://$DOMAIN 으로 접속해보세요."
    
    # 9. 자동 갱신 설정
    echo "9. SSL 인증서 자동 갱신 설정..."
    (crontab -l 2>/dev/null; echo "0 12 * * * /usr/bin/certbot renew --quiet && docker-compose restart nginx") | crontab -
    
    echo "✅ SSL 인증서 자동 갱신이 설정되었습니다."
    
else
    echo "❌ SSL 인증서 발급에 실패했습니다."
    echo "도메인이 올바르게 설정되었는지 확인해주세요."
    exit 1
fi

echo "=== SSL 설정이 완료되었습니다! ==="
