upstream backend {
    server seo-backend:8000;  
}

server {
    listen 80;
    server_name seoapi.pxd.co.kr;  # 실제 도메인으로 변경

    # Let's Encrypt 인증을 위한 경로
    location /.well-known/acme-challenge/ {
        root /var/www/certbot;
    }

    # 모든 HTTP 요청을 HTTPS로 리다이렉트
    location / {
        return 301 https://$server_name$request_uri;
    }
}

# HTTPS 서버 설정 (SSL 인증서 발급 후 활성화)
server {
    listen 443 ssl http2;
    server_name seoapi.pxd.co.kr;  # 실제 도메인으로 변경

    # SSL 인증서 경로 (certbot이 자동으로 설정)
    ssl_certificate /etc/letsencrypt/live/seoapi.pxd.co.kr/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/seoapi.pxd.co.kr/privkey.pem;

    # SSL 설정
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-RSA-AES128-SHA256:ECDHE-RSA-AES256-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;

    location / {
        proxy_pass http://backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        # CORS 설정
        add_header Access-Control-Allow-Origin * always;
        add_header Access-Control-Allow-Methods "GET, POST, OPTIONS" always;
        add_header Access-Control-Allow-Headers "DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization" always;
        add_header Access-Control-Max-Age 86400 always;
    }

    # 정적 파일 설정 (필요시)
    location /static/ {
        alias /home/<USER>/seo/static/;
    }
}