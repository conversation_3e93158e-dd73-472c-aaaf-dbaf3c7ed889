import axios from "axios";
import { SEOAnalysisResult, AnalysisRequest } from "../types";
import API_CONFIG from "../config/api";

const api = axios.create({
  baseURL: API_CONFIG.BASE_URL,
  timeout: 300000, // 5분 타임아웃으로 증가
  headers: API_CONFIG.HEADERS,
});

export const analyzeSEO = async (
  request: AnalysisRequest,
  onProgress?: (progress: number) => void
): Promise<SEOAnalysisResult & { _analysisId?: string }> => {
  try {
    console.log("🔍 SEO 분석 요청 시작:", request);

    // 진행 상황 시뮬레이션 (계속 증가하는 방향으로만)
    let currentProgress = 0;
    const progressInterval = setInterval(() => {
      if (onProgress) {
        // 점진적으로 증가 (0%에서 95%까지)
        currentProgress = Math.min(95, currentProgress + Math.random() * 5 + 2); // 2-7%씩 증가
        onProgress(currentProgress);
      }
    }, 1500); // 1.5초마다 업데이트

    const response = await api.post<SEOAnalysisResult>(
      API_CONFIG.ENDPOINTS.ANALYZE,
      request
    );

    clearInterval(progressInterval);

    // 분석 완료 시 100%로 설정
    if (onProgress) {
      onProgress(100);
    }

    console.log("✅ SEO 분석 성공:", response.data);
    const analysisId =
      (response.headers as any)["x-analysis-id"] ||
      (response as any).headers?.["x-analysis-id"];

    // 분석 완료 후 자동으로 프로세스 정리 (백그라운드에서 실행)
    cleanupProcesses().catch((error) => {
      console.warn("⚠️ 자동 프로세스 정리 실패 (무시됨):", error);
    });

    return Object.assign({}, response.data, { _analysisId: analysisId });
  } catch (error: any) {
    console.error("❌ SEO 분석 오류:", error);
    // 에러 발생 시에도 프로세스 정리 (백그라운드에서 실행)
    cleanupProcesses().catch((cleanupError) => {
      console.warn("⚠️ 에러 후 프로세스 정리 실패 (무시됨):", cleanupError);
    });
    // 타임아웃 오류 특별 처리
    if (error.code === "ECONNABORTED" || error.message.includes("timeout")) {
      throw new Error(
        "분석 시간이 초과되었습니다. 서버가 혼잡하거나 분석할 데이터가 많을 수 있습니다. 잠시 후 다시 시도해주세요."
      );
    }

    if (error.response) {
      console.error("📊 응답 상태:", error.response.status);
      console.error("📊 응답 헤더:", error.response.headers);
      console.error("📊 응답 데이터:", error.response.data);
      const message = error.response?.data?.detail || error.message;
      throw new Error(`SEO 분석 실패: ${message}`);
    }

    // 네트워크 오류 처리
    if (
      error.code === "NETWORK_ERROR" ||
      error.message.includes("Network Error")
    ) {
      throw new Error(
        "네트워크 연결에 문제가 있습니다. 인터넷 연결을 확인하고 다시 시도해주세요."
      );
    }

    throw new Error(`분석 중 오류가 발생했습니다: ${error.message}`);
  }
};

export const saveResult = async (
  result: SEOAnalysisResult,
  options?: {
    isPublic?: boolean;
    ttlDays?: number;
    userEmail?: string;
    userDisplayName?: string;
  }
): Promise<{ id: string; share_url: string }> => {
  const response = await api.post<{ id: string; share_url: string }>(
    API_CONFIG.ENDPOINTS.SAVE_RESULT,
    {
      result,
      is_public: options?.isPublic ?? true,
      ttl_days: options?.ttlDays ?? 7,
      user_email: options?.userEmail,
      user_display_name: options?.userDisplayName,
    }
  );
  return response.data;
};

export interface ResultListItem {
  id: string;
  url: string;
  overall_score: number;
  created_at: string;
}

export const fetchResults = async (
  page = 1,
  pageSize = 20
): Promise<{
  items: ResultListItem[];
  total: number;
  page: number;
  page_size: number;
}> => {
  const res = await api.get<{
    items: ResultListItem[];
    total: number;
    page: number;
    page_size: number;
  }>(`/results`, { params: { page, page_size: pageSize } });
  return res.data;
};

// (삭제됨) E-E-A-T 관련 API는 더 이상 제공하지 않습니다.

export const healthCheck = async (): Promise<boolean> => {
  try {
    const response = await api.get(API_CONFIG.ENDPOINTS.HEALTH);
    return response.status === 200;
  } catch (error) {
    return false;
  }
};

// 자동 프로세스 정리 API (백그라운드에서 호출됨)
export const cleanupProcesses = async (): Promise<{
  status: string;
  message: string;
}> => {
  try {
    console.log("🧹 자동 프로세스 정리 요청");
    const response = await api.post<{ status: string; message: string }>(
      "/emergency-cleanup"
    );
    console.log("✅ 자동 프로세스 정리 완료:", response.data);
    return response.data;
  } catch (error: any) {
    console.error("❌ 자동 프로세스 정리 오류:", error);
    // 정리 실패는 치명적이지 않으므로 기본 응답 반환
    return {
      status: "error",
      message: `자동 프로세스 정리 중 오류: ${error.message}`,
    };
  }
};

// 응급상황용 수동 정리 API (개발자용)
export const emergencyCleanup = async (): Promise<{
  status: string;
  message: string;
}> => {
  try {
    console.log("🚨 응급 프로세스 정리 요청");
    const response = await api.post<{ status: string; message: string }>(
      "/emergency-cleanup"
    );
    console.log("✅ 응급 프로세스 정리 완료:", response.data);
    return response.data;
  } catch (error: any) {
    console.error("❌ 응급 프로세스 정리 오류:", error);
    throw new Error(`응급 프로세스 정리 실패: ${error.message}`);
  }
};
